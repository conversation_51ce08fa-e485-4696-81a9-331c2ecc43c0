<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Fields\FileField;
use App\Services\Form\Components\Fields\TextareaField;
use App\Services\Form\Components\Fields\TextField;
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\FieldLayoutItem;
use App\Services\Form\Components\LayoutItems\GroupLayoutItem;
use App\Services\Form\Components\LayoutItems\TemplateLayoutItem;
use App\Services\Form\Components\Layouts\Grid\InputScreenLargeLayout;
use App\Services\Form\Components\Layouts\Grid\OutputBidDocumentLayout;
use App\Services\Form\Components\Layouts\Grid\OutputJobDocumentLayout;
use App\Services\Form\Components\Overrides\TemplateOverride;
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\FieldSideValue;
use App\Services\Form\Components\RuleConditionSideValues\NumericSideValue;
use App\Services\Form\Components\RuleEvents\CalculateRuleEvent;
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;
use Common\Classes\FormHelpers;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            TextField::make($group, 'length')->setLabel('Length (lf)')->setIsRequired()->setDisplayMaskDecimal(2);
            TextField::make($group, 'width')->setLabel('Width (lf)')->setIsRequired()->setDisplayMaskDecimal(2);
            TextField::make($group, 'total')->setLabel('Total Square Feet')->setDisplayDisabled();
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize(10485760);
        })
        ->withGroups(function (DefaultGroup $group, BidStructure $structure) {
            FormHelpers::productQuantityGroup($structure, $group, [
                'table-alias' => 'product-table',
                'product_category' => [
                    'label' => 'Encapsulation Product Category',
                    'description' => 'Choose or create the encapsulation product category. The category will be used to attach and organize your products within the form dropdown.',
                    'default_name' => 'Encapsulation',
                    'create_instruction' => 'Create the Encapsulation product category needed for the form.'
                ]
            ]);
        })
        ->withTemplates(function (DefaultGroup $group) {
            ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                $override->setLabel('Encapsulation Service Description')
                    ->setDescription('Add an explanation of your encapsulation installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.');
            });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'length')->setSize(4);
                FieldLayoutItem::make($layout, 'width')->setSize(4);
                FieldLayoutItem::make($layout, 'total')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(8);
                FieldLayoutItem::make($layout, 'photos')->setSize(4);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                TemplateLayoutItem::make($layout, 'description')->setSize(12);
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'length')->setSize(4);
                FieldLayoutItem::make($layout, 'width')->setSize(4);
                FieldLayoutItem::make($layout, 'total')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'length')->setSize(4);
                FieldLayoutItem::make($layout, 'width')->setSize(4);
                FieldLayoutItem::make($layout, 'total')->setSize(4);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
        })
        ->withRules(function (DefaultGroup $group) {
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('width'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('width'))->greaterThan(NumericSideValue::make(0));
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule, 'calculate-square-feet')->setExpression('{{field:length|FLT}}*{{field:width|FLT}}')->setResultField('total');
                });
        });
});

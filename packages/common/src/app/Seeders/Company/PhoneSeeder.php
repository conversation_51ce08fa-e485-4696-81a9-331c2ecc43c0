<?php

declare(strict_types=1);

namespace Common\Seeders\Company;

use App\Classes\Acl;
use App\Resources\Company\PhoneResource;
use Common\Classes\Seeder;
use Common\Traits\Seeder\CompanyTrait;
use Core\Components\Resource\Classes\Entity;

/**
 * Class PhoneSeeder
 *
 * @package Common\Seeders\Company
 */
class PhoneSeeder extends Seeder
{
    use CompanyTrait;

    /**
     * @var bool
     */
    protected bool $primary = false;

    /**
     * Set if phone is primary
     *
     * @param bool $primary
     * @return PhoneSeeder
     */
    public function primary(bool $primary): self
    {
        $this->primary = $primary;
        return $this;
    }

    /**
     * Determines if phone is primary
     *
     * @return bool
     */
    public function isPrimary(): bool
    {
        return $this->primary;
    }

    /**
     * Get nested entity without any identifiers
     *
     * @return array
     */
    public function nestedEntity(): array
    {
        $faker = $this->getFaker();

        return [
            'number' => $faker->fxPhoneNumber,
            'description' => $faker->randomElement(['Home', 'Cell']),
            'is_primary' => $this->isPrimary()
        ];
    }

    /**
     * Run seeder
     *
     * @throws \Core\Exceptions\AppException
     */
    public function run(): void
    {
        $entity = $this->nestedEntity();
        $entity['company_id'] = $this->getCompanyID();

        $id = PhoneResource::make(Acl::make())->create(Entity::make($entity))->run();

        $this->primaryKey($id);
    }
}

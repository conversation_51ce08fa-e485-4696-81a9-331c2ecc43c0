<?php

namespace Common\Models\Common;

use Common\Models\BidItem;
use Common\Models\Company;
use Common\Traits\DB\UuidTrait;

trait BidItemMediaCommon
{
    use UuidTrait;

    protected $castMap = [
        'type' => 'int',
        'order' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function bidItem()
    {
        return $this->belongsTo(BidItem::class, 'bidItemID', 'bidItemID');
    }

    public function item()
    {
        return $this->morphTo('item', 'type', 'itemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('bidItems', 'bidItems.bidItemID', '=', "{$this->table}.bidItemID")
            ->join('project', 'project.projectID', '=', 'bidItems.projectID')
            ->join('customer', 'customer.customerID', '=', 'project.customerID');
        return $query->where('customer.companyID', $company);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.createdAt", 'asc');
    }
}

<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\File;
use Common\Models\Interfaces\MediaInterface;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait MediaCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'companyID' => 'int',
        'status' => 'int',
        'isBidMedia' => 'bool',
        'isBidDefault' => 'bool',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $searchColumns = ['name', 'description'];

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function file()
    {
        return $this->belongsTo(File::class, 'fileID', 'fileID');
    }

    public function scopeActive($query)
    {
        return $query->where("{$this->table}.status", MediaInterface::STATUS_ACTIVE);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.name", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where('media.companyID', $company);
    }
}

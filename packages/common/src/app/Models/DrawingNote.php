<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrawingNote extends Model
{
    use SoftDeletes;

    protected $table = 'drawingNotes';
    protected $primaryKey = 'ID';
    protected $fillable = [
        'drawingID',
        'nodeID',
        'xPos',
        'yPos',
        'zRotation',
        'text',
        'fontSize',
        'color'
    ];
    protected $hidden = ['drawingID'];

    public function appDrawing()
    {
        return $this->belongsTo('AppDrawing', 'drawingID', 'drawingID');
    }
}

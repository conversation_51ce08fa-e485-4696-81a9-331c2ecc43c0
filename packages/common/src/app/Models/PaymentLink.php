<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentLink extends Model
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'paymentLinks';
    protected $primaryKey = 'paymentLinkID';
    
    protected $fillable = [
        'paymentLinkID',
        'propelrMerchantID',
        'shortToken',
        'createdByUserID',
        'deletedAt',
        'deletedByUserID'
    ];

    protected $dates = [
        'createdAt',
        'updatedAt',
        'deletedAt',
    ];

    public function merchant()
    {
        return $this->belongsTo(PropelrMerchant::class, 'propelrMerchantID', 'propelrMerchantID');
    }

    /**
     * Scope by company through merchant relationship
     */
    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->companyUUID;
        }

        $query->join('propelrMerchants', 'propelrMerchants.propelrMerchantID', '=', "{$this->table}.propelrMerchantID");
        return $query->where("propelrMerchants.companyUUID", $company);
    }
}
<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyFormItemDependency extends Model
{
    use SoftDeletes;
    use UuidTrait;

    public const TYPE_PRODUCT_ITEM = 13;

    protected $table = 'companyFormItemDependencies';
    protected $primaryKey = 'companyFormItemDependencyID';
    protected $fillable = [
        'companyFormItemDependencyID', 'companyFormItemID', 'formItemDependencyID', 'type', 'itemID', 'createdByUserID',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'type' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function form()
    {
        return $this->belongsTo(CompanyFormItem::class, 'companyFormItemID', 'companyFormItemID');
    }

    public function formItemDependency()
    {
        return $this->belongsTo(FormItemDependency::class, 'formItemDependencyID', 'formItemDependencyID');
    }

    public function item()
    {
        return $this->morphTo('item', 'type', 'itemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyFormItems', 'companyFormItems.companyFormItemID', "{$this->table}.companyFormItemID");
        return $query->where('companyFormItems.companyID', $company);
    }
}

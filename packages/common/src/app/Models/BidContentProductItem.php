<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Models\Common\BidContentProductItemCommon;
use Common\Models\Interfaces\BidContentProductItemInterface;

class BidContentProductItem extends HistoryEntityModel implements BidContentProductItemInterface
{
    use BidContentProductItemCommon;

    protected $table = 'bidContentProductItems';
    protected $primaryKey = 'bidContentProductItemID';
    protected $fillable = [
        'bidContentProductItemID', 'bidContentID', 'productItemID', 'createdAt', 'createdByUserID', 'updatedAt',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}

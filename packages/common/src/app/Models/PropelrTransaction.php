<?php
declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

class PropelrTransaction extends Model
{
    use UuidTrait;
    use ScopeSearchTrait;


    public const STATUS_CAPTURED = 1;              // Sale succeeded, in open batch
    public const STATUS_PENDING_SETTLEMENT = 2;    // Submitted to processor
    public const STATUS_UNDER_REVIEW = 3;          // Amount threshold review
    public const STATUS_SETTLED = 4;               // Accepted for funding
    public const STATUS_FUNDED = 5;                // Deposit posted
    public const STATUS_VOIDED = 6;                // Canceled before settlement
    public const STATUS_REFUNDED = 7;              // Returned post-settlement
    public const STATUS_AUTH_DECLINED = 8;         // Issuer decline
    public const STATUS_RETRYABLE = 9;             // Temporary issue
    public const STATUS_SETTLEMENT_REJECTED = 10;  // Rejected for funding
    public const STATUS_SETTLEMENT_ERROR = 11;     // Format/token error
    public const STATUS_ZERO_AMOUNT = 12;          // $0 auth verification
    public const TRANSACTION_TYPE_CREDIT_CARD = 1;
    public const TRANSACTION_TYPE_ACH = 2;

    public const ACH_ACCOUNT_TYPE_CHECKING = 1;
    public const ACH_ACCOUNT_TYPE_SAVINGS = 2;

    protected $table = 'propelrTransactions';
    protected $primaryKey = 'propelrTransactionID';

    protected $fillable = [
        'propelrTransactionID',
        'propelrMerchantID',
        'retref',
        'orderID',
        'invoiceNumber',
        'type',
        'status',
        'achAccountType',
        'baseAmount',
        'creditCardProcessingFee',
        'totalAmount',
        'settledAmount',
        'currency',
        'customerFirstName',
        'customerLastName',
        'customerEmail',
        'customerPhone',
        'paymentToken',
        'last4',
        'expMonth',
        'expYear',
        'cardBrand',
        'routingNumber',
        'authCode',
        'gatewayResponseCode',
        'gatewayResponseMessage',
        'cardResponseCode',
        'cardResponseMessage',
        'respStat',
        'processorCode',
        'avsResp',
        'cvvResp',
        'entryMode',
        'setlStat',
        'batchID',
        'hostBatch',
        'commCard',
        'binType',
        'voidReference',
        'refundReference',
        'rawGatewayJSON',
        'profileID',
        'fundingID',
        'achReturnCode',
        'metadata',
        'authorizedAt',
        'capturedAt',
        'voidedAt',
        'refundedAt',
        'settledAt',
        'fundedAt',
        'settlementDate',
    ];

    protected $dates = [
        'authorizedAt',
        'capturedAt',
        'voidedAt',
        'refundedAt',
        'settledAt',
        'settlementDate',
        'fundedAt',
        'createdAt',
        'updatedAt',
    ];

    protected $castMap = [
        'baseAmount' => 'float',
        'creditCardProcessingFee' => 'float',
        'totalAmount' => 'float',
        'settledAmount' => 'float',
        'metadata' => 'array',
        'rawGatewayJSON' => 'array',
    ];

    protected $searchColumns = [
        'customerFirstName',
        'customerLastName',
        'customerEmail',
        'customerPhone',
        'batchID',
        'fundingID',
        'invoiceNumber'
    ];

    protected $dateFormat = 'Y-m-d H:i:s.u';

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->status = self::mapRespStatToStatus($model->respStat);
        });
    }


    /**
     * Map the response status from the gateway to PropelrTransaction status.
     * This will be updated by polling later.
     *
     * @param string|null $rs
     * @return int
     */
    protected static function mapRespStatToStatus(?string $rs): int
    {
        return match($rs) {
            'A' => self::STATUS_CAPTURED,
            'B' => self::STATUS_RETRYABLE,
            'C' => self::STATUS_AUTH_DECLINED,
            'Z' => self::STATUS_ZERO_AMOUNT,
            default => self::STATUS_SETTLEMENT_ERROR,
        };
    }


    public function merchant()
    {
        return $this->belongsTo(PropelrMerchant::class, 'propelrMerchantID', 'propelrMerchantID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->companyUUID;
        }

        $query->join('propelrMerchants', 'propelrMerchants.propelrMerchantID', '=', "{$this->table}.propelrMerchantID");
        return $query->where("propelrMerchants.companyUUID", $company);
    }

    public function canVoid(): bool
    {
        return !$this->voidedAt && in_array($this->status, [
            self::STATUS_CAPTURED,
            self::STATUS_PENDING_SETTLEMENT
        ]);
    }

    public function isVoided(): bool
    {
        return !is_null($this->voidedAt);
    }

    public function canRefund(): bool
    {
        return !$this->refundedAt 
            && !$this->voidedAt 
            && in_array($this->status, [
                self::STATUS_SETTLED,
                self::STATUS_FUNDED
            ]);
    }

    public function isRefunded(): bool
    {
        return !is_null($this->refundedAt);
    }

    public function isPreSettlement(): bool
    {
        return in_array($this->status, [
            self::STATUS_CAPTURED,
            self::STATUS_PENDING_SETTLEMENT,
            self::STATUS_UNDER_REVIEW
        ]);
    }

    public function isSettled(): bool
    {
        return $this->status === self::STATUS_SETTLED;
    }

    public function needsPolling(): bool
    {
        return in_array($this->status, [
            self::STATUS_CAPTURED,
            self::STATUS_PENDING_SETTLEMENT,
            self::STATUS_UNDER_REVIEW,
            self::STATUS_RETRYABLE
        ]) && !$this->isVoided() && !$this->isRefunded();
    }

    public function isFunded(): bool
    {
        return $this->status === self::STATUS_FUNDED;
    }

    public function canSettle(): bool
    {
        return in_array($this->status, [
            self::STATUS_CAPTURED,
            self::STATUS_PENDING_SETTLEMENT,
            self::STATUS_UNDER_REVIEW
        ]) && !$this->isVoided() && !$this->isRefunded();
    }

    /**
     * Check if transaction can be funded
     */
    public function canFund(): bool
    {
        return $this->status === self::STATUS_SETTLED && !$this->isFunded() && !$this->isVoided() && !$this->isRefunded();
    }

    /**
     * Get human-readable status name
     */
    public function getStatusName(): string
    {
        return match($this->status) {
            self::STATUS_CAPTURED => 'Captured',
            self::STATUS_PENDING_SETTLEMENT => 'Pending Settlement',
            self::STATUS_UNDER_REVIEW => 'Under Review',
            self::STATUS_SETTLED => 'Settled',
            self::STATUS_FUNDED => 'Funded',
            self::STATUS_VOIDED => 'Voided',
            self::STATUS_REFUNDED => 'Refunded',
            self::STATUS_AUTH_DECLINED => 'Auth Declined',
            self::STATUS_RETRYABLE => 'Retryable',
            self::STATUS_SETTLEMENT_REJECTED => 'Settlement Rejected',
            self::STATUS_SETTLEMENT_ERROR => 'Settlement Error',
            self::STATUS_ZERO_AMOUNT => 'Zero Amount',
            default => 'Unknown',
        };
    }

    public static function getStatusNameByCode(int $status): string
    {
        return match($status) {
            self::STATUS_CAPTURED => 'Captured',
            self::STATUS_PENDING_SETTLEMENT => 'Pending Settlement',
            self::STATUS_UNDER_REVIEW => 'Under Review',
            self::STATUS_SETTLED => 'Settled',
            self::STATUS_FUNDED => 'Funded',
            self::STATUS_VOIDED => 'Voided',
            self::STATUS_REFUNDED => 'Refunded',
            self::STATUS_AUTH_DECLINED => 'Auth Declined',
            self::STATUS_RETRYABLE => 'Retryable',
            self::STATUS_SETTLEMENT_REJECTED => 'Settlement Rejected',
            self::STATUS_SETTLEMENT_ERROR => 'Settlement Error',
            self::STATUS_ZERO_AMOUNT => 'Zero Amount',
            default => 'Unknown',
        };
    }

    /**
     * Scope for transactions needing polling
     */
    public function scopeNeedsPolling($query)
    {
        return $query->whereIn('status', [
                self::STATUS_CAPTURED,
                self::STATUS_PENDING_SETTLEMENT,
                self::STATUS_UNDER_REVIEW,
                self::STATUS_RETRYABLE
            ])
            ->whereNull('settlementDate')
            ->whereNull('voidedAt')
            ->whereNull('refundedAt');
    }

    /**
     * Scope for stuck transactions (pre-settlement > 2 days)
     */
    public function scopeStuckInSettlement($query, $days = 2)
    {
        return $query->whereIn('status', [
                self::STATUS_CAPTURED,
                self::STATUS_PENDING_SETTLEMENT,
                self::STATUS_UNDER_REVIEW,
                self::STATUS_RETRYABLE
            ])
            ->where('createdAt', '<', now()->subDays($days))
            ->whereNull('voidedAt')
            ->whereNull('refundedAt');
    }

}
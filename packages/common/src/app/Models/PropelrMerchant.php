<?php
declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Ramsey\Uuid\Uuid;


class PropelrMerchant extends Model
{
    use SoftDeletes;
    use UuidTrait;

    // Application lifecycle statuses following CoPilot merchant onboarding flow
    public const APPLICATION_STATUS_CREATED = 1;           // Initial merchant creation
    public const APPLICATION_STATUS_PENDING_SIGNATURE = 2; // Signature URL generated
    public const APPLICATION_STATUS_QUALIFY = 3;           // Signed, awaiting underwriting
    public const APPLICATION_STATUS_UNDER = 4;             // Under review
    public const APPLICATION_STATUS_BOARDING = 5;          // Approved, gateway provisioning
    public const APPLICATION_STATUS_BOARDED = 6;           // Gateway provisioned
    public const APPLICATION_STATUS_LIVE = 7;              // Merchant is live (first transaction received)
    public const APPLICATION_STATUS_DECLINED = 8;          // Application declined

    public const GATEWAY_STATUS_NOT_BOARDED = 1;
    public const GATEWAY_STATUS_BOARDED = 2;

    // CoPilot signature status constants
    public const SIGNATURE_STATUS_PENDING = 1;
    public const SIGNATURE_STATUS_SIGNED = 2;

    // Tax Filing Methods
    public const TAX_FILING_METHOD_EIN = 'EIN';
    public const TAX_FILING_METHOD_SSN = 'SSN';

    // Ownership Types
    public const OWNERSHIP_TYPE_GOVT = 'GOVT';
    public const OWNERSHIP_TYPE_INDIVSOLE = 'INDIVSOLE';
    public const OWNERSHIP_TYPE_LLC = 'LLC';
    public const OWNERSHIP_TYPE_NONPRFT = 'NONPRFT';
    public const OWNERSHIP_TYPE_PRTNRSHP = 'PRTNRSHP';
    public const OWNERSHIP_TYPE_PRIVCORP = 'PRIVCORP';
    public const OWNERSHIP_TYPE_PUBCORP = 'PUBCORP';
    public const OWNERSHIP_TYPE_TAXEXMPT = 'TAXEXMPT';

    // Bank Account Types
    public const BANK_ACCOUNT_TYPE_BIZ = 'BIZ';
    public const BANK_ACCOUNT_TYPE_SAVINGS = 'SAVINGS';

    // Ready to Process Status
    public const IS_READY_TO_PROCESS_PENDING = 1;
    public const IS_READY_TO_PROCESS_READY = 2;

    protected $table = 'propelrMerchants';
    protected $primaryKey = 'propelrMerchantID';

    protected $fillable = [
        'propelrMerchantID',
        'companyUUID',
        'isACHAvailable',
        'ccMID',
        'achMID',
        'copilotMerchantID',
        'templateID',
        'status',
        'gatewayStatus',
        'gatewayUsername',
        'gatewayPasswordEnc',
        'lastSyncedAt',
        'approvedAt',
        'declinedAt',
        'boardedAt',
        'createdByUserID',
        'updatedByUserID',
        'deletedByUserID',

        'signatureUrl',
        'signatureStatus',
        'signedAt',
        'submittedAt',
        'lastSignatureErrorCode',
        'lastSignatureErrorField',
        'lastSignatureErrorMessage',
        'isReadyToProcess',
        'isReadyToProcessAt',
    ];

    protected $dates = [
        'lastSyncedAt',
        'signedAt',
        'submittedAt',
        'approvedAt',
        'declinedAt',
        'boardedAt',
        'createdAt',
        'updatedAt',
    ];
    protected $dateFormat = 'Y-m-d H:i:s.u';

    protected $casts = [
        'isACHAvailable' => 'bool',
        'metadata' => 'json',
    ];

    public static function boot()
    {
        parent::boot();

        self::creating(function ($model) {
            $uuid = Uuid::uuid4();
            $model->propelrMerchantID = $uuid->getBytes();
        });
    }


    public function transactions()
    {
        return $this->hasMany(PropelrTransaction::class, 'propelrMerchantID', 'propelrMerchantID');
    }

    /**
     * Get the appropriate MID based on transaction type
     */
    public function getMidForTransactionType(int $transactionType): ?string
    {
        return $transactionType === PropelrTransaction::TRANSACTION_TYPE_ACH 
            ? $this->achMID 
            : $this->ccMID;
    }

    /**
     * Get Credit Card MID
     */
    public function getCreditCardMid(): ?string
    {
        return $this->ccMID;
    }

    /**
     * Get ACH MID
     */
    public function getAchMid(): ?string
    {
        return $this->achMID;
    }

    /**
     * Check if signature is signed
     */
    public function isSignatureSigned(): bool
    {
        return $this->signatureStatus === self::SIGNATURE_STATUS_SIGNED;
    }

    /**
     * Check if application has been submitted for underwriting
     */
    public function isSubmittedForUnderwriting(): bool
    {
        return !empty($this->submittedAt);
    }

    /**
     * Get signature status name
     */
    public function getSignatureStatusName(): string
    {
        return match ($this->signatureStatus) {
            self::SIGNATURE_STATUS_PENDING => 'Pending',
            self::SIGNATURE_STATUS_SIGNED => 'Signed',
            default => 'Unknown'
        };
    }

    /**
     * Get application status name
     */
    public function getApplicationStatusName(): string
    {
        return match ($this->status) {
            self::APPLICATION_STATUS_CREATED => 'Created',
            self::APPLICATION_STATUS_PENDING_SIGNATURE => 'Pending Signature',
            self::APPLICATION_STATUS_QUALIFY => 'Qualifying',
            self::APPLICATION_STATUS_UNDER => 'Under Review',
            self::APPLICATION_STATUS_BOARDING => 'Boarding',
            self::APPLICATION_STATUS_BOARDED => 'Boarded',
            self::APPLICATION_STATUS_LIVE => 'Live',
            self::APPLICATION_STATUS_DECLINED => 'Declined',
            default => 'Unknown'
        };
    }

    /**
     * Check if merchant can generate signature
     */
    public function canGenerateSignature(): bool
    {
        return $this->status === self::APPLICATION_STATUS_CREATED && 
               !empty($this->copilotMerchantID);
    }

    /**
     * Get available tax filing methods
     */
    public static function getTaxFilingMethods(): array
    {
        return [
            self::TAX_FILING_METHOD_EIN,
            self::TAX_FILING_METHOD_SSN
        ];
    }

    /**
     * Get available ownership types
     */
    public static function getOwnershipTypes(): array
    {
        return [
            self::OWNERSHIP_TYPE_GOVT,
            self::OWNERSHIP_TYPE_INDIVSOLE,
            self::OWNERSHIP_TYPE_LLC,
            self::OWNERSHIP_TYPE_NONPRFT,
            self::OWNERSHIP_TYPE_PRTNRSHP,
            self::OWNERSHIP_TYPE_PRIVCORP,
            self::OWNERSHIP_TYPE_PUBCORP,
            self::OWNERSHIP_TYPE_TAXEXMPT
        ];
    }

    /**
     * Get available bank account types
     */
    public static function getBankAccountTypes(): array
    {
        return [
            self::BANK_ACCOUNT_TYPE_BIZ,
            self::BANK_ACCOUNT_TYPE_SAVINGS
        ];
    }

    /**
     * Check if signature status is in a final state (SIGNED)
     */
    public function isSignatureStatusFinal(): bool
    {
        return $this->signatureStatus === self::SIGNATURE_STATUS_SIGNED;
    }

    /**
     * Check if application status is in a final state (BOARDED, LIVE, or DECLINED)
     */
    public function isApplicationStatusFinal(): bool
    {
        return in_array($this->status, [
            self::APPLICATION_STATUS_BOARDED,
            self::APPLICATION_STATUS_LIVE,
            self::APPLICATION_STATUS_DECLINED
        ]);
    }

    /**
     * Check if merchant needs any status updates (either signature or application status is not final)
     */
    public function needsStatusUpdate(): bool
    {
        return !$this->isSignatureStatusFinal() || !$this->isApplicationStatusFinal();
    }

    /**
     * Get ready to process status name
     */
    public function getReadyToProcessName(): string
    {
        return match($this->isReadyToProcess) {
            self::IS_READY_TO_PROCESS_PENDING => 'Pending',
            self::IS_READY_TO_PROCESS_READY => 'Ready',
            default => 'Unknown'
        };
    }

}
<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrawingPolyFoam extends Model
{
    use SoftDeletes;

    protected $table = 'drawingPolyFoams';
    protected $primaryKey = 'ID';
    protected $fillable = [
        'drawingID',
        'nodeID',
        'xPos',
        'yPos',
        'width',
        'height'
    ];
    protected $hidden = ['drawingID'];

    public function appDrawing()
    {
        return $this->belongsTo('AppDrawing', 'drawingID', 'drawingID');
    }
}

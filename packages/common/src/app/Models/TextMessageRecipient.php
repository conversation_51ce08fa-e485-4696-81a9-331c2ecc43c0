<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class TextMessageRecipient extends Model
{
    use SoftDeletes;
    use UuidTrait;

    const STATUS_PENDING = 1;
    const STATUS_ACCEPTED = 2;
    const STATUS_QUEUED = 3;
    const STATUS_SENT = 4;
    const STATUS_DELIVERED = 5;
    const STATUS_UNDELIVERED = 6;
    const STATUS_FAILED = 7;

    protected $table = 'textMessageRecipients';
    protected $primaryKey = 'textMessageRecipientID';
    protected $fillable = [
        'textMessageRecipientID', 'textMessageID', 'textMessageNumberID', 'status', 'acceptedAt', 'queuedAt', 'sentAt',
        'deliveredAt', 'undeliveredAt', 'failedAt'
    ];
    protected $casts = [
        'status' => 'int'
    ];
    protected $dateFormat = 'Y-m-d H:i:s.u';
    protected $dates = ['acceptedAt', 'queuedAt', 'sentAt', 'deliveredAt', 'undeliveredAt', 'failedAt'];

    public function message()
    {
        return $this->belongsTo(TextMessage::class, 'textMessageID', 'textMessageID');
    }

    public function number()
    {
        return $this->belongsTo(TextMessageNumber::class, 'textMessageNumberID', 'textMessageNumberID');
    }
}

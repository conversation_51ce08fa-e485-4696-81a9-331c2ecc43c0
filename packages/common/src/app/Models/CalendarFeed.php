<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;

class CalendarFeed extends Model
{
    use UuidTrait;

    const OWNER_TYPE_USER = 37;

    const TYPE_PROJECT_EVENT = 1;

    protected $table = 'calendarFeeds';
    protected $primaryKey = 'calendarFeedID';
    protected $fillable = [
        'calendarFeedID', 'ownerType', 'ownerID', 'type', 'refreshInterval', 'cacheMisses', 'cacheHits',
        'lastGeneratedAt'
    ];
    protected $casts = [
        'ownerType' => 'int',
        'ownerID' => 'int',
        'type' => 'int',
        'refreshInterval' => 'int',
        'cacheMisses' => 'int',
        'cacheHits' => 'int'
    ];
    protected $dates = ['lastGeneratedAt'];

    public function owner()
    {
        return $this->morphTo('owner', 'ownerType', 'ownerID');
    }

    public function scopeOfUser($query, $user)
    {
        if (is_object($user) && $user instanceof User) {
            $user = $user->userID;
        }
        return $query->where('ownerType', self::OWNER_TYPE_USER)->where('ownerID', $user);
    }
}

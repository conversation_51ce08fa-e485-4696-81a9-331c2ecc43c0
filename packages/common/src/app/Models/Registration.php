<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class Registration extends Model
{
    use UuidTrait;
    use SoftDeletes;

    protected $table = 'registrations';
    protected $primaryKey = 'registrationID';
    protected $fillable = [
        'registrationID', 'domainID', 'resellerRegistrationProfileID', 'isOnSite', 'userAccessTokenID'
    ];
    protected $casts = [
        'domainID' => 'int',
        'resellerRegistrationProfileID' => 'int',
        'isOnSite' => 'bool'
    ];

    public function resellerRegistrationProfile()
    {
        return $this->belongsTo(ResellerRegistrationProfile::class, 'resellerRegistrationProfileID', 'resellerRegistrationProfileID');
    }

    public function userAccessToken()
    {
        return $this->belongsTo(UserAccessToken::class, 'userAccessTokenID', 'userAccessTokenID');
    }
}

<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class MySalesmanCredential extends Model
{
    use UuidTrait;

    const API_AUTH_HEADER = 'X-Ca-Mys-Authorization';

    protected $table = 'mySalesmanCredentials';
    protected $primaryKey = 'mySalesmanCredentialsID';
    protected $fillable = ['mySalesmanCredentialsID', 'userID', 'key', 'isActive', 'deactivatedAt'];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->mySalesmanCredentialsID = random_bytes(16);
            $model->key = hash('sha256', bin2hex(random_bytes(32)));
        });
    }

    public function getApiKey(): string
    {
        return $this->key;
    }

    /**
     * Deactivate the credential
     *
     * @return void
     */
    public function deactivate(): void
    {
        $this->update([
            'isActive' => 0,
            'deactivatedAt' => Carbon::now(),
            'updatedAt' => Carbon::now(),
        ]);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'userID', 'userID');
    }

    public function getUser()
    {
        return $this->user()
            ->active()
            ->first();
    }
}

<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class IntakeIndustry extends Model
{
    protected $table = 'intakeIndustries';
    protected $primaryKey = 'intakeIndustryID';
    protected $fillable = ['name', 'order'];

    const FOUNDATION_REPAIR_WATERPROOFING = 1;
    const FENCING_STAINING_DECKS = 2;
    const FLOORING_CARPET_COATINGS = 3;
    const LANDSCAPE_TURF_IRRIGATION = 4;
    const PAINTING_RESURFACING = 5;
    const INSULATION_ROOFING = 6;
    const OTHER = 7;
    const HANDYMAN = 8;
    const HOME_BUILDER = 9;
    const RENOVATION_REMODELING = 10;
    const HVAC_PLUMBING_ELECTRICAL = 11;
    const CONCRETE_MASONRY = 12;
    const GUTTERS_EXTERIORS = 13;

    public function companyIntakes()
    {
        return $this->hasMany(CompanyIntake::class, 'intakeIndustryID');
    }

    public function productItems()
    {
        return $this->hasMany(IntakeIndustryProductItem::class, 'intakeIndustryID');
    }
}

<?php

namespace Common\Models;

use Common\Classes\DB\Model;

class MarketingType extends Model
{
    protected $table = 'marketingType';
    protected $primaryKey = 'marketingTypeID';

    protected $fillable = [
        'marketingTypeName', 'parentMarketingTypeID', 'companyID', 'dateAdded', 'dateUpdated', 'isRepeatBusiness'
    ];

    public $timestamps = false;

    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID', 'companyID');
    }

    public function projects()
    {
        return $this->hasMany(Project::class, 'referralMarketingTypeID');
    }
}

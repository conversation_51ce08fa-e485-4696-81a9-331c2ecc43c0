<?php

namespace Common\Models\Interfaces;

/**
 * Interface FormItemEntryGroupInterface
 *
 * @package Common\Models\Interfaces
 */
interface FormItemEntryGroupInterface
{
    public function children();

    public function entry();

    public function fieldFiles();

    public function fieldOptions();

    public function fieldProducts();

    public function fieldValues();

    public function group();

    public function parent();

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}

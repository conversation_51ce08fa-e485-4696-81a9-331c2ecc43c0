<?php

declare(strict_types=1);

namespace Common\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;

/**
 * Class PhoneRule
 *
 * @package Common\Components\Validation\Rules
 */
class PhoneRule extends Rule
{
    /**
     * Convert phone number into (XXX) XXX-XXXX format
     *
     * @param string $phone
     * @return string
     */
    public static function formatUsPhoneNumber(string $phone): string
    {
        $phone = preg_replace('#[^0-9]#', '', $phone);
        return '(' . substr($phone, 0, 3) . ') ' . substr($phone, 3, 3) . '-' . substr($phone, 6, 4);
    }

    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return array_merge(parent::getMessages(), [
            'us_phone' => '{label} must contain 10 digits',
            'us_phone_copilot' => '{label} is invalid. Please use XXX-XXX-XXXX format. Phone cannot start with a zero or a one and must not be all the same numbers.'
        ]);
    }

    /**
     * Verify field has 10 digits ignoring any non-numeric characters
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('us_phone')]
    public function usPhone(mixed $field): bool|string
    {
        $phone = preg_replace('#[^0-9]#', '', (string) $field);
        if (strlen($phone) === 10) {
            return true;
        }
        return 'us_phone';
    }

    /**
     * Verify field is a valid US phone number for CoPilot API requirements
     * - Must be 10 digits
     * - Area code cannot start with 0 or 1
     * - Cannot be all the same digits
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('us_phone_copilot')]
    public function usPhoneCopilot(mixed $field): bool|string
    {
        $phone = preg_replace('#[^0-9]#', '', (string) $field);
        
        // Must be exactly 10 digits
        if (strlen($phone) !== 10) {
            return 'us_phone_copilot';
        }
        
        // Area code (first digit) cannot be 0 or 1
        if ($phone[0] === '0' || $phone[0] === '1') {
            return 'us_phone_copilot';
        }
        // Cannot be all the same digits
        if (count(array_unique(str_split($phone))) === 1) {
            return 'us_phone_copilot';
        }
        // Additional invalid patterns commonly rejected
        $invalid_patterns = [
            '1234567890',
            '0123456789',
            '9876543210',
            '5555555555',
            '1111111111',
            '0000000000'
        ];
        
        if (in_array($phone, $invalid_patterns)) {
            return 'us_phone_copilot';
        }
        
        return true;
    }

    /**
     * Format number in (XXX) XXX-XXXX format
     *
     * @param mixed $field
     * @return bool
     */
    #[RuleAttribute('us_phone_format')]
    public function usPhoneFormat(mixed &$field): bool
    {
        $field = self::formatUsPhoneNumber((string) $field);
        return true;
    }
}

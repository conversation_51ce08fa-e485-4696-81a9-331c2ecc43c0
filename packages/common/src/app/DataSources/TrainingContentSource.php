<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\TrainingContent;

/**
 * Class TrainingContentSource
 *
 * @package Common\DataSources
 */
class TrainingContentSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'training_content';

    /**
     * Setup source by importing data into database
     *
     * @param string $env
     * @param BrandSource $brand
     * @throws \Core\Exceptions\AppException
     */
    public function setup(string $env, BrandSource $brand): void
    {
        $content = $this->loadData($env);
        foreach ($content as $info) {
            TrainingContent::create([
                'brandID' => isset($info['brand_alias']) ? $brand->getIdFromAlias($info['brand_alias']) : null,
                'name' => $info['name'],
                'content' => $info['content']
            ]);
        }
    }
}

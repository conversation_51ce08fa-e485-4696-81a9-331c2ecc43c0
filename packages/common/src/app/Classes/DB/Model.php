<?php

namespace Common\Classes\DB;

use Common\Traits\DB\UtcDateTrait;
use Core\Components\DB\Traits\OptionalMorphTrait;
use Illuminate\Database\Eloquent\Model as EloquentModel;

/**
 * Class Model
 *
 * @package Common\Classes\DB
 */
class Model extends EloquentModel
{
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    const DELETED_AT = 'deletedAt';

    use OptionalMorphTrait;
    use UtcDateTrait;
}

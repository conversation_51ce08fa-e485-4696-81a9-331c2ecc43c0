<?php

declare(strict_types=1);

namespace Core\Classes\Exception;

use Core\Exceptions\AppException;
use Core\Interfaces\{ExceptionHandlerInterface, KernelInterface};
use ErrorException;
use Throwable;

/**
 * Class Handler
 *
 * @package Core\Classes\Exception
 */
class Handler implements ExceptionHandlerInterface
{
    /**
     * Handler constructor
     *
     * @param KernelInterface $kernel
     */
    public function __construct(protected KernelInterface $kernel)
    {}

    /**
     * Log exception to standard error log configured in PHP settings
     *
     * @param Throwable $exception
     */
    protected function log(Throwable $exception): void
    {
        \error_log((string) $exception);
    }

    /**
     * Determines if error should halt application
     *
     * @param ErrorException $exception
     * @return bool
     */
    public function errorShouldHalt(ErrorException $exception): bool
    {
        return true;
    }

    /**
     * Handle a PHP error which has been converted to an exception
     *
     * @param ErrorException $exception
     */
    public function handleError(ErrorException $exception): void
    {
        $this->log($exception);
    }

    /**
     * <PERSON><PERSON> thrown exception which was not caught
     *
     * @param Throwable $exception
     */
    public function handleException(Throwable $exception): void
    {
        $this->log($exception);
    }

    /**
     * Convert exception stack trace into string
     *
     * @param Throwable $e
     * @param null|array $seen
     * @return string
     */
    protected function getTraceAsString(Throwable $e, array $seen = null): string
    {
        $result = [
            \sprintf('%s%s: %s', ($seen !== null ? 'Caused by: ' : ''), \get_class($e), $e->getMessage())
        ];
        if ($seen === null) {
            $seen = [];
        }
        $trace = $e->getTrace();
        $prev = $e instanceof AppException ? $e->getLastException() : $e->getPrevious();
        $file = $e->getFile();
        $line = $e->getLine();
        $c = \count($trace);
        for ($i = 0; $i < $c; $i++) {
            $trace_item = $trace[$i];
            $current = "{$file}:{$line}";
            if (\is_array($seen) && \in_array($current, $seen)) {
                $result[] = \sprintf(' ... %d more', $c - $i);
                break;
            }
            $str = ' at ';
            if (isset($trace_item['class'])) {
                $str .= \str_replace('\\', '.', $trace_item['class']);
                if (isset($trace_item['function'])) {
                    $str .= '.';
                }
            }
            if (isset($trace_item['function'])) {
                $str .= \str_replace('\\', '.', $trace_item['function']);
            } else {
                $str .= '(main)';
            }
            $str .= '(' . ($line === null ? $file : \basename($file));
            if ($line !== null) {
                $str .= ':' . $line;
            }
            $str .= ')';
            $result[] = $str;
            $seen[] = $current;
            $file = $trace_item['file'] ?? 'Unknown Source';
            $line = isset($trace_item['file']) && isset($trace_item['line']) ? $trace_item['line'] : null;
        }
        $result = \implode(\PHP_EOL, $result);
        if ($prev !== null) {
            $result .= \PHP_EOL . $this->getTraceAsString($prev, $seen);
        }
        return $result;
    }
}

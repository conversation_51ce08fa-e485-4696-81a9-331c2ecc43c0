<?php

declare(strict_types=1);

namespace Core\StaticAccessors;

use Core\Classes\StaticAccessor;

/**
 * Class Path
 *
 * @package Core\StaticAccessors
 *
 * @method static string normalize(string $path)
 * @method static string|bool get(string $name, ...$args)
 * @method static string build(string $name, ?string $path = null, ...$args)
 */
class Path extends StaticAccessor
{
    /**
     * Get instance of Path from registry
     *
     * @return \Core\Classes\Path
     */
    protected static function getInstance(): \Core\Classes\Path
    {
        return static::$kernel->get(\Core\Classes\Path::class);
    }
}

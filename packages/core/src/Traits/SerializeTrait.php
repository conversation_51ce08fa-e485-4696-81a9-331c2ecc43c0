<?php

declare(strict_types=1);

namespace Core\Traits;

use Core\Attributes\{PreSerializeAttribute, SerializeIgnoreAttribute};
use ReflectionClass;
use ReflectionProperty;

/**
 * Trait SerializeTrait
 *
 * @package Core\Traits
 */
trait SerializeTrait
{
    /**
     * Grabs all non static properties and those which aren't annotated as ignored for use during serialization
     *
     * @return array
     */
    public function __sleep(): array
    {
        $class = new ReflectionClass($this);
        foreach ($class->getMethods() as $method) {
            $attribute = $method->getAttributes(PreSerializeAttribute::class)[0] ?? null;
            if ($attribute === null) {
                continue;
            }
            $this->{$method->getName()}();
        }
        $properties = array_filter($class->getProperties(), function (ReflectionProperty $property) {
            return !$property->isStatic() && count($property->getAttributes(SerializeIgnoreAttribute::class)) === 0;
        });
        return array_map(fn(ReflectionProperty $property) => $property->getName(), $properties);
    }
}

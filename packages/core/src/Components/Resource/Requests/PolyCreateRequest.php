<?php

namespace Core\Components\Resource\Requests;

use Core\Components\Resource\Classes\BatchRequest;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Exceptions\ActionNotAllowedException;
use Core\Components\Resource\Exceptions\BatchHandleException;
use Core\Components\Resource\Exceptions\MediaTypeNotFoundException;
use Core\Components\Resource\Exceptions\RelationNotFoundException;
use Core\Components\Resource\Exceptions\ResourceException;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Interfaces\BatchableRequestInterface;
use Core\Components\Resource\Interfaces\MediaHandlerInterface;
use Core\Components\Resource\Interfaces\PolyRequestInterface;
use Core\Components\Resource\Interfaces\ValidatedRequestInterface;
use Core\Components\Resource\Traits\Request\BatchableTrait;
use Core\Components\Resource\Traits\Request\EntityTrait;
use Core\Components\Resource\Traits\Request\NestedTrait;
use Core\Components\Resource\Traits\Request\PolyTrait;
use Core\Components\Resource\Traits\Request\SaveProxyTrait;
use Core\Components\Resource\Traits\Request\ScopeTrait;
use Core\Components\Resource\Traits\Request\TransactionTrait;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Validation;

/**
 * Class PolyCreateRequest
 *
 * Used to handle nested requests within an entity, usually used with polymorphic relationships. A separate request is
 * needed to batch all the request together so failures are handled within a DB transaction and request level rollbacks
 * are ran.
 *
 * @package Core\Components\Resource\Requests
 */
class PolyCreateRequest extends Request implements BatchableRequestInterface, ValidatedRequestInterface, PolyRequestInterface
{
    use BatchableTrait;
    use EntityTrait;
    use NestedTrait;
    use PolyTrait;
    use ScopeTrait;
    use SaveProxyTrait;
    use TransactionTrait;

    /**
     * @var null|BatchRequest
     */
    protected $batch_request = null;

    /**
     * PolyCreateRequest constructor
     *
     * @param Resource $resource
     * @param Entity $entity
     */
    public function __construct(Resource $resource, Entity $entity)
    {
        parent::__construct($resource);

        $this->setEntity($entity);
    }

    /**
     * Set batch request to be used by all poly requests
     *
     * @param BatchRequest $request
     */
    protected function setBatchRequest(BatchRequest $request)
    {
        $this->batch_request = $request;
    }

    /**
     * Get batch request
     *
     * @return BatchRequest|null
     */
    public function getBatchRequest()
    {
        return $this->batch_request;
    }

    /**
     * Prepare request
     *
     * Pull polymorphic relations and media out of main entity to be processed as individual requests in a batch
     *
     * @throws ActionNotAllowedException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     * @throws \Core\Exceptions\AppException
     */
    public function prepare()
    {
        parent::prepare();

        $action = Resource::ACTION_CREATE;
        if ($this->isNested()) {
            $action = Resource::ACTION_NESTED_CREATE;
        }

        if (!$this->actionAllowed($action)) {
            throw new ActionNotAllowedException('Create action not allowed');
        }

        $this->setAction($action);

        $entity = clone $this->getEntity();
        $this->setMainEntity($entity);

        $batch_request = BatchRequest::make()->sequential();
        $batch_request->setUseTransaction($this->useTransaction());
        $this->setBatchRequest($batch_request);

        // create main request
        $main_request = $this->resource->create($entity)->actionCheck(false);
        // since this is a proxy request for creates, we set the main create request as the source so any calls made to
        // this instance will be passed on to it
        $this->setRequest($main_request);

        $fields = $this->resource->getFields();

        // if field list has polymorphic fields, then find the nested entities in the input entity
        if ($fields->polyCount() > 0) {
            foreach ($fields->getAllPolyFields() as $poly_field) {
                // build field list with only the associated type field for poly field
                $type_field = $fields->only($poly_field->getTypeField())->ofAction($action, $this->resource->getAccessLevel());

                // run create validator with field list and entity data
                $validator = $this->resource->getValidation($type_field)->run($entity->toArray());

                // if type field is not valid, then skip
                if (!$validator->valid()) {
                    continue;
                }

                // if poly field isn't available in entity or if it's null, then skip
                $poly_name = $poly_field->getName();
                if (!$entity->has($poly_name) || ($poly_entity = $entity->get($poly_name)) === null) {
                    continue;
                }

                // remove it from the entity, so it doesn't clutter up the main create request (even though it wouldn't hurt it)
                $entity->remove($poly_name);

                if (!is_array($poly_entity)) {
                    $poly_entity = [];
                }
                $this->addPolyEntity($poly_name, $poly_field, Entity::make($poly_entity));
            }
            // if some poly entities were found in the entity, then create a batch request
            if (count($this->poly_entities) > 0) {
                // loop through and add all poly entities to a batch request
                foreach ($this->poly_entities as &$poly_entity) {
                    try {
                        // get proper resource based on type field of main entity
                        $poly_resource = $this->resource
                            ->polyRelationResource($poly_entity['field']->getRelation(), $entity->get($poly_entity['field']->getTypeField()));
                        $request = $poly_entity['request'] = $poly_resource->create($poly_entity['entity']);
                        if ($this->isNested()) {
                            $request->nested();
                        }
                        $request->attach('handle.after', function () use ($entity, $poly_entity, $request) {
                            $entity->set($poly_entity['field']->getKeyField(), $request->response());
                        });
                        $request->setParentPolyRequest($this);
                        $batch_request->add($request, $poly_entity['field']->getName());
                    } catch (RelationNotFoundException $e) {
                        continue;
                    }
                }
            }
        }

        // if field list has media fields, then find the nested media in the input entity
        if ($fields->mediaCount() > 0) {
            foreach ($fields->getAllMediaFields() as $media_field) {
                // if media field isn't available in entity or if it's null, then skip
                $media_name = $media_field->getName();
                if (!$entity->has($media_name) || ($media_entity = $entity->get($media_name)) === null) {
                    continue;
                }

                // remove it from the entity, so it doesn't clutter up the main create request (even though it wouldn't hurt it)
                $entity->remove($media_name);

                if (!is_array($media_entity)) {
                    $media_entity = [];
                }
                $this->addMediaEntity($media_name, $media_field, Entity::make($media_entity));
            }
            // if some media entities were found in the entity, then send them to their respective media handlers for processing
            if (count($this->media_entities) > 0) {
                $media_validation_fields = [];
                $media_validation_entity = new Entity;
                // loop through and find all necessary validation for media fields
                foreach ($this->media_entities as $media_entity) {
                    if (($validation_field = $media_entity['field']->getValidationField()) === null) {
                        continue;
                    }
                    $media_validation_fields[] = $validation_field;
                    $media_validation_entity[$media_entity['field']->getName()] = $media_entity['entity'];
                }
                // if media validation fields are found, then run them through a validator
                if (count($media_validation_fields) > 0) {
                    $field_config = FieldConfig::fromArray($media_validation_fields);
                    $validator = (new Validation($this->resource->getValidationRules()))
                        ->config($field_config)
                        ->run($media_validation_entity->toArray());
                    if (!$validator->valid()) {
                        throw (new ValidationException('Unable to process media requests'))
                            ->setValidator($validator);
                    }
                }
                // if validation has passed, loop through and send all media entities to their respective media handlers
                foreach ($this->media_entities as $media_entity) {
                    try {
                        /** @var MediaHandlerInterface $handler */
                        $handler = $this->resource->getMediaHandler($media_entity['field']->getMediaType());
                        $handler->create($media_entity['entity'], $this);
                    } catch (MediaTypeNotFoundException $e) {
                        continue;
                    }
                }
            }
        }

        // setup main request
        if ($this->isNested()) {
            $main_request->nested();
        }
        if ($this->hasScope()) {
            $main_request->scope($this->getScope());
        }
        // assign this poly request to the create request so it can access other poly entities and media if necessary
        $main_request->setParentPolyRequest($this);
        $batch_request->add($main_request, '_main');

        $this->prepared = true;
    }

    /**
     * Handle request
     *
     * Run nested batch request
     *
     * @throws \Core\Components\Resource\Exceptions\BatchPrepareException
     * @throws \Core\Exceptions\AppException
     */
    public function handle()
    {
        parent::handle();

        try {
            $this->batch_request->run();
        } catch (BatchHandleException $e) {
            foreach ($this->batch_request->getRequests() as $name => $request) {
                if ($request->success()) {
                    continue;
                }
                if ($name === '_main') {
                    throw $request->exception();
                }
                throw (new ValidationException('Unable to validate nested requests'))->setErrors([
                    $name => ResourceException::toMessage($request->exception())
                ]);
            }
        }
    }
}

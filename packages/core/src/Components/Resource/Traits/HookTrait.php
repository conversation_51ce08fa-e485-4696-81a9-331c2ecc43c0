<?php

namespace Core\Components\Resource\Traits;

use Closure;
use Core\Components\Resource\Classes\Hooks;

trait HookTrait
{
    /**
     * @var null|Hooks
     */
    protected static $hooks = null;

    public static function setHooksManager(Hooks $hooks)
    {
        static::$hooks = $hooks;
    }

    public static function delegate($class)
    {
        if (!isset(static::$hooks)) {
            return;
        }
        static::$hooks->delegate(static::class, $class);
    }

    public static function attach($name, Closure $closure)
    {
        if (!isset(static::$hooks)) {
            return;
        }
        static::$hooks->attach(static::class, $name, $closure);
    }

    // @todo potentially run instance only hooks here
    public function runHook($name, ...$args)
    {
        if (!isset(static::$hooks)) {
            return;
        }
        static::$hooks->run(static::class, $name, ...$args);
    }

    public function runFilterHook($name, $value, ...$args)
    {
        if (!isset(static::$hooks)) {
            return $value;
        }
        return static::$hooks->runFilter(static::class, $name, $value, ...$args);
    }

    /**
     * @param string|array $name
     * @param bool $default
     * @param bool $stop_value
     * @param array ...$args
     * @return bool
     */
    public function runBoolHook($name, $default = false, $stop_value = false, ...$args)
    {
        if (!isset(static::$hooks)) {
            return $default;
        }
        return static::$hooks->runBool(static::class, $name, $default, $stop_value, ...$args);
    }
}

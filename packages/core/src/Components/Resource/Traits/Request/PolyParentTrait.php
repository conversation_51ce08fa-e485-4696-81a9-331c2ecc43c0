<?php

namespace Core\Components\Resource\Traits\Request;

use Core\Components\Resource\Interfaces\PolyRequestInterface;

/**
 * Trait PolyParentTrait
 *
 * @package Core\Components\Resource\Traits\Request
 */
trait PolyParentTrait
{
    /**
     * @var null|PolyRequestInterface
     */
    protected $parent_poly_request = null;

    /**
     * Set parent poly request
     *
     * @param PolyRequestInterface $request
     */
    public function setParentPolyRequest(PolyRequestInterface $request)
    {
        $this->parent_poly_request = $request;
    }

    /**
     * Get parent poly request
     *
     * @return null|PolyRequestInterface
     */
    public function getParentPolyRequest()
    {
        return $this->parent_poly_request;
    }
}

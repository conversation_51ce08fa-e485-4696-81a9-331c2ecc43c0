<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Carbon\Carbon;
use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\{Rule, Rules, Validator};
use Core\Components\Validation\Exceptions\ValidationException;
use Core\Exceptions\AppException;
use DateTimeZone;
use InvalidArgumentException;
use Throwable;

/**
 * Class DateTimeRule
 *
 * @package Core\Components\Validation\Rules
 */
class DateTimeRule extends Rule
{
    protected const DATE_ORDER_BEFORE = 1;
    protected const DATE_ORDER_AFTER = 2;

    /**
     * Default date rule timezone
     *
     * @var DateTimeZone|null
     */
    protected ?DateTimeZone $timezone = null;

    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'date' => '{label} is not in a valid format',
            'date_before' => '{label} must be before {compare_value}',
            'date_after' => '{label} must be after {compare_value}',
            'iso8601_date:datetime' => '{label} must be a valid ISO 8601 datetime',
            'iso8601_date:date' => '{label} must be a valid ISO 8601 date [YYYY-MM-DD]',
            'iso8601_date:time' => '{label} must be a valid ISO 8601 time',
            'carbon' => 'Unable to convert date into Carbon instance'
        ];
    }

    /**
     * Set default date rule timezone
     *
     * @param DateTimeZone|null $timezone
     */
    public function setTimezone(?DateTimeZone $timezone): void
    {
        $this->timezone = $timezone;
    }

    /**
     * Get default timezone
     *
     * @return DateTimeZone|null
     */
    public function getTimezone(): ?DateTimeZone
    {
        return $this->timezone;
    }

    /**
     * Parse data using specified timezone
     *
     * @param mixed $field
     * @param mixed $timezone
     * @return bool|string
     */
    #[RuleAttribute('date')]
    public function date(mixed &$field, mixed $timezone): bool|string
    {
        try {
            if (!is_string($timezone) && (!is_object($timezone) || !($timezone instanceof DateTimeZone))) {
                $timezone = $this->timezone;
            }
            $field = Carbon::parse((string) $field, $timezone);
            return true;
        } catch (Throwable) {
            return 'date';
        }
    }

    /**
     * Set time of date to start of day
     *
     * @param Carbon $field
     * @return bool
     */
    #[RuleAttribute('date_sod')]
    public function dateSod(Carbon $field): bool
    {
        $field->startOfDay();
        return true;
    }

    /**
     * Set time of date to end of day
     *
     * @param Carbon $field
     * @return bool
     */
    #[RuleAttribute('date_eod')]
    public function dateEod(Carbon $field): bool
    {
        $field->endOfDay();
        return true;
    }

    /**
     * Verify date is before or after a specified field
     *
     * @param int $type
     * @param Carbon $field
     * @param array $param
     * @param Validator $validator
     * @return int|array|bool
     *
     * @throws AppException
     */
    protected function dateOrder(int $type, Carbon $field, mixed $param, Validator $validator): int|array|bool
    {
        if (!is_array($param)) {
            throw new InvalidArgumentException('Date order config must be an array');
        }
        $config = $validator->getConfig();
        if (isset($param['field'])) {
            if (!$validator->errors()->has($param['field'])) {
                $value = $validator->data($param['field']);
            }
            if (($_field = $config->getField($param['field'])) === null) {
                throw new InvalidArgumentException('Unable to find field');
            }
            $compare_value = $_field->getLabel($param['field']);
            // @todo add other types like 'value' here
        } else {
            throw new InvalidArgumentException('Invalid date value type specified, only field allowed');
        }
        if (!isset($value)) {
            // skip rule if other field is not present
            return Rules::STOP;
        }
        if (!is_object($value) || !($value instanceof Carbon)) {
            throw new ValidationException('Date comparisons can only be done with Carbon dates');
        }
        switch ($type) {
            case static::DATE_ORDER_BEFORE:
                if ($field->gte($value)) {
                    return ['date_before', ['compare_value' => $compare_value]];
                }
                break;
            case static::DATE_ORDER_AFTER:
                if ($field->lte($value)) {
                    return ['date_after', ['compare_value' => $compare_value]];
                }
                break;
        }
        return true;
    }

    /**
     * Validate date is before specified field
     *
     * @param Carbon $field
     * @param mixed $param
     * @param Validator $validator
     * @return int|array|bool
     * @throws AppException
     */
    #[RuleAttribute('date_before')]
    public function dateBefore(Carbon &$field, mixed $param, Validator $validator): int|array|bool
    {
        return $this->dateOrder(static::DATE_ORDER_BEFORE, $field, $param, $validator);
    }

    /**
     * Validate date is after specified field
     *
     * @param Carbon $field
     * @param mixed $param
     * @param Validator $validator
     * @return int|array|bool
     * @throws AppException
     */
    #[RuleAttribute('date_after')]
    public function dateAfter(Carbon &$field, $param, Validator $validator): int|array|bool
    {
        return $this->dateOrder(static::DATE_ORDER_AFTER, $field, $param, $validator);
    }

    /**
     * Convert date to UTC timezone
     *
     * @param mixed $field
     * @param mixed $timezone
     * @return bool|string
     */
    #[RuleAttribute('utc_date')]
    public function utcDate(mixed &$field, mixed $timezone): bool|string
    {
        if (($return = $this->date($field, $timezone)) !== true) {
            return $return;
        }
        $field->timezone('UTC');
        return true;
    }

    /**
     * Verify field is a valid ISO8601 date/time
     *
     * @param mixed $field
     * @param string $type
     * @return string|bool
     */
    #[RuleAttribute('iso8601_date')]
    public function iso8601Date(mixed $field, mixed $type): string|bool
    {
        if (!is_string($type)) {
            $type = 'datetime';
        }
        $regex = [
            'date' => '(?:[1-9]\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)',
            'time' => '(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d(?:Z|[+-][01]\d:[0-5]\d)'
        ];
        $regex['datetime'] = "{$regex['date']}T{$regex['time']}";
        if (!isset($regex[$type])) {
            throw new InvalidArgumentException('Invalid date part');
        }
        if (preg_match("#^{$regex[$type]}$#", (string) $field) !== 1) {
            return "iso8601_date:{$type}";
        }
        return true;
    }

    /**
     * Convert field to Carbon instance
     *
     * @param string $field
     * @param string|DateTimeZone $timezone
     * @return bool|string
     */
    #[RuleAttribute('to_carbon')]
    public function toCarbon(mixed &$field, mixed $timezone): bool|string
    {
        try {
            if (!is_string($timezone) && (!is_object($timezone) || !($timezone instanceof DateTimeZone))) {
                $timezone = $this->timezone;
            }
            $field = Carbon::parse((string) $field, $timezone);
            return true;
        } catch (Throwable) {
            return 'carbon';
        }
    }

    /**
     * Change timezone of field Carbon instance to UTC
     *
     * @param Carbon $field
     * @return bool
     */
    #[RuleAttribute('to_utc')]
    public function toUtc(Carbon $field): bool
    {
        $field->timezone('UTC');
        return true;
    }
}

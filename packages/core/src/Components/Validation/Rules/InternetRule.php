<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Core\Classes\{Arr, File};
use Core\Components\Http\Classes\{Http, URLBuilder};
use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;
use Core\Exceptions\AppException;
use InvalidArgumentException;

/**
 * Class InternetRule
 *
 * @package Core\Components\Validation\Rules
 */
class InternetRule extends Rule
{
    public const IP_TYPE_V4 = 1;
    public const IP_TYPE_V6 = 2;

    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'email' => '{label} is invalid',
            'url_invalid' => '{label} is not a valid URL',
            'upload_required' => '{label} is required',
            'upload_unknown' => '{label} failed to upload due to unknown error',
            'upload_invalid_mime' => '{label} file type is not allowed',
            'upload_invalid_img' => '{label} is not a valid image',
            'upload_invalid_size' => '{label} exceeds the maximum file size of {size}',
            'upload_image_min' => '{label} dimensions must be greater than {width}x{height}',
            'upload_image_min_width' => '{label} width must be greater than {width}px',
            'upload_image_min_height' => '{label} height must be greater than {height}px',
            'upload_image_max' => '{label} dimensions must be less than {width}x{height}',
            'upload_image_max_width' => '{label} width must be less than {width}px',
            'upload_image_max_height' => '{label} height must be less than {height}px'
        ];
    }

    /**
     * Validate IP address and determine type
     *
     * Optionally convert IPv4 address to IPv4-mapped IPv6 address
     *
     * @param string $ip
     * @param bool $ipv4_to_ipv6
     * @return array<int, string>|bool
     */
    public static function validateIp(string $ip, bool $ipv4_to_ipv6 = false): array|bool
    {
        if (\filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false) {
            $type = self::IP_TYPE_V4;
            if ($ipv4_to_ipv6) {
                $ip = "0:0:0:0:0:FFFF:{$ip}";
            }
        } elseif (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false) {
            $type = self::IP_TYPE_V6;
        } else {
            return false;
        }
        return [$type, $ip];
    }

    /**
     * Validate individual email
     *
     * @param string $email
     * @return bool
     */
    public static function validateEmail(string $email): bool
    {
        if (($pos = strpos($email, '@')) === false || $pos === 0 || $pos == strlen($email) || substr_count($email, '@') > 1) {
            return false;
        }
        $local_part = substr($email, 0, $pos);
        if (preg_match('#^[A-Za-z0-9!\#\$%&\'\*\+\-/=\?\^_`\{\|\}~\.]+$#', $local_part) == 0) {
            return false;
        }
        if (str_starts_with($local_part, '.') || str_ends_with($local_part, '.') || preg_match('#[\.]{2,}#', $local_part) > 0) {
            return false;
        }
        $hostname = substr($email, ($pos + 1));
        if (preg_match('#^[A-Za-z0-9\-\.]+$#', $hostname) !== 1) {
            return false;
        }
        $not_allowed = ['-', '.'];
        if (in_array(substr($hostname, 0, 1), $not_allowed) || in_array(substr($hostname, -1, 1), $not_allowed) || preg_match('#[\.]{2,}#', $hostname) > 0) {
            return false;
        }
        return true;
    }

    /**
     * Validate email address
     *
     * @param mixed $field
     * @return string|bool
     */
    #[RuleAttribute('email')]
    public function email(mixed $field): string|bool
    {
        if (!self::validateEmail((string) $field)) {
            return 'email';
        }
        return true;
    }

    /**
     * Filter to validate URL and convert into URL Builder instance
     *
     * @param string $field
     * @return string|bool
     */
    #[RuleAttribute('url_builder')]
    public function urlBuilder(mixed &$field): string|bool
    {
        try {
            if (!is_string($field)) {
                return 'url_invalid';
            }
            $field = URLBuilder::fromString($field);
            return true;
        } catch (InvalidArgumentException) {
            return 'url_invalid';
        }
    }

    /**
     * Verify upload requirement, mime type, and max size
     *
     * Requires a _FILES array style field entry
     *
     * @param mixed $field
     * @param array $config
     * @return string|bool|array
     *
     * @throws AppException
     */
    #[RuleAttribute('upload')]
    public function upload(mixed &$field, array $config): string|bool|array
    {
        $missing = [];
        if (!Arr::hasKey($config, ['mimes', 'max_size'], $missing)) {
            throw new AppException('Upload config missing the following items: %s', implode(', ', $missing));
        }
        if (($config['max_size'] = File::toBytes($config['max_size'])) === false) {
            throw new AppException('Invalid max file size');
        }
        if (!is_array($config['mimes'])) {
            $config['mimes'] = explode(',', $config['mimes']);
        }
        if (!is_array($field) || !isset($field['error']) || is_array($field['error'])) {
            if (isset($config['required']) && $config['required']) {
                return 'upload_required';
            }
            $field = null;
            return true;
        }
        switch ($field['error']) {
            case UPLOAD_ERR_OK:
                break;
            case UPLOAD_ERR_NO_FILE:
                return 'upload_required';
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return ['upload_invalid_size', ['size' => File::formatFilesize($config['max_size'])]];
            default:
                return 'upload_unknown';
        }

        if (filesize($field['tmp_name']) > $config['max_size']) {
            return ['upload_invalid_size', ['size' => File::formatFilesize($config['max_size'])]];
        }

        $mime = Http::getMimeType($field['tmp_name'], $field['type']);
        $extn = Http::getExtensionByMimeType($mime);

        if (!in_array($extn, $config['mimes'])) {
            return 'upload_invalid_mime';
        }

        if (in_array($extn, ['jpeg', 'png', 'gif'])) {
            if (($image = getimagesize($field['tmp_name'])) === false) {
                return 'upload_invalid_img';
            }
            [$width, $height] = $image;

            //check dimensions
            foreach (['min' => 'Min', 'max' => 'Max'] as $type => $label) {
                $width_key = "{$type}_width";
                $height_key = "{$type}_height";
                if (isset($config["{$type}_dimensions"])) {
                    if (count($config["{$type}_dimensions"]) !== 2) {
                        throw new AppException('%s dimensions config required 2 values', $label);
                    }
                    [$config[$width_key], $config[$height_key]] = $config["{$type}_dimensions"];
                }
                switch ($type) {
                    case 'max':
                        $_width = isset($config[$width_key]) && $width > $config[$width_key];
                        $_height = isset($config[$height_key]) && $height > $config[$height_key];
                        break;
                    case 'min':
                        $_width = isset($config[$width_key]) && $width < $config[$width_key];
                        $_height = isset($config[$height_key]) && $height < $config[$height_key];
                        break;
                }
                if ($_width && $_height) {
                    return ["upload_image_{$type}", ['width' => $config[$width_key], 'height' => $config[$height_key]]];
                }
                if ($_width) {
                    return ["upload_image_{$type}_width", ['width' => $config[$width_key]]];
                }
                if ($_height) {
                    return ["upload_image_{$type}_height", ['height' => $config[$height_key]]];
                }
            }
        }

        return true;
    }
}

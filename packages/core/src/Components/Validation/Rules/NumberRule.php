<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;
use InvalidArgumentException;

/**
 * Class NumberRule
 *
 * @package Core\Components\Validation\Rules
 */
class NumberRule extends Rule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'numeric' => '{label} must be numeric',
            'less_than' => '{label} must be less than {value}',
            'less_than_equal' => '{label} must be less than or equal to {value}',
            'greater_than' => '{label} must be greater than {value}',
            'greater_than_equal' => '{label} must be greater than or equal to {value}',
            'decimal_invalid_1' => '{label} must contain a decimal point',
            'decimal_invalid_2' => '{label} must be in the format: {left}.{right}'
        ];
    }

    /**
     * Verify field is numeric
     *
     * @param mixed $field
     * @return bool|string
     */
    #[RuleAttribute('numeric')]
    public function numeric(mixed $field): bool|string
    {
        if (is_numeric($field)) {
            return true;
        }
        return 'numeric';
    }

    /**
     * Convert number to its absolute value
     *
     * @param mixed $field
     * @return bool
     */
    #[RuleAttribute('abs')]
    public function abs(mixed &$field): bool
    {
        if (is_integer($field) || is_float($field)) {
            $field = abs($field);
        } elseif (is_numeric($field) && is_string($field) && str_starts_with($field, '-')) {
            $field = substr($field, 1);
        }
        return true;
    }

    /**
     * Verify field is less than provided value
     *
     * @param mixed $field
     * @param mixed $value
     * @return bool|array|string
     */
    #[RuleAttribute('less_than')]
    public function lessThan(mixed $field, mixed $value): bool|array|string
    {
        $bc_scale = 0;
        if (is_array($value)) {
            [$value, $bc_scale] = $value;
        }
        if (is_integer($field) || is_float($field)) {
            if ($field < $value) {
                return true;
            }
            return ['less_than', ['value' => $value]];
        }
        if (is_string($field)) {
            $value = (string) $value;
            if (bccomp($field, $value, (int) $bc_scale) === -1) {
                return true;
            }
            return ['less_than', ['value' => $value]];
        }
        return 'numeric';
    }

    /**
     * Verify field is less than or equal to provided value
     *
     * @param mixed $field
     * @param mixed $value
     * @return bool|array|string
     */
    #[RuleAttribute('less_than_equal')]
    public function lessThanEqual(mixed $field, mixed $value): bool|array|string
    {
        $bc_scale = 0;
        if (is_array($value)) {
            [$value, $bc_scale] = $value;
        }
        if (is_integer($field) || is_float($field)) {
            if ($field <= $value) {
                return true;
            }
            return ['less_than_equal', ['value' => $value]];
        }
        if (is_string($field)) {
            $value = (string) $value;
            if (bccomp($field, $value, (int) $bc_scale) <= 0) {
                return true;
            }
            return ['less_than_equal', ['value' => $value]];
        }
        return 'numeric';
    }

    /**
     * Verify field is greater than provided value
     *
     * @param mixed $field
     * @param mixed $value
     * @return bool|array|string
     */
    #[RuleAttribute('greater_than')]
    public function greaterThan(mixed $field, mixed $value): bool|array|string
    {
        $bc_scale = 0;
        if (is_array($value)) {
            [$value, $bc_scale] = $value;
        }
        if (is_integer($field) || is_float($field)) {
            if ($field > $value) {
                return true;
            }
            return ['greater_than', ['value' => $value]];
        }
        if (is_string($field)) {
            $value = (string) $value;
            if (bccomp($field, $value, (int) $bc_scale) === 1) {
                return true;
            }
            return ['greater_than', ['value' => $value]];
        }
        return 'numeric';
    }

    /**
     * Verify field is greater than or equal to provided value
     *
     * @param mixed $field
     * @param mixed $value
     * @return bool|array|string
     */
    #[RuleAttribute('greater_than_equal')]
    public function greaterThanEqual(mixed $field, mixed $value): bool|array|string
    {
        $bc_scale = 0;
        if (is_array($value)) {
            [$value, $bc_scale] = $value;
        }
        if (is_integer($field) || is_float($field)) {
            if ($field >= $value) {
                return true;
            }
            return ['greater_than_equal', ['value' => $value]];
        }
        if (is_string($field)) {
            $value = (string) $value;
            if (bccomp($field, $value, (int) $bc_scale) >= 0) {
                return true;
            }
            return ['greater_than_equal', ['value' => $value]];
        }
        return 'numeric';
    }

    /**
     * Verify field is a decimal with specific lengths on either side of point
     *
     * @param mixed $field
     * @param array $param
     * @return string|array|bool
     */
    #[RuleAttribute('decimal')]
    public function decimal(mixed $field, mixed $param): string|array|bool
    {
        if (!is_array($param) || count($param) !== 2) {
            throw new InvalidArgumentException('Invalid parameters');
        }
        $field = (string) $field;
        [$before, $after] = $param;
        if (!str_contains($field, '.')) {
            return 'decimal_invalid_1';
        }
        [$b, $a] = explode('.', $field, 2);
        if (
            preg_match('#^(\+|-)?[0-9]+$#', $b) !== 1 ||
            preg_match('#^[0-9]+$#', $a) !== 1 ||
            ($before !== '*' && strlen($b) > $before) ||
            ($after !== '*' && strlen($a) > $after)
        ) {
            return ['decimal_invalid_2', [
                'left' => ($before === '*' ? 'X*' : str_repeat('X', $before)),
                'right' => ($after === '*' ? 'X*' : str_repeat('X', $after))
            ]];
        }
        return true;
    }
}

<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Closure;
use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\{Rule, Rules, Validator};
use Core\Exceptions\AppException;
use InvalidArgumentException;

/**
 * Class DynamicRule
 *
 * @package Core\Components\Validation\Rules
 */
class DynamicRule extends Rule
{
    /**
     * Run rule set returned from passed closure
     *
     * @param mixed $field
     * @param string|Closure $closure
     * @param Validator $validator
     * @param ...$args
     * @return bool|int|string|array
     * @throws AppException
     */
    #[RuleAttribute('dynamic_rules')]
    public function dynamicRules(mixed $field, string|Closure $closure, Validator $validator, ...$args): bool|int|string|array
    {
        if (is_string($closure)) {
            $closure = $validator->getConfig()->storage($closure);
        }
        if (!is_object($closure) || !($closure instanceof Closure)) {
            throw new InvalidArgumentException('Param must be a closure or a string linked to a closure in storage');
        }
        $rules = $closure($field, $validator, $this);
        if ($rules === true) {
            return true;
        }
        if ($rules === Rules::STOP) {
            return Rules::STOP;
        }
        array_unshift($args, $validator);
        if (!is_array($rules)) {
            $rules = Rules::parse($rules);
        }
        if (count($rules) === 0) {
            throw new AppException('No rules returned from closure');
        }
        foreach ($rules as $rule => $rule_param) {
            if (($message_key = $this->rules->run($rule, $field, $rule_param, ...$args)) === true) {
                continue;
            }
            return $message_key;
        }
        return true;
    }
}

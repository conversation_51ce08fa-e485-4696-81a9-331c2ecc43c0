<?php

declare(strict_types=1);

namespace Core\Components\Validation\Rules;

use Core\Components\Validation\Attributes\RuleAttribute;
use Core\Components\Validation\Classes\Rule;
use InvalidArgumentException;

/**
 * Class LocationRule
 *
 * @package Core\Components\Validation\Rules
 */
class LocationRule extends Rule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return [
            'coordinate_latitude' => '{label} is not a valid latitude',
            'coordinate_longitude' => '{label} is not a valid longitude'
        ];
    }

    /**
     * Verify format of coordinate value
     *
     * @param mixed $field
     * @param string $type
     * @return string|bool
     */
    #[RuleAttribute('coordinate')]
    public function coordinate(mixed $field, string $type): string|bool
    {
        switch ($type) {
            case 'latitude':
                if (
                    preg_match(
                        '#^(\+|-)?(?:90(?:(?:\.0{1,})?)|(?:[0-9]|[1-8][0-9])(?:(?:\.[0-9]{1,})?))$#',
                        (string) $field
                    ) !== 1
                ) {
                    return 'coordinate_latitude';
                }
                break;
            case 'longitude':
                if (
                    preg_match(
                        '#^(\+|-)?(?:180(?:(?:\.0{1,})?)|(?:[0-9]|[1-9][0-9]|1[0-7][0-9])(?:(?:\.[0-9]{1,})?))$#',
                        (string) $field
                    ) !== 1
                ) {
                    return 'coordinate_longitude';
                }
                break;
            default:
                throw new InvalidArgumentException('Invalid coordinate type');
        }
        return true;
    }
}

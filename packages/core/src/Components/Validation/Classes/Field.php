<?php

declare(strict_types=1);

namespace Core\Components\Validation\Classes;

use Core\Classes\Arr;
use InvalidArgumentException;

/**
 * Class Field
 *
 * @package Core\Components\Validation\Classes
 */
class Field
{
    protected const RULE_SET_DEFAULT = 1;
    protected const RULE_SET_GROUP = 2;

    public const ACTION_OVERWRITE = 1;
    public const ACTION_PREPEND = 2;
    public const ACTION_APPEND = 3;
    public const ACTION_MERGE = 4;
    public const ACTION_MERGE_RECURSIVE = 5;

    /**
     * @var string|null Name of field
     */
    protected ?string $name = null;

    /**
     * @var string|null Key of field if in JSON
     */
    protected ?string $key = null;

    /**
     * @var string|null Label of field used for things like error messages
     */
    protected ?string $label = null;

    /**
     * @var array Storage of rules keyed by set type
     */
    protected array $rules = [
        self::RULE_SET_DEFAULT => [],
        self::RULE_SET_GROUP => []
    ];

    /**
     * @var array Arbitrary data stored with field
     */
    protected array $data = [];

    /**
     * Set field name and optionally it's key
     *
     * @param string $name
     * @param string|null $key
     * @return $this
     */
    public function name(string $name, ?string $key = null): self
    {
        $this->name = $name;
        if ($key !== null) {
            $this->key($key);
        }
        return $this;
    }

    /**
     * Get field name
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * Set field key
     *
     * @param string $key
     * @return $this
     */
    public function key(string $key): self
    {
        $this->key = $key;
        return $this;
    }

    /**
     * Get field key
     *
     * @return null|string
     */
    public function getKey(): ?string
    {
        return $this->key ?? $this->name;
    }

    /**
     * Set field label
     *
     * @param string $label
     * @return $this
     */
    public function label(string $label): self
    {
        $this->label = $label;
        return $this;
    }

    /**
     * Get field label or defined default value is none is defined
     *
     * @param string|null $default
     * @return string|null
     */
    public function getLabel(?string $default = null): ?string
    {
        return $this->label ?? $default;
    }

    /**
     * Set/prepend/append/merge rules for various rule sets
     *
     * @param int $set
     * @param string|array $rules
     * @param int $action
     */
    protected function handleRules(int $set, string|array $rules, int $action): void
    {
        if (is_string($rules)) {
            $rules = Rules::parse($rules);
        }
        if (!is_array($rules)) {
            throw new InvalidArgumentException('Rules must be an array');
        }
        switch ($action) {
            case self::ACTION_OVERWRITE:
                $this->rules[$set] = $rules;
                break;
            case self::ACTION_PREPEND:
                $this->rules[$set] = $rules + $this->rules[$set];
                break;
            case self::ACTION_APPEND:
                $this->rules[$set] = $this->rules[$set] + $rules;
                break;
            case self::ACTION_MERGE:
                $this->rules = array_merge($this->rules[$set], $rules);
                break;
            case self::ACTION_MERGE_RECURSIVE:
                $this->rules = Arr::mergeRecursiveDistinct($this->rules[$set], $rules);
                break;
            default:
                throw new InvalidArgumentException('Invalid action');
        }
    }

    /**
     * Set group rules
     *
     * @param string|array $rules
     * @param int $action
     * @return $this
     */
    public function groupRules(string|array $rules, int $action = self::ACTION_OVERWRITE): self
    {
        $this->handleRules(self::RULE_SET_GROUP, $rules, $action);
        return $this;
    }

    /**
     * Set field rules
     *
     * @param string|array $rules
     * @param int $action
     * @return $this
     */
    public function rules(string|array $rules, int $action = self::ACTION_OVERWRITE): self
    {
        $this->handleRules(self::RULE_SET_DEFAULT, $rules, $action);
        return $this;
    }

    /**
     * Check if field has group rules
     *
     * @return bool
     */
    public function hasGroupRules(): bool
    {
        return count($this->rules[self::RULE_SET_GROUP]) > 0;
    }

    /**
     * Check if field has rules
     *
     * @return bool
     */
    public function hasRules(): bool
    {
        return count($this->rules[self::RULE_SET_DEFAULT]) > 0;
    }

    /**
     * Check if field has a specific group rule
     *
     * @param string $rule
     * @return bool
     */
    public function hasGroupRule(string $rule): bool
    {
        return isset($this->rules[self::RULE_SET_GROUP][$rule]);
    }

    /**
     * Check if field has a specific rule
     *
     * @param string $rule
     * @return bool
     */
    public function hasRule(string $rule): bool
    {
        return isset($this->rules[self::RULE_SET_DEFAULT][$rule]);
    }

    /**
     * Set/prepend/append/merge individual rule configs
     *
     * @param int $set
     * @param string $rule
     * @param mixed $config
     * @param int $action
     */
    protected function handleRule(int $set, string $rule, mixed $config, int $action): void
    {
        if ($action !== self::ACTION_OVERWRITE && !isset($this->rules[$set][$rule])) {
            $action = self::ACTION_OVERWRITE;
        }
        switch ($action) {
            case self::ACTION_OVERWRITE:
                $this->rules[$set][$rule] = $config;
                break;
            case self::ACTION_PREPEND:
                if (is_array($this->rules[$set][$rule])) {
                    if (!is_array($config)) {
                        $config = [$config];
                    }
                    $this->rules[$set][$rule] = $config + $this->rules[$set][$rule];
                    break;
                }
                $this->rules[$set][$rule] = $config . $this->rules[$set][$rule];
                break;
            case self::ACTION_APPEND:
                if (is_array($this->rules[$set][$rule])) {
                    if (!is_array($config)) {
                        $config = [$config];
                    }
                    $this->rules[$set][$rule] = $this->rules[$set][$rule] + $config;
                    break;
                }
                $this->rules[$set][$rule] .= $config;
                break;
            case self::ACTION_MERGE:
                if (!is_array($this->rules[$set][$rule])) {
                    throw new InvalidArgumentException('Cannot use merge action with non array value');
                }
                $this->rules[$set][$rule] = array_merge($this->rules[$set][$rule], $config);
                break;
            case self::ACTION_MERGE_RECURSIVE:
                if (!is_array($this->rules[$set][$rule])) {
                    throw new InvalidArgumentException('Cannot use merge action with non array value');
                }
                $this->rules[$set][$rule] = Arr::mergeRecursiveDistinct($this->rules[$set][$rule], $config);
                break;
            default:
                throw new InvalidArgumentException('Invalid action');
        }
    }

    /**
     * Set specific group rule
     *
     * @param string $rule
     * @param mixed $config
     * @param int $action
     * @return $this
     */
    public function groupRule(string $rule, mixed $config, int $action = self::ACTION_OVERWRITE): self
    {
        $this->handleRule(self::RULE_SET_GROUP, $rule, $config, $action);
        return $this;
    }

    /**
     * Set specific field rule
     *
     * @param string $rule
     * @param mixed $config
     * @param int $action
     * @return $this
     */
    public function rule(string $rule, mixed $config, int $action = self::ACTION_OVERWRITE): self
    {
        $this->handleRule(self::RULE_SET_DEFAULT, $rule, $config, $action);
        return $this;
    }

    /**
     * Get all group rules
     *
     * @return array
     */
    public function getGroupRules(): array
    {
        return $this->rules[self::RULE_SET_GROUP];
    }

    /**
     * Get all rules
     *
     * @return array
     */
    public function getRules(): array
    {
        return $this->rules[self::RULE_SET_DEFAULT];
    }

    /**
     * Set or override field data
     *
     * @param array $data
     * @return $this
     */
    public function data(array $data): self
    {
        $this->data = $data;
        return $this;
    }

    /**
     * Set data value via dot notation
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function set(string $key, mixed $value): self
    {
        Arr::set($this->data, $key, $value);
        return $this;
    }

    /**
     * Get data value via dot notation
     *
     * @param string|null $key
     * @param mixed $default
     * @return mixed
     */
    public function get(?string $key = null, mixed $default = null): mixed
    {
        return Arr::get($this->data, $key, $default);
    }

    /**
     * Convert field to an array
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'name' => $this->getName(),
            'key' => $this->getKey(),
            'label' => $this->getLabel(),
            'rules' => $this->getRules(),
            'group_rules' => $this->getGroupRules(),
            'data' => $this->get()
        ];
    }

    /**
     * Create field instance from an array
     *
     * @param array $data
     * @return static
     */
    public static function fromArray(array $data): static
    {
        $field = new static();
        $keys = ['name' => 'name', 'key' => 'key', 'label' => 'label', 'rules' => 'rules', 'group_rules' => 'groupRules'];
        foreach ($keys as $key => $func) {
            if (!isset($data[$key])) {
                continue;
            }
            $field->{$func}($data[$key]);
        }
        $other = Arr::except($data, array_keys($keys));
        $field->data($other);
        return $field;
    }
}

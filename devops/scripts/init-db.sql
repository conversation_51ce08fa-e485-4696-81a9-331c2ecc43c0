-- Create databases
CREATE DATABASE IF NOT EXISTS cxlratr_app;
CREATE DATABASE IF NOT EXISTS cxlratr_import;
CREATE DATABASE IF NOT EXISTS cxlratr_utility;

-- Create user and grant privileges
CREATE USER IF NOT EXISTS 'vagrant'@'%' IDENTIFIED BY 'vagrant';
GRANT ALL PRIVILEGES ON cxlratr_app.* TO 'vagrant'@'%';
GRANT ALL PRIVILEGES ON cxlratr_import.* TO 'vagrant'@'%';
GRANT ALL PRIVILEGES ON cxlratr_utility.* TO 'vagrant'@'%';

FLUSH PRIVILEGES;

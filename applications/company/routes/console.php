<?php

use Core\Components\Console\StaticAccessors\Command;

// google commands
Command::add('google:calendar:sync-stale', App\Console\Commands\GoogleCommand::class, 'syncStaleCalendars');
Command::add('google:calendar:expand-sync-windows', App\Console\Commands\GoogleCommand::class, 'expandSyncWindows');
Command::add('google:notification-channel:renew-all', App\Console\Commands\GoogleCommand::class, 'renewNotificationChannels');
Command::add('google:notification-channel:create', App\Console\Commands\GoogleCommand::class, 'createNotificationChannel');
Command::add('google:disconnect:company', App\Console\Commands\GoogleCommand::class, 'disconnectCompany');
Command::add('google:disconnect:user', App\Console\Commands\GoogleCommand::class, 'disconnectUser');
Command::add('google:check-static-images', App\Console\Commands\GoogleCommand::class, 'checkStaticImages');
Command::add('google:clean-static-image-cache', App\Console\Commands\GoogleCommand::class, 'cleanStaticImageCache');

// import commands
Command::add('import:export-template', App\Console\Commands\ImportCommand::class, 'exportTemplate');
Command::add('import:ingest', App\Console\Commands\ImportCommand::class, 'ingest');
Command::add('import:run', App\Console\Commands\ImportCommand::class, 'run');

// company commands
Command::add('company:change-subscription', App\Console\Commands\CompanyCommand::class, 'changeSubscription');
Command::add('company:handle-dormant', App\Console\Commands\CompanyCommand::class, 'handleDormant');
Command::add('company:handle-expired', App\Console\Commands\CompanyCommand::class, 'handleExpired');

// company custom report commands
Command::add('company:custom-report:create', App\Console\Commands\CompanyCustomReportCommand::class, 'create');
Command::add('company:custom-report:change-status', App\Console\Commands\CompanyCustomReportCommand::class, 'changeStatus');
Command::add('company:custom-report:run', App\Console\Commands\CompanyCustomReportCommand::class, 'run');
Command::add('company:custom-report:clean', App\Console\Commands\CompanyCustomReportCommand::class, 'clean');

// company feature commands
Command::add('company:feature:list', App\Console\Commands\CompanyFeatureCommand::class, 'listAll');
Command::add('company:feature:enable', App\Console\Commands\CompanyFeatureCommand::class, 'enable');
Command::add('company:feature:disable', App\Console\Commands\CompanyFeatureCommand::class, 'disable');
Command::add('company:feature:remove', App\Console\Commands\CompanyFeatureCommand::class, 'remove');

// company invoice commands
Command::add('company:invoice:create-credit', App\Console\Commands\CompanyInvoiceCommand::class, 'createCredit');
Command::add('company:invoice:create', App\Console\Commands\CompanyInvoiceCommand::class, 'create');
Command::add('company:invoice:change-status', App\Console\Commands\CompanyInvoiceCommand::class, 'changeStatus');

// company subscription commands
Command::add('company:subscription:activate', App\Console\Commands\CompanySubscriptionCommand::class, 'activate');
Command::add('company:subscription:bill-all', App\Console\Commands\CompanySubscriptionCommand::class, 'billAll');
Command::add('company:subscription:cancel', App\Console\Commands\CompanySubscriptionCommand::class, 'cancel');
Command::add('company:subscription:change-trial-date', App\Console\Commands\CompanySubscriptionCommand::class, 'changeTrialDate');
Command::add('company:subscription:suspend-delinquent', App\Console\Commands\CompanySubscriptionCommand::class, 'suspendDelinquent');

// form commands
Command::add('system-form:setup', App\Console\Commands\SystemFormCommand::class, 'setup');
Command::add('system-form:import', App\Console\Commands\SystemFormCommand::class, 'import');
Command::add('system-form:export', App\Console\Commands\SystemFormCommand::class, 'export');
Command::add('company-form:import', App\Console\Commands\CompanyFormCommand::class, 'import');
Command::add('company-form:export', App\Console\Commands\CompanyFormCommand::class, 'export');

Command::add('system-form:category:list', App\Console\Commands\SystemFormCategoryCommand::class, 'list');
Command::add('system-form:category:add', App\Console\Commands\SystemFormCategoryCommand::class, 'add');
Command::add('system-form:category:archive', App\Console\Commands\SystemFormCategoryCommand::class, 'archive');
Command::add('system-form:category:attach', App\Console\Commands\SystemFormCategoryCommand::class, 'attach');
Command::add('system-form:category:detach', App\Console\Commands\SystemFormCategoryCommand::class, 'detach');
// future form:item:category-attach

// cleanup commands
Command::add('cleanup:api-request-log', App\Console\Commands\CleanupCommand::class, 'apiRequestLog');
Command::add('cleanup:google-calendar-push-notification-log', App\Console\Commands\CleanupCommand::class, 'googleCalendarPushNotificationLog');
Command::add('cleanup:mailgun-webhook-log', App\Console\Commands\CleanupCommand::class, 'mailgunWebhookLog');
Command::add('cleanup:twilio-webhook-log', App\Console\Commands\CleanupCommand::class, 'twilioWebhookLog');

// misc commands
Command::add('utility:crypt:encrypt', App\Console\Commands\Utilities\CryptCommand::class, 'encrypt');
Command::add('utility:crypt:decrypt', App\Console\Commands\Utilities\CryptCommand::class, 'decrypt');
Command::add('utility:crypt:generate-key', App\Console\Commands\Utilities\CryptCommand::class, 'generateKey');
Command::add('utility:url:sign', App\Console\Commands\Utilities\UrlCommand::class, 'sign');

// quickbooks
Command::add('quickbooks:invoice-update', App\Console\Commands\QuickbooksCommand::class, 'invoiceUpdate');
Command::add('quickbooks:renew', App\Console\Commands\QuickbooksCommand::class, 'renew');
Command::add('quickbooks:query', App\Console\Commands\QuickbooksCommand::class, 'query');

// queue commands
Command::add('queue:worker', App\Console\Commands\QueueCommand::class, 'worker');
Command::add('queue:run-pending-jobs', App\Console\Commands\QueueCommand::class, 'runPendingJobs');
Command::add('queue:scheduled-job-worker', App\Console\Commands\QueueCommand::class, 'scheduledJobWorker');
Command::add('queue:push-scheduled-jobs', App\Console\Commands\QueueCommand::class, 'pushScheduledJobs');
Command::add('queue:restart', App\Console\Commands\QueueCommand::class, 'restartDaemons');
Command::add('queue:retry', App\Console\Commands\QueueCommand::class, 'retry');
Command::add('queue:check', App\Console\Commands\QueueCommand::class, 'check');
Command::add('queue:clear', App\Console\Commands\QueueCommand::class, 'clear');

// project commands
Command::add('project:publish-pending-events', App\Console\Commands\ProjectCommand::class, 'publishPendingEvents');

// domain commands
Command::add('domain:primary-reseller', App\Console\Commands\DomainCommand::class, 'primaryReseller');

// reseller commands
Command::add('reseller:primary-domain', App\Console\Commands\ResellerCommand::class, 'primaryDomain');

// reminder commands
Command::add('event-reminder', App\Console\Commands\EventReminderCommand::class, 'send');
Command::add('bid-follow-up', App\Console\Commands\BidFollowUpCommand::class, 'send');

// cache commands
Command::add('cache:clear-all', App\Console\Commands\CacheCommand::class, 'clearAll');
Command::add('cache:clear-routes', App\Console\Commands\CacheCommand::class, 'clearRoutes');
Command::add('cache:clear-brands', App\Console\Commands\CacheCommand::class, 'clearBrands');
Command::add('cache:clear-domains', App\Console\Commands\CacheCommand::class, 'clearDomains');
Command::add('cache:clear-templates', App\Console\Commands\CacheCommand::class, 'clearTemplates');
Command::add('cache:clear-intake-options', App\Console\Commands\CacheCommand::class, 'clearIntakeOptions');

// email commands
Command::add('email:message:resend', App\Console\Commands\EmailCommand::class, 'resendMessage');

// subscription commands
Command::add('subscription:create', App\Console\Commands\SubscriptionCommand::class, 'create');
Command::add('subscription:import', App\Console\Commands\SubscriptionCommand::class, 'import');
Command::add('subscription:export', App\Console\Commands\SubscriptionCommand::class, 'export');

// user commands
Command::add('user:access-token', App\Console\Commands\UserCommand::class, 'accessToken');

// hubspot commands
Command::add('hubspot:push-company', App\Console\Commands\HubspotCommand::class, 'pushCompany');
Command::add('hubspot:push-contact', App\Console\Commands\HubspotCommand::class, 'pushContact');

// training commands
Command::add('training:enable-user', App\Console\Commands\TrainingCommand::class, 'enableUser');
Command::add('training:enable-company-users', App\Console\Commands\TrainingCommand::class, 'enableCompanyUsers');
Command::add('training:clear-caches', App\Console\Commands\TrainingCommand::class, 'clearAllCaches');
Command::add('training:clear-static-caches', App\Console\Commands\TrainingCommand::class, 'clearStaticCaches');
Command::add('training:clear-user-caches', App\Console\Commands\TrainingCommand::class, 'clearUserCaches');

// temp zendesk command
Command::add('zendesk:push-company', App\Console\Commands\ZendeskCommand::class, 'pushCompany');

// payment commands
Command::add('payments:merchant:onboard', App\Console\Commands\PaymentMerchantCommand::class, 'onboardMerchant');
Command::add('payments:merchant:generate-signature', App\Console\Commands\PaymentMerchantCommand::class, 'generateSignature');
Command::add('payments:merchant:go-live', App\Console\Commands\PaymentMerchantCommand::class, 'goLiveMerchant');
Command::add('payments:merchant:onboard:fetch', App\Console\Commands\CoPilotMerchantStatusCommand::class, 'fetchStatus');
Command::add('payments:reconcile:settlements', App\Console\Commands\SettlementReconcilerCommand::class, 'reconcile');
Command::add('payments:reconcile:funding', App\Console\Commands\FundingReconcilerCommand::class, 'reconcile');
{"name": "cxlratr-company-app", "version": "0.1.0", "description": "", "main": "gulpfile.js", "dependencies": {"@babel/runtime": "^7.5.5", "@fancyapps/fancybox": "^3.5.2", "@fontsource/barlow": "^4.5.7", "@fontsource/roboto": "^4.5.7", "@markerjs/markerjs3": "^3.7.3", "@popperjs/core": "^2.9.3", "@rive-app/canvas": "^1.0.93", "@simonwep/pickr": "^1.5.0", "@uppy/core": "^1.12.0", "@uppy/dashboard": "1.10.2", "@uppy/webcam": "^1.6.8", "@uppy/xhr-upload": "^1.6.1", "accounting": "^0.4.1", "anchorme": "^2.1.2", "autosize": "^4.0.1", "backoff": "^2.5.0", "bootstrap-input-spinner": "^3.1.7", "card-validator": "^5.0.0", "clipboard": "^2.0.6", "codemirror": "^5.38.0", "core-js": "2.6.9", "datatables.net-buttons-zf": "^1.5.1", "datatables.net-colreorder-dt": "1.4.1", "datatables.net-fixedcolumns-dt": "^3.2.6", "datatables.net-plugins": "^1.10.15", "datatables.net-responsive-zf": "^2.2.1", "datatables.net-select-dt": "^1.2.4", "datatables.net-zf": "^1.10.16", "decimal.js": "^10.0.0", "dexie": "^2.0.4", "events": "^2.0.0", "fast-text-encoding": "^1.0.0", "file-saver": "^2.0.2", "filesize": "^3.6.1", "flatpickr": "4.6.7", "foundation-sites": "^6.7.1", "helphero": "^3.5.3", "inputmask": "^3.3.11", "interactjs": "^1.6.3", "jquery-mask-plugin": "^1.14.12", "jquery-ui": "^1.12.1", "jquery-ui-touch-punch": "^0.2.3", "mathjs": "^4.1.2", "moment-timezone": "^0.5.33", "nestedSortable": "^1.3.4", "page": "^1.11.4", "papaparse": "^5.3.0", "paper": "^0.12.4", "parsleyjs": "^2.8.0", "pdf.js": "https://github.com/mozilla/pdf.js.git", "popper.js": "^1.16.0", "process": "^0.11.10", "pubsub-js": "^1.6.0", "qs": "^6.5.1", "remixicon": "4.3.0", "rive-js": "^0.7.33", "select2": "^4.0.8", "sortablejs": "^1.7.0", "spectrum-colorpicker": "^1.8.0", "tether-drop": "^1.4.2", "tinymce": "6.3.1", "tippy.js": "^5.1.3", "util": "^0.12.3", "uuid": "^3.1.0", "validator": "^9.1.1"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.4", "babel-loader": "^8.2.2", "chalk": "^4.1.2", "clean-webpack-plugin": "^4.0.0", "css-loader": "^6.2.0", "css-minimizer-webpack-plugin": "^3.0.2", "cssnano": "^5.0.8", "del": "^6.0.0", "fibers": "^5.0.0", "glob": "^7.1.7", "glob-import-loader": "^1.1.4", "glob-parent": "^6.0.1", "handlebars": "^4.7.7", "handlebars-loader": "^1.7.1", "jquery": "^3.2.1", "lodash": "^4.17.4", "mini-css-extract-plugin": "^2.2.2", "mini-svg-data-uri": "^1.3.3", "minimist": "^1.2.5", "moment-locales-webpack-plugin": "^1.2.0", "moment-timezone-data-webpack-plugin": "^1.5.0", "postcss": "^8.3.6", "postcss-loader": "^6.1.1", "postcss-preset-env": "^6.7.0", "sass": "^1.39.0", "sass-loader": "^12.1.0", "svg-sprite-loader": "^6.0.9", "terser-webpack-plugin": "^5.2.3", "webpack": "^5.52.0", "webpack-cli": "^4.8.0", "worker-loader": "^3.0.8"}, "engines": {"node": ">=14.17"}, "scripts": {"build": "NODE_OPTIONS=--openssl-legacy-provider node --max-old-space-size=2560 build/webpack.js", "copy-files": "node build/copy-shared-files.js", "compile-sass": "node build/compile-resource-sass.js"}, "author": "Accelerated Development Group", "license": "ISC", "private": true}
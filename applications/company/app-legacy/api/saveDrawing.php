<?php

    use App\Classes\FX\API\SaveDrawing;

	header('Content-Type: application/json');
	
	if (isset($_POST["token"])) {
		$token = filter_input(INPUT_POST, 'token', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

    if (isset($_POST["drawingID"])) {
        $UUID = filter_input(INPUT_POST, 'drawingID', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    }

	if (isset($_POST["evaluationID"])) {
		$evaluationID = filter_input(INPUT_POST, 'evaluationID', FILTER_SANITIZE_NUMBER_INT);
	}

	if (isset($_POST["drawingName"])) {
		$drawingName = filter_input(INPUT_POST, 'drawingName', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["zoomLevel"])) {
		$zoomLevel = filter_input(INPUT_POST, 'zoomLevel', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["gridUnits"])) {
		$gridUnits = filter_input(INPUT_POST, 'gridUnits', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["showWallLengths"])) {
		$showWallLengths = filter_input(INPUT_POST, 'showWallLengths', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["createdAt"])) {
		$creationDate = filter_input(INPUT_POST, 'createdAt', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["createdByUserID"])) {
		$createdByUserID = filter_input(INPUT_POST, 'createdByUserID', FILTER_SANITIZE_NUMBER_INT);
	}

	if (isset($_POST["updatedAt"])) {
		$lastEdited = filter_input(INPUT_POST, 'updatedAt', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["updatedByUserID"])) {
		$lastEditedByUserID = filter_input(INPUT_POST, 'updatedByUserID', FILTER_SANITIZE_NUMBER_INT);
	}

	if (isset($_POST["inProgress"])) {
		$inProgress = filter_input(INPUT_POST, 'inProgress', FILTER_SANITIZE_NUMBER_INT);
	}

	if (isset($_POST["deviceID"])) {
	    $deviceID = filter_input(INPUT_POST, 'deviceID', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    }

	if (isset($_POST["delete"])) {
		$delete = filter_input(INPUT_POST, 'delete', FILTER_SANITIZE_NUMBER_INT);
		$object = new SaveDrawing();
		$object->setToken($token);
		$object->setUUID($UUID);
		$object->setDelete($delete);
		$object->save();
		$response = $object->getResults();
	}
	else{
		$object = new SaveDrawing();
		$object->setToken($token);
		$object->setUUID($UUID);
		$object->setEvaluationID($evaluationID);
		$object->setDrawingName($drawingName);
		$object->setZoomLevel($zoomLevel);
		$object->setGridUnits($gridUnits);
		$object->setShowWallLengths($showWallLengths);
		$object->setCreationDate($creationDate);
		$object->setCreatedByUserID($createdByUserID);
		$object->setLastEdited($lastEdited);
		$object->setLastEditedByUserID($lastEditedByUserID);
		$object->setInProgress($inProgress);
		$object->setDeviceID($deviceID);
		$object->save();
		$response = $object->getResults();
	}

	echo json_encode($response);
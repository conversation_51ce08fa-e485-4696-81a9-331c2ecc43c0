<?php

use App\Classes\FX\API\Authenticate;
use Core\StaticAccessors\Path;

header('Content-Type: application/json');
/*
cid = company id
uid = user id
pid = project id
eid = evaluation id

Location by type:

projectdoc Project Documents - assets/company/{cid}/projects/{pid}/documents/{name}
evalimage Evaluation Images - assets/company/{cid}/projects/{pid}/evaluations/{eid}/images/{name}
evaldrawing Evaluation Drawings - assets/company/{cid}/projects/{pid}/evaluations/{eid}/drawings/{name}
evaldoc Evaluation Documents -	assets/company/{cid}/projects/{pid}/evaluations/{eid}/documents/{name}

document location using getDocument.php:
Project Documents - getDocument.php?type=projectdoc&pid={pid}&name={name}
Evaluation Photos - getDocument.php?type=evalphoto&pid={pid}&eid={eid}&name={name}
Evaluation Drawings - getDocument.php?type=evaldrawing&pid={pid}&eid={eid}&name={name}
Evaluation Documents -	getDocument.php?type=evaldoc&pid={pid}&eid={eid}&name={name}
*/


	function showFile($filePath){
		// open the file in a binary mode
		if (file_exists($filePath)) {
			$file = fopen($filePath, 'rb');

			// send the right headers
			header("Content-Type: " . mime_content_type($filePath));
			header("Content-Length: " . filesize($filePath)); 

			// dump the picture and stop the script
			fpassthru($file); 
			exit;
		}
		else{
			echo 'no file';
		}
	}

	if(isset($_GET['token'])) {
		$token = filter_input(INPUT_GET, 'token', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	$object = new Authenticate();
	$object->setToken($token);
	$object->getCompanyID();
	$response = $object->getResults();
	
	if (empty($response) || $response['message'] != 'success'){
		echo $response['message'];
	}
	else{

		$companyID = $response['companyID'];

		if(isset($_GET['name'])) {
			$name = filter_input(INPUT_GET, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
			$name = htmlspecialchars_decode($name);
		}

		if(isset($_GET['eid'])) {
			$evaluationID = filter_input(INPUT_GET, 'eid', FILTER_SANITIZE_NUMBER_INT);
		}

		if(isset($_GET['pid'])) {
			$projectID = filter_input(INPUT_GET, 'pid', FILTER_SANITIZE_NUMBER_INT);
		}

		if(isset($_GET['type'])) {
			$type = filter_input(INPUT_GET, 'type', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
		}

		//get path to file by type
		$path = NULL;

		if ($type == 'projectdoc'){
			$path = Path::projectDocument($name, $companyID, $projectID);
			showFile($path);
		}
		else if ($type == 'evalphoto'){
		    $path = Path::evaluationImage($name, $companyID, $projectID, $evaluationID);
			showFile($path);
		}
		else if ($type == 'evaldrawing'){
		    $path = Path::evaluationDrawing($name, $companyID, $projectID, $evaluationID);
			showFile($path);
		}
		else if ($type == 'evaldoc'){
		    $path = Path::evaluationDocument($name, $companyID, $projectID, $evaluationID);
			showFile($path);
		}
		else{
			echo 'File not found ';
		}
	}
?>
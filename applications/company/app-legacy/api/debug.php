<?php

use App\Classes\Log;
use App\Exceptions\LegacyApiException;
use Common\Models\User;

$json = [
    'status' => 1
];
try {
    $log = Log::create('api_debug', [
        'email' => [
            'subject' => 'Repair Plan [debug]'
        ],
        'slack' => [
            'channel' => '#repair-plan-app',
            'username' => 'api-debug'
        ],
        'file' => 'api_debug.log',
        'minimum_levels' => [
            'email' => Log::DEBUG,
            'slack' => Log::DEBUG
        ]
    ]);
    $token = trim(Input::post('token', ''));
    if (strlen($token) === 0) {
        throw new LegacyApiException(1, 'User token is required');
    }
    if (($user = User::where('token', $token)->first()) === null) {
        throw new LegacyApiException(2, 'User not found');
    }
    $user_debug_path = Path::get('userAppDebug', $user->companyID, $user->userID);
    if (!is_dir($user_debug_path) && !mkdir($user_debug_path, 0755, true)) {
        throw new LegacyApiException(3, 'Unable to create app debug directory for user');
    }
    if (($db = Input::upload('db', $user_debug_path)) === false) {
        throw new LegacyApiException(4, 'Unable to upload db file');
    }
    $log->debug('User has sent debug data', [
        'user_id' => $user->userID,
        'file' => $db['file']
    ]);
} catch (LegacyApiException $e) {
    $json['status'] = 0;
    $json['error'] = [
        'code' => $e->getCode(),
        'message' => $e->getMessage()
    ];
}

header('Content-Type: application/json');
echo json_encode($json);

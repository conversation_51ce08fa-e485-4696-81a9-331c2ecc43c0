<?php

use App\Exceptions\LegacyApiException;
use Carbon\Carbon;

$json = ['status' => 1];
try {
    $token = Input::get('token', '');
    if (strlen($token) === 0) {
        throw new LegacyApiException(1, 'Token not specified');
    }
    $user = DB::select("SELECT user.*
FROM user
  JOIN companies ON user.companyID = companies.companyID
WHERE user.token = ?
      AND companies.isActive = '1'
      AND user.userActive = '1'
LIMIT 1", [$token]);
    $user = collect($user)->first();
    if ($user === null) {
        throw new LegacyApiException(2, 'User not found');
    }
    $drawing_id = Input::get('drawingID');
    if ($drawing_id !== null && preg_match('#^[A-F0-9]{32}$#', $drawing_id) !== 1) {
        throw new LegacyApiException(3, 'Drawing ID is not valid');
    }

    $with_nodes = (Input::get('withNodes', 'false') === 'true');
    $drawings = DB::table('appDrawing as d')
        ->select('d.*')
        ->join('evaluation as e', 'e.evaluationID', '=', 'd.evaluationID')
        ->join('project as p_1', 'p_1.projectID', '=', 'e.projectID')
        ->join('property as p_2', 'p_2.propertyID', '=', 'p_1.propertyID')
        ->join('customer as c', 'c.customerID', '=', 'p_2.customerID')
        ->where('c.companyID', $user->companyID);
    // @todo add date boundings from input
    if ($drawing_id !== null) {
        $drawings->where('d.drawingID', hex2bin($drawing_id));
    }
    $drawings = collect($drawings->get());
    if ($drawing_id !== null && $drawings->count() === 0) {
        throw new LegacyApiException(4, 'Unable to find drawing');
    }

    $drawing_ids = $drawings->pluck('drawingID');

    // eager load node data if necessary
    $relations = [
        'compasses' => Common\Models\DrawingCompass::class,
        'customServices' => Common\Models\DrawingCustomService::class,
        'deadMans' => Common\Models\DrawingDeadMan::class,
        'floorCracks' => Common\Models\DrawingFloorCrack::class,
        'interiorDrains' => Common\Models\DrawingInteriorDrain::class,
        'interiorPiers' => Common\Models\DrawingInteriorPier::class,
        'mudjackings' => Common\Models\DrawingMudjacking::class,
        'notes' => Common\Models\DrawingNote::class,
        'piers' => Common\Models\DrawingPier::class,
        'polyFoams' => Common\Models\DrawingPolyFoam::class,
        'soilAnchors' => Common\Models\DrawingSoilAnchor::class,
        'sumpPumps' => Common\Models\DrawingSumpPump::class,
        'supportPosts' => Common\Models\DrawingSupportPost::class,
        'walls' => Common\Models\DrawingWall::class,
        'wallBraces' => Common\Models\DrawingWallBrace::class,
        'wallCracks' => Common\Models\DrawingWallCrack::class
    ];
    $node_data = [];
    if ($with_nodes) {
        foreach ($relations as $relation => $model) {
            $nodes = $model::query()->whereIn('drawingID', $drawing_ids)->get();
            $node_data[$relation] = $nodes->groupBy('drawingID');
        }
    }

    $_drawings = [];
    foreach ($drawings as $drawing) {
        if ($with_nodes) {
            $drawing->nodes = [];
            foreach ($relations as $relation => $model) {
                if (!isset($node_data[$relation][$drawing->drawingID])) {
                    $drawing->nodes[$relation] = [];
                    continue;
                }
                $drawing->nodes[$relation] = $node_data[$relation][$drawing->drawingID];
            }
        }
        $drawing->drawingID = strtoupper(bin2hex($drawing->drawingID));
        $drawing->deviceID = strtoupper(bin2hex($drawing->deviceID));
        $_drawings[] = $drawing;
    }
    $json['result'] = [
        'drawings' => $_drawings,
        'sync_time' => Carbon::now('UTC')->format("Y-m-d H:i:s")
    ];
} catch (LegacyApiException $e) {
    $json['error'] = ['code' => $e->getCode(), 'message' => $e->getMessage()];
    $json['status'] = 0;
}

header('Content-Type: application/json; charset=utf-8');
echo json_encode($json, JSON_UNESCAPED_UNICODE);

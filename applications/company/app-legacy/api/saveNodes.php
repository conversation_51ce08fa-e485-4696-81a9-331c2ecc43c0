<?php

use App\Classes\Log;
use App\Exceptions\LegacyApiException;
use Common\Models\DrawingCompass;
use Common\Models\DrawingCustomService;
use Common\Models\DrawingDeadMan;
use Common\Models\DrawingFloorCrack;
use Common\Models\DrawingInteriorDrain;
use Common\Models\DrawingInteriorPier;
use Common\Models\DrawingMudjacking;
use Common\Models\DrawingNote;
use Common\Models\DrawingPier;
use Common\Models\DrawingPolyFoam;
use Common\Models\DrawingSoilAnchor;
use Common\Models\DrawingSumpPump;
use Common\Models\DrawingSupportPost;
use Common\Models\DrawingWall;
use Common\Models\DrawingWallBrace;
use Common\Models\DrawingWallCrack;
use Carbon\Carbon;

// @todo remove once entire project is UTC based
date_default_timezone_set('UTC');

$types = [
    'compass' => [
        'label' => 'Compass',
        'class' => DrawingCompass::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'zRotation' => [
                'label' => 'Z Rotation',
                'rules' => [
                    'numeric' => true
                ]
            ]
        ]
    ],
    'customServices' => [
        'label' => 'Custom Service',
        'class' => DrawingCustomService::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'deadMans' => [
        'label' => 'Deadman Anchor',
        'class' => DrawingDeadMan::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'zRotation' => [
                'label' => 'Z Rotation',
                'rules' => [
                    'numeric' => true
                ]
            ]
        ]
    ],
    'floorCracks' => [
        'label' => 'Floor Crack',
        'class' => DrawingFloorCrack::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'interiorDrains' => [
        'label' => 'Interior Drain',
        'class' => DrawingInteriorDrain::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'width' => [
                'label' => 'Width',
                'rules' => [
                    'integer' => true
                ]
            ],
            'height' => [
                'label' => 'Height',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'interiorPiers' => [
        'label' => 'Interior Pier',
        'class' => DrawingInteriorPier::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'pierNumber' => [
                'label' => 'Pier Number',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'mudjackings' => [
        'label' => 'Mudjacking',
        'class' => DrawingMudjacking::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'width' => [
                'label' => 'Width',
                'rules' => [
                    'integer' => true
                ]
            ],
            'height' => [
                'label' => 'Height',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'notes' => [
        'label' => 'Note',
        'class' => DrawingNote::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'zRotation' => [
                'label' => 'Z Rotation',
                'rules' => [
                    'numeric' => true
                ]
            ],
            'text' => [
                'label' => 'Text',
                'rules' => [
                    'string' => true,
                    'lengthLessThan' => 250
                ]
            ],
            'fontSize' => [
                'label' => 'Font Size',
                'rules' => [
                    'integer' => true
                ]
            ],
            'color' => [
                'label' => 'Text',
                'rules' => [
                    'color' => true
                ]
            ]
        ]
    ],
    'piers' => [
        'label' => 'Pier',
        'class' => DrawingPier::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'pierNumber' => [
                'label' => 'Pier Number',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'polyFoams' => [
        'label' => 'Poly Foam',
        'class' => DrawingPolyFoam::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'width' => [
                'label' => 'Width',
                'rules' => [
                    'integer' => true
                ]
            ],
            'height' => [
                'label' => 'Height',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'soilAnchors' => [
        'label' => 'Wall Anchor',
        'class' => DrawingSoilAnchor::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'zRotation' => [
                'label' => 'Z Rotation',
                'rules' => [
                    'numeric' => true
                ]
            ]
        ]
    ],
    'sumpPumps' => [
        'label' => 'Sump Pump',
        'class' => DrawingSumpPump::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'supportPosts' => [
        'label' => 'Support Post',
        'class' => DrawingSupportPost::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'wallBraces' => [
        'label' => 'Wall Brace',
        'class' => DrawingWallBrace::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'zRotation' => [
                'label' => 'Z Rotation',
                'rules' => [
                    'numeric' => true
                ]
            ]
        ]
    ],
    'wallCracks' => [
        'label' => 'Wall Crack',
        'class' => DrawingWallCrack::class,
        'fields' => [
            'xPos' => [
                'label' => 'X Position',
                'rules' => [
                    'integer' => true
                ]
            ],
            'yPos' => [
                'label' => 'Y Position',
                'rules' => [
                    'integer' => true
                ]
            ]
        ]
    ],
    'walls' => [
        'label' => 'Wall',
        'class' => DrawingWall::class,
        'fields' => [
            'x1' => [
                'label' => 'x1',
                'rules' => [
                    'integer' => true
                ]
            ],
            'y1' => [
                'label' => 'y1',
                'rules' => [
                    'integer' => true
                ]
            ],
            'x2' => [
                'label' => 'x2',
                'rules' => [
                    'integer' => true
                ]
            ],
            'y2' => [
                'label' => 'y2',
                'rules' => [
                    'integer' => true
                ]
            ],
            'lineColor' => [
                'label' => 'Line Color',
                'rules' => [
                    'color' => true
                ]
            ]
        ]
    ]
];

function validate($rules, $subject)
{
    foreach ($rules as $rule => $expected_result) {
        if (is_object($rule) && $rule instanceof Closure) {
            return $rule($subject);
        }
        switch ($rule) {
            case 'required':
                if ($subject === '' || $subject === null) {
                    return false;
                }
                break;
            case 'integer':
                if (preg_match('/^-?[0-9]+$/', $subject) !== 1) {
                    return false;
                }
                break;
            case 'numeric':
                if (!is_numeric($subject)) {
                    return false;
                }
                break;
            case 'string':
                if (!is_string($subject)) {
                    return false;
                }
                break;
            case 'lengthLessThan':
                if (!(strlen($subject) <= $rules[$rule])) {
                    return false;
                }
                break;
            case 'color':
                if (preg_match('/^#(?:[0-9a-fA-F]{3}){1,2}$/', $subject) !== 1) {
                    return false;
                }
                break;
        }
    }
    // TODO: Validate field existence
    return true;
}

$log = Log::create('api_save_nodes', [
    'email' => [
        'enabled' => false
    ],
    'slack' => [
        'enabled' => false
    ],
    'file' => 'api_save_nodes.log'
]);

$json = ['status' => 1];
try {
    $token = trim(Input::post('token', ''));
    if (strlen($token) === 0) {
        throw new LegacyApiException(1, 'Token not specified');
    }
    $drawings = trim(Input::post('drawings', ''));
    if (strlen($drawings) === 0) {
        throw new LegacyApiException(2, 'No drawings specified');
    }
    if (($drawings = json_decode($drawings, true)) === null) {
        throw new LegacyApiException(3, 'Unable to decode drawings JSON');
    }
    if (count($drawings) === 0) {
        throw new LegacyApiException(2, 'No drawings specified');
    }
    $user = DB::select("SELECT user.*
FROM user
  JOIN companies ON user.companyID = companies.companyID
WHERE user.token = ?
      AND companies.isActive = '1'
      AND user.userActive = '1'
LIMIT 1", [$token]);
    $user = collect($user)->first();
    if ($user === null) {
        throw new LegacyApiException(4, 'User not found');
    }
    $results = [];
    foreach ($drawings as $drawingID => $node_data) {
        if (!isset($results[$drawingID])) {
            $results[$drawingID] = [];
        }
        $bin_drawingID = hex2bin($drawingID);
        try {
            $sql =<<<SQL
SELECT companies.* FROM appDrawing
JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
JOIN project ON project.projectID = evaluation.projectID
JOIN customer ON customer.customerID = project.customerID
JOIN companies ON companies.companyID = customer.companyID
JOIN user ON user.companyID = companies.companyID
WHERE user.token = ?
AND appDrawing.drawingID = ?
SQL;
            $company = DB::select($sql, [$token, $bin_drawingID]);
            $company = collect($company)->first();
            if ($company === null) {
                throw new LegacyApiException(5, 'User does not have access to drawing');
            }
            $result = [];
            foreach ($node_data['types'] as $type => $actions) {
                foreach ($actions as $action => $nodes) {
                    foreach ($nodes as $node) {
                        try {
                            if (!isset($node['nodeID'])) {
                                throw new LegacyApiException(6, 'Node ID not defined');
                            }
                            if (!is_numeric($node['nodeID'])) {
                                throw new LegacyApiException(7, 'Node ID must be numeric');
                            }
                            switch ($action) {
                                case 'create':
                                case 'update':
                                    $fields = [];
                                    foreach ($types[$type]['fields'] as $field_name => $field) {
                                        if (!isset($node[$field_name])) {
                                            if ($action === 'update') {
                                                continue;
                                            }
                                            throw new LegacyApiException(
                                                8,
                                                'Missing required field %s to create %s node (nodeID %d, drawingID %s)',
                                                $field_name,
                                                $types[$type]['label'],
                                                $node['nodeID'],
                                                $drawingID
                                            );
                                        }
                                        if (!validate($field['rules'], $node[$field_name])) {
                                            throw new LegacyApiException(
                                                9, $node[$field_name] . ' is not a valid value for %s', $field_name
                                            );
                                        }
                                        $fields[$field_name] = $node[$field_name];
                                    }
                                    $node_model = (new $types[$type]['class'])->where('drawingID', $bin_drawingID)->where('nodeID', $node['nodeID'])->first();
                                    if ($node_model === null) {
                                        $fields['drawingID'] = $bin_drawingID;
                                        $fields['nodeID'] = $node['nodeID'];
                                        $types[$type]['class']::create($fields);
                                    }
                                    else {
                                        $node_model->fill($fields)->save();
                                    }
                                    break;
                                case 'delete':
                                    $node_model = (new $types[$type]['class'])->where('drawingID', $bin_drawingID)->where('nodeID', $node['nodeID'])->first();
                                    if ($node_model === null) {
                                        throw new LegacyApiException(10, 'Node does not exist');
                                    }
                                    $node_model->forceDelete();
                                    break;
                                default:
                                    throw new LegacyApiException(11, '%s is not a valid action', $action);
                            }
                            $result[$type][$node['nodeID']] = [
                                'status' => 1
                            ];
                        } catch (LegacyApiException $e) {
                            $context = [
                                'code' => $e->getCode(),
                                'drawingID' => $drawingID
                            ];
                            if ($context['code'] !== 6 && $context['code'] !== 7) {
                                $result[$type][$node['nodeID']] = [
                                    'status' => 0,
                                    'error' => [
                                        'code' => $code,
                                        'message' => $e->getMessage()
                                    ]
                                ];
                                $context['nodeID'] = $node['nodeID'];
                            }
                            $log->error($e->getMessage(), $context);
                        }
                    }
                }
            }
            $results[$drawingID] = [
                'status' => 1,
                'result' => $result
            ];
        } catch (LegacyApiException $e) {
            $log->error($e->getMessage(), [
                'code' => $e->getCode(),
                'drawingID' => $drawingID
            ]);
            $results[$drawingID] = [
                'status' => 0,
                'error' => [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage()
                ]
            ];
        }
    }
    $json['result'] = [
        'drawings' => $results,
        'sync_time' => Carbon::now('UTC')->format('Y-m-d H:i:s')
    ];
} catch (LegacyApiException $e) {
    $log->error($e->getMessage(), [
        'code' => $e->getCode()
    ]);
    $json['error'] = ['code' => $e->getCode(), 'message' => $e->getMessage()];
    $json['status'] = 0;
}

header('Content-Type: application/json');
echo json_encode($json);

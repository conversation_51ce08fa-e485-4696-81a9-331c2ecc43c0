APP_DEBUG=false
APP_KEY=
APP_SECURE=
APP_BASE_URL=http://app.contractoraccelerator.test

URI_KEY=

SERVER_ROLE=LOCAL
ERROR_EMAIL=<EMAIL>

DB_HOST=localhost
DB_USER=vagrant
DB_MIGRATION_USER=vagrant_full
DB_PASS=vagrant
DB_MIGRATION_PASS=vagrant
DB_NAME=cxlratr_app
DB_DEBUG=false

DB_IMPORT_HOST=localhost
DB_IMPORT_USER=vagrant
DB_IMPORT_MIGRATION_USER=vagrant_full
DB_IMPORT_PASS=vagrant
DB_IMPORT_MIGRATION_PASS=vagrant
DB_IMPORT_NAME=cxlratr_import

DB_UTILITY_HOST=localhost
DB_UTILITY_USER=vagrant
DB_UTILITY_MIGRATION_USER=vagrant_full
DB_UTILITY_PASS=vagrant
DB_UTILITY_MIGRATION_PASS=vagrant
DB_UTILITY_NAME=cxlratr_utility

REDIS_CACHE_HOST=127.0.0.1
REDIS_CACHE_PORT=6379
REDIS_CACHE_DATABASE=1

REDIS_QUEUE_HOST=127.0.0.1
REDIS_QUEUE_PORT=6379
REDIS_QUEUE_DATABASE=2

SMTP_HOST=127.0.0.1
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
SMTP_SECURE=

API_LOGGING_ENABLED=true

CORPORATE_SITE_API_ENDPOINT=http://contractoraccelerator.test/api
CORPORATE_SITE_API_TOKEN=

SLACK_ENABLED=false
SLACK_GENERAL_WEBHOOK_URL=
SLACK_GENERAL_CHANNEL=
SLACK_SUPPORT_WEBHOOK_URL=
SLACK_SUPPORT_CHANNEL=
SLACK_MAIL_WEBHOOK_URL=
SLACK_MAIL_CHANNEL=
SLACK_COMPANY_WEBHOOK_URL=
SLACK_COMPANY_CHANNEL=
SLACK_SUBSCRIPTION_WEBHOOK_URL=
SLACK_SUBSCRIPTION_CHANNEL=
SLACK_TEXT_WEBHOOK_URL=
SLACK_TEXT_CHANNEL=

AUTHORIZE_NAME=
AUTHORIZE_KEY=

GOOGLE_TAG_MANAGER_ID=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALENDAR_EVENT_WEBHOOK_URI=
GOOGLE_API_QPS=
GOOGLE_MAPS_API_KEY=
GOOGLE_API_KEY=

QUICKBOOKS_OAUTH_CLIENT_ID=
QUICKBOOKS_OAUTH_CLIENT_SECRET=

MAILGUN_API_KEY=

MAILING_LIST_ENABLED=false
MAILCHIMP_API_KEY=
MAILCHIMP_LIST_ID=
MAILCHIMP_DAYS_TO_ONBOARD=35

MARKETING_ENABLED=false
HUBSPOT_ENABLED=false
HUBSPOT_API_KEY=
HUBSPOT_API_QPS=
HUBSPOT_DEAL_SETUP_SUBSCRIPTION_STAGE=
HUBSPOT_DEAL_TRIAL_STAGE=
HUBSPOT_DEAL_ACTIVE_STAGE=
HUBSPOT_DEAL_SUSPENDED_STAGE=
HUBSPOT_DEAL_DORMANT_STAGE=

ASSET_VERSION_ENABLED=true
ASSET_VERSION=1

MAIL_LOG_BOUNCES=true

DOMAIN_ID=
BRAND_ID=

WKHTMLTOPDF_BIN=

ZEROBOUNCE_API_KEY=

TEXT_ENABLED=true
TEXT_DEBUG=true
TWILIO_TEST_ACCOUNT_SID=
TWILIO_TEST_AUTH_TOKEN=
TWILIO_TEST_FROM_NUMBER=
TWILIO_LIVE_ACCOUNT_SID=
TWILIO_LIVE_AUTH_TOKEN=
TWILIO_WEBHOOK_HOST=

HELPHERO_ENABLED=true
HELPHERO_APP_ID=
HELPHERO_USER_ID_ENV=

# Environment Setup Vars
APP_ASSET_PATH=/var/ca-app/files

MAILTRAP_SMTP_HOST=smtp.mailtrap.io
MAILTRAP_SMTP_PORT=2525
MAILTRAP_SMTP_USER=
MAILTRAP_SMTP_PASS=
MAILTRAP_SMTP_SECURE=tls

MAILGUN_SMTP_HOST=smtp.mailgun.com
MAILGUN_SMTP_PORT=587
MAILGUN_SMTP_USER=
MAILGUN_SMTP_PASS=
MAILGUN_SMTP_SECURE=tls

NGROK_DOMAIN=

TWILIO_MESSAGE_SERVICE_ID=

ZENDESK_ENABLED=
ZENDESK_SUBDOMAIN=
ZENDESK_USERNAME=
ZENDESK_TOKEN=

SENTRY_ENABLED=false
SENTRY_DSN=
SENTRY_CLI=

WISETACK_API_URL=
WISETACK_API_KEY=
WISETACK_API_SECRET=

WEBSITE_LEADS_FORM_SKIP_RECAPTCHA=false
RECAPTCHA_KEY=
RECAPTCHA_SECRET=

PAYMENTS_CARDCONNECT_GATEWAY_URL=
PAYMENTS_COPILOT_URL=
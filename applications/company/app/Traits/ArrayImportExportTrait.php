<?php

declare(strict_types=1);

namespace App\Traits;

use Closure;
use Core\Classes\Arr;
use Core\Exceptions\AppException;

const IMPORT_TYPES = [
    'scalar' => 'is_scalar',
    'int' => 'is_integer',
    'string' => 'is_string',
    'bool' => 'is_bool',
    'float' => 'is_float',
    'array' => 'is_array',
    'list' => 'is_array'
];

/**
 * Trait ArrayImportExportTrait
 *
 * @package App\Traits
 */
trait ArrayImportExportTrait
{
    /**
     * Import data into class from array
     *
     * Uses $config key as the key for the data lookup. The $config value is an array containing the type
     * (see IMPORT_TYPES constant) and the method to call with the value. This is used to keep bad data from being
     * sent to type strict methods which would result in error.
     *
     * If the custom list type is used, the data value will verified as an array before looping through and running
     * the passed closure on each element.
     *
     * @param array $config
     * @param array $data
     * @param array $aliases
     * @throws AppException
     */
    protected function importFromArray(array $config, array $data, array $aliases = []): void
    {
        foreach ($config as $key => [$type, $method]) {
            if (!isset(IMPORT_TYPES[$type])) {
                throw new AppException('Invalid type: %s', $type);
            }
            $value = Arr::get($data, $key);
            if (isset($aliases[$key][$value])) {
                $value = $aliases[$key][$value];
            }
            if ($value === null) {
                continue;
            }
            if (!(IMPORT_TYPES[$type])($value)) {
                continue;
            }
            if ($type === 'list') {
                foreach ($value as $item) {
                    if (!is_array($item)) {
                        continue;
                    }
                    $method($item);
                }
                continue;
            }
            if (is_object($method) && $method instanceof Closure) {
                $method($value);
                continue;
            }
            $this->{$method}($value);
        }
    }

    /**
     * Export class data to array format
     *
     * Allows for using dot syntax to store nested array data easily.
     *
     * If aliases config item is set and contains on of the exported fields, the map will be checked and the alias value
     * will be used if found.
     *
     * @param array $fields
     * @param array $config
     * @return array|null
     */
    protected function exportToArray(array $fields, array $config = []): ?array
    {
        $data = $config['initial'] ?? [];
        foreach ($fields as $key => $method) {
            $value = is_object($method) && $method instanceof Closure ? $method() : $this->{$method}();
            if ($value !== null && isset($config['aliases'][$key])) {
                $value = array_search($value, $config['aliases'][$key]) ?: null;
            }
            if ($value === null) {
                continue;
            }
            Arr::set($data, $key, $value);
        }
        if (($config['null_if_empty'] ?? false) && count($data) === 0) {
            return null;
        }
        return $data;
    }
}

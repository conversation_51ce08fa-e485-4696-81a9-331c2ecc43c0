<?php

namespace App\Traits\ResourceDelegate;

use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\Resource;

trait TimestampFieldsTrait
{
    public function timestampFields(FieldList $list, $with_user_ids = true, $user_name_fields = false, $sortable = false)
    {
        $list->field('created_at')
            ->typeDateTime()
            ->column('createdAt')
            ->label('Created At')
            ->immutable();

        if ($with_user_ids) {
            $list->field('created_by_user_id')
                ->column('createdByUserID', $user_name_fields)
                ->immutable();
            if ($user_name_fields) {
                $list->field('created_by_user_name')
                    ->onDemand()
                    ->query(function ($query, $scope_builder, $table_alias) {
                        return $query->leftJoin('user as cbu', 'cbu.userID', '=', "{$table_alias}.createdByUserID");
                    })
                    ->rawColumn("IF(cbu.userID IS NOT NULL, CONCAT(cbu.userFirstName, ' ', cbu.userLastName), NULL)", 'created_by_user_name')
                    ->label('Created By User Name');
            }
        }

        $list->field('updated_at')
            ->typeDateTime()
            ->column('updatedAt')
            ->label('Updated At')
            ->immutable();

        if ($with_user_ids) {
            $list->field('updated_by_user_id')
                ->column('updatedByUserID')
                ->immutable();
            if ($user_name_fields) {
                $list->field('updated_by_user_name')
                    ->onDemand()
                    ->query(function ($query, $scope_builder, $table_alias) {
                        return $query->leftJoin('user as ubu', 'ubu.userID', '=', "{$table_alias}.updatedByUserID");
                    })
                    ->rawColumn("IF(ubu.userID IS NOT NULL, CONCAT(ubu.userFirstName, ' ', ubu.userLastName), NULL)", 'updated_by_user_name')
                    ->label('Updated By User Name');
            }
        }

        if ($sortable) {
            $sortable_fields = ['created_at', 'updated_at'];
            if ($with_user_ids) {
                $sortable_fields[] = 'created_by_user_id';
                $sortable_fields[] = 'updated_by_user_id';
            }
            if ($user_name_fields) {
                $sortable_fields[] = 'created_by_user_name';
                $sortable_fields[] = 'updated_by_user_name';
            }
            $list->modify($sortable_fields, function (Field $field) {
                return $field->onAction(Resource::ACTION_SORT, function (Field $field) {
                    return $field->onDemand(false)->mutable();
                });
            });
            $list->modify($sortable_fields, function (Field $field) {
                return $field->onAction(Resource::ACTION_FILTER, function (Field $field) {
                    return $field->onDemand(false)->mutable();
                });
            });
        }
    }
}

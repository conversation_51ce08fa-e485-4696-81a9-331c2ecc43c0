<?php

use Core\Components\Http\StaticAccessors\Middleware;

Middleware::add('auth', App\Http\Middleware\AuthMiddleware::class);
Middleware::add('api-auth', App\Http\Middleware\ApiAuthMiddleware::class);
Middleware::add('no-auth', App\Http\Middleware\NoAuthMiddleware::class);
Middleware::add('redirect', App\Http\Middleware\RedirectMiddleware::class);
Middleware::add('session', Core\Components\Session\Middleware\SessionMiddleware::class);
Middleware::add('domain', App\Http\Middleware\DomainMiddleware::class);
Middleware::add('api-log', App\Http\Middleware\ApiLoggingMiddleware::class);
Middleware::add('media-auth', App\Http\Middleware\MediaAuthMiddleware::class);
Middleware::add('google-calendar', App\Http\Middleware\GoogleCalendarMiddleware::class);
Middleware::add('lead-form-api-key', App\Http\Middleware\LeadFormApiKeyMiddleware::class);
Middleware::add('api-rate-limit', App\Http\Middleware\ApiRateLimitMiddleware::class);

Middleware::group('lead-form-api', function ($stack) {
    $stack->add('api-rate-limit');
    $stack->add('lead-form-api-key');
});
Middleware::add('my-salesman-auth', App\Http\Middleware\MySalesmanAuthMiddleware::class);

Middleware::group('web', function ($stack) {
    $stack->add('session');
    $stack->add('domain');
});

Middleware::group('app-public-no-session', function ($stack) {
    $stack->add('domain');
});

Middleware::group('app-public', function ($stack) {
    $stack->group('web');
    $stack->add('no-auth');
});

Middleware::group('app', function ($stack) {
    $stack->group('web');
    $stack->add('auth');
    $stack->add('redirect');
});

Middleware::group('app-api', function ($stack) {
    $stack->group('web');
    $stack->add('auth');
});

Middleware::group('my-salesman-api', function ($stack) {
    $stack->add('my-salesman-auth');
});

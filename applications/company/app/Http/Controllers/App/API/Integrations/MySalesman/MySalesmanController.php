<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API\Integrations\MySalesman;


use App\Services\MySalesman\MySalesmanService;
use App\Services\MySalesman\Utils\MySalesmanHelper;
use Common\Models\MySalesmanCredential;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Ramsey\Uuid\Uuid;

class MySalesmanController {

    private MySalesmanHelper $helper;
    private MySalesmanService $service;

    public function __construct()
    {
        $this->service = new MySalesmanService();
        $this->helper = new MySalesmanHelper();
    }


    /**
     * Get MySalesman integration status
     *
     * @return JSONResponse
     */
    public function getIntegrationData(): JSONResponse
    {
        try {
            $user = Auth::user();
            $this->helper->checkIntegrationFeatureEnabled($user);

            $data = $this->service->getIntegrationData($user);
        } catch (\Throwable $e) {
            return $this->helper->handleException($e, 'Unable to get integration data', ['user_id' => $user->getKey()]);
        }

        return Response::json([
            'data' => $data
        ]);
    }


    /**
     * Enable MySalesman integration
     *
     * @param RequestInterface $request
     * @return JSONResponse
     */
    public function updateIntegration(RequestInterface $request): JSONResponse
    {
        try {
            $user = Auth::user();
            $this->helper->checkIntegrationFeatureEnabled($user);
            $input = $request->input()->post();

            $key = $this->service->updateIntegration($input);
        } catch (\Throwable $e) {
            return $this->helper->handleException($e, 'Unable to update integration', ['user_id' => $user->getKey()]);
        }

        return Response::json([
            'message' => 'MySalesman integration is updated',
            'data' => $key
        ]);
    }

    /**
     * Create MySalesman lead
     *
     * @param RequestInterface $request
     * @return JSONResponse
     */
    public function createLead(RequestInterface $request): JSONResponse
    {
        try {
            $token = $request->input()->header(MySalesmanCredential::API_AUTH_HEADER);
            $input = $request->input()->post();
            $lead = $this->service->createlead($input);
        } catch (\Throwable $e) {
            return $this->helper->handleException($e, 'Unexpected error while creating lead', [
                'token' => $token, 'input' => $input
            ]);
        }

        return Response::json([
            'message' => 'Lead created',
            'data' => $lead
        ]);
    }
}

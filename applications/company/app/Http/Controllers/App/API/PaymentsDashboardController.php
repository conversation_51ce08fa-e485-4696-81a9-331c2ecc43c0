<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Resources\Payment\PropelrTransactionResource;
use App\Services\Payment\PropelrPaymentService;
use App\Services\Payment\PaymentMetricService;
use App\Traits\Resource\Controller\PolyActionTrait;
use Carbon\Carbon;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Http\StaticAccessors\Request;
use Exception;

class PaymentsDashboardController
{
    use PolyActionTrait;

    protected $resource = PropelrTransactionResource::class;

    public function __construct()
    {
        $this->registerFormat('payments-dashboard-transactions-v1', 'application/vnd.adg.fx.payments-dashboard-transactions-v1+json');
    }

    /**
     * Get payment metrics for dashboard
     *
     * @return JSONResponse
     */
    public function metrics(): JSONResponse
    {
        try {
            $start_date = Request::get('start_date');
            $end_date = Request::get('end_date');

            if (!$start_date || !$end_date) {
                return Response::json([
                    'success' => false,
                    'message' => 'Start date and end date are required',
                    'error' => 'Missing required parameters'
                ], 400);
            }

            Carbon::parse($start_date);
            Carbon::parse($end_date);

            $payment_metric_service = new PaymentMetricService();
            $metrics = $payment_metric_service->getPaymentTransactionsMetrics($start_date, $end_date);

            return Response::json([
                'success' => true,
                'data' => $metrics
            ], 200);

        } catch (Exception $e) {
            return Response::json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Void a transaction
     *
     * @param string $id
     * @return Response
     */
    public function void(string $id): JSONResponse
    {
        try {
            $payment_service = new PropelrPaymentService();
            $result = $payment_service->voidTransaction($id);

            return Response::json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result
            ], 200);

        } catch (Exception $e) {
            return Response::json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Refund a transaction
     *
     * @param string $id
     * @return JSONResponse
     */
    public function refund(string $id): JSONResponse
    {
        try {
            $payment_service = new PropelrPaymentService();
            $result = $payment_service->refundTransaction($id);

            return Response::json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result
            ], 200);

        } catch (Exception $e) {
            return Response::json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => $e->getMessage()
            ], 400);
        }
    }
}
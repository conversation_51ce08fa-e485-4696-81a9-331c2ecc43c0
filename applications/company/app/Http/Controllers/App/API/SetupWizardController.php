<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Exceptions\{ApiException, Api\ForbiddenException, Api\UnprocessableEntityException};
use Carbon\Carbon;
use App\Resources\CompanyResource;
use Common\Models\CompanySetup;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\Responses\NoContentResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\RequestValidation\Classes\Validation as RequestValidation;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\FieldConfig;
use Throwable;

/**
 * Class SetupWizardController
 *
 * @package App\Http\Controllers\App\API
 */
class SetupWizardController
{
    protected $setupWizardSteps = [
        CompanyResource::SETUP_WIZARD_STEP_INSTRUCTION,
        CompanyResource::SETUP_WIZARD_STEP_GENERAL,
        CompanyResource::SETUP_WIZARD_STEP_USERS,
        CompanyResource::SETUP_WIZARD_STEP_BID_CUSTOMIZATION,
        CompanyResource::SETUP_WIZARD_STEP_EMAILS,
        CompanyResource::SETUP_WIZARD_STEP_TERMS_CONDITIONS,
        CompanyResource::SETUP_WIZARD_STEP_PRODUCTS,
        CompanyResource::SETUP_WIZARD_STEP_MEDIA,
        CompanyResource::SETUP_WIZARD_STEP_WARRANTY_PACKET,
        CompanyResource::SETUP_WIZARD_STEP_QUICKBOOKS_ONLINE,
        CompanyResource::SETUP_WIZARD_STEP_GOOGLE_CALENDAR,
        CompanyResource::SETUP_WIZARD_STEP_ADDITIONAL_SERVICES,
        CompanyResource::SETUP_WIZARD_STEP_REVIEW,
        CompanyResource::SETUP_WIZARD_STEP_COMPLETE
    ];

    protected $previous_step_lookup = [
        CompanyResource::SETUP_WIZARD_STEP_GENERAL => 'instruction',
        CompanyResource::SETUP_WIZARD_STEP_USERS => 'general',
        CompanyResource::SETUP_WIZARD_STEP_BID_CUSTOMIZATION => 'users',
        CompanyResource::SETUP_WIZARD_STEP_EMAILS => 'bidCustomization',
        CompanyResource::SETUP_WIZARD_STEP_TERMS_CONDITIONS => 'emails',
        CompanyResource::SETUP_WIZARD_STEP_PRODUCTS => 'termsConditions',
        CompanyResource::SETUP_WIZARD_STEP_MEDIA => 'products',
        CompanyResource::SETUP_WIZARD_STEP_WARRANTY_PACKET => 'media',
        CompanyResource::SETUP_WIZARD_STEP_QUICKBOOKS_ONLINE => 'warrantyPacket',
        CompanyResource::SETUP_WIZARD_STEP_GOOGLE_CALENDAR => 'quickbooksOnline',
        CompanyResource::SETUP_WIZARD_STEP_ADDITIONAL_SERVICES => 'googleCalendar',
        CompanyResource::SETUP_WIZARD_STEP_REVIEW => 'additionalServices'
    ];

    /**
     * Save step setup wizard
     *
     * @param RequestValidation $validation
     * @return JSONResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveStep(RequestValidation $validation): JSONResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $config = FieldConfig::fromArray([
                'step' => [
                    'label' => 'step',
                    'rules' => 'required|type[int]|in_array[setup_wizard_steps]'
                ],
            ]);

            $config->store('setup_wizard_steps', $this->setupWizardSteps);

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }
            $field = $this->previous_step_lookup[$validator->data('step')];
            $now = Carbon::now('UTC');
            CompanySetup::where('companyID', $user->companyID)->update([
                $field => true,
                "{$field}CompletedAt" => $now,
                'updatedAt' => $now,
                'updatedByUserID' => $user->userID
           ]);
            $company_data = Entity::make([
                'id' => $user->companyID,
                'setup_wizard_step' => $validator->data('step')
            ]);
            CompanyResource::make(Auth::acl())->partialUpdate($company_data)->run();

            return Response::json([
                "{$field}CompletedAt" => $now
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Skip step setup wizard
     *
     * @param RequestValidation $validation
     * @return NoContentResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function skipStep(RequestValidation $validation): NoContentResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $config = FieldConfig::fromArray([
                'step' => [
                    'label' => 'step',
                    'rules' => 'required|type[int]|in_array[setup_wizard_steps]'
                ],
            ]);

            $config->store('setup_wizard_steps', $this->setupWizardSteps);

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }
            $field = $this->previous_step_lookup[$validator->data('step')];
            CompanySetup::where('companyID', $user->companyID)->update([
                $field => false,
                'updatedAt' => Carbon::now('UTC'),
                'updatedByUserID' => $user->userID
            ]);
            $company_data = Entity::make([
                'id' => $user->companyID,
                'setup_wizard_step' => $validator->data('step')
            ]);
            CompanyResource::make(Auth::acl())->partialUpdate($company_data)->run();

            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Save a step in the company record
     *
     * When the user clicks to go back a step in setup wizard
     *
     * @param RequestValidation $validation
     * @return NoContentResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveCompanyStep(RequestValidation $validation): NoContentResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $config = FieldConfig::fromArray([
                'step' => [
                     'label' => 'step',
                     'rules' => 'required|type[int]|in_array[setup_wizard_steps]'
                ],
            ]);

            $config->store('setup_wizard_steps', $this->setupWizardSteps);

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $company_data = Entity::make([
                'id' => $user->companyID,
                'setup_wizard_step' => $validator->data('step')
            ]);
            CompanyResource::make(Auth::acl())->partialUpdate($company_data)->run();

            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Get All Setup Wizard data for company
     *
     * @param RequestValidation $validation
     * @return JSONResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function getAll(): JSONResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $company_scope = Scope::make()
                ->fields(
                    [
                        'name', 'address', 'address_2', 'city', 'state', 'zip', 'website', 'color', 'email_from',
                        'logo_file_id'
                    ]
                )
                ->with([
                    'setup',
                    'logo_media_urls',
                    'phones' => [
                        'fields' => ['description', 'number', 'is_primary']
                    ]
                ]);

            $company_resource = CompanyResource::make(Auth::acl());
            $company = $company_resource
                ->entity($user->companyID)
                ->scope($company_scope)
                ->run();

            $user_scope = Scope::make()
                ->fields(['first_name', 'last_name', 'email', 'bio', 'phone_number']);
            $user = $company_resource->relationResource('users')
                ->entity($user->userID)
                ->scope($user_scope)
                ->run();
            $company['user'] = $user;

            return Response::json($company);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Save general info for company
     *
     * @param RequestValidation $validation
     * @return NoContentResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveGeneral(RequestValidation $validation): NoContentResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $config = FieldConfig::fromArray([
                'email_from' => [
                    'label' => 'email from',
                    'rules' => 'trim|required|email|max_length[100]'
                ],
                'email_reply' => [
                    'label' => 'email reply',
                    'rules' => 'trim|required|email|max_length[100]'
                ],
                'website' => [
                    'label' => 'website',
                    'rules' => 'trim|max_length[255]|domain_only'
                ],
                'primary' => [
                    'label' => 'primary color',
                    'rules' => 'required|hex_color'
                ],
                'secondary' => [
                    'label' => 'secondary color',
                    'rules' => 'required|hex_color'
                ]
            ]);

            $validation->config($config);
            $validation->rules(
                function (Rules $rules) {
                    $rules->register(
                        'domain_only',
                        function (&$domain) {
                            $strs = ['http://', 'https://', '//', 'www.'];
                            foreach ($strs as $str) {
                                if (!str_starts_with($domain, $str)) {
                                    continue;
                                }
                                $domain = str_replace($str, '', $domain);
                                break;
                            }
                            return true;
                        }
                    );
                    return $rules;
                }
            );
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $now = Carbon::now('UTC');
            CompanySetup::where('companyID', $user->companyID)->update([
                'general' => true,
                'generalCompletedAt' => $now,
                'updatedAt' => $now,
                'updatedByUserID' => $user->userID
            ]);

            $company_data = Entity::make([
                'id' => $user->companyID,
                'email_from' => $validator->data('email_from'),
                'email_reply' => $validator->data('email_reply'),
                'website' => $validator->data('website'),
                'color' => $validator->data('primary'),
                'color_hover' => $validator->data('secondary'),
                'setup_wizard_step' => CompanyResource::SETUP_WIZARD_STEP_USERS
            ]);
            CompanyResource::make(Auth::acl())->partialUpdate($company_data)->run();

            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Save additional services for company
     *
     * @param RequestValidation $validation
     * @return JSONResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveAdditionalServicesData(RequestValidation $validation): JSONResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $config = FieldConfig::fromArray([
                'products_services' => [
                    'label' => 'Products & Services',
                    'rules' => 'required|type[bool]'
                ],
                'team_training' => [
                    'label' => 'Team Training',
                    'rules' => 'required|type[bool]'
                ],
                'additional_support' => [
                    'label' => 'Addditional Support',
                    'rules' => 'required|type[bool]'
                ],
                'previous_customer_data' => [
                    'label' => 'Prevous Customer Data',
                    'rules' => 'required|type[bool]'
                ]
            ]);
            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $now = Carbon::now('UTC');
            CompanySetup::where('companyID', $user->companyID)->update([
                'additionalServices' => true,
                'isProductsServices' => $validator->data('products_services'),
                'isTeamTraining' => $validator->data('team_training'),
                'isAdditionalSupport' => $validator->data('additional_support'),
                'isPreviousCustomerData' => $validator->data('previous_customer_data'),
                'additionalServicesCompletedAt' => $now,
                'updatedAt' => $now,
                'updatedByUserID' => $user->userID
            ]);

            $company_data = Entity::make([
                'id' => $user->companyID,
                'setup_wizard_step' => CompanyResource::SETUP_WIZARD_STEP_REVIEW
            ]);
            CompanyResource::make(Auth::acl())->partialUpdate($company_data)->run();

            return Response::json([
                "additionalServicesCompletedAt" => $now
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Save quickbooks online settings for company
     *
     * @param RequestValidation $validation
     * @return JSONResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveQuickbooksOnlineSettings(RequestValidation $validation): JSONResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $config = FieldConfig::fromArray([
                'quickbooks_default_service' => [
                    'label' => 'Quickbooks Default Service',
                    'rules' => 'required|type[int]'
                ],
                'quickbooks_use_invoice' => [
                    'label' => 'Use Quickbooks Invoice',
                    'rules' => 'required|type[bool]'
                ],
                'quickbooks_allow_online_credit_card_payment' => [
                    'label' => 'Show QuickBooks Credit Card Link',
                    'rules' => 'required|type[bool]'
                ],
                'quickbooks_allow_online_ach_payment' => [
                    'label' => 'Show QuickBooks ACH Link',
                    'rules' => 'required|type[bool]'
                ]
            ]);
            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $company_resource = CompanyResource::make(Auth::acl());

            $company_settings = Entity::make([
                'id' => $user->companyID,
                'settings' => [
                    'quickbooks_default_service' => $validator->data('quickbooks_default_service'),
                    'quickbooks_use_invoice' => $validator->data('quickbooks_use_invoice'),
                    'quickbooks_allow_online_credit_card_payment' => $validator->data('quickbooks_allow_online_credit_card_payment'),
                    'quickbooks_allow_online_ach_payment' => $validator->data('quickbooks_allow_online_ach_payment')
                ]
            ]);
            $company_resource->partialUpdate($company_settings)->run();

            $now = Carbon::now('UTC');
            CompanySetup::where('companyID', $user->companyID)->update([
               'quickbooksOnline' => true,
               'quickbooksOnlineCompletedAt' => $now,
               'updatedAt' => $now,
               'updatedByUserID' => $user->userID
            ]);

            $company_data = Entity::make([
                'id' => $user->companyID,
                'setup_wizard_step' => CompanyResource::SETUP_WIZARD_STEP_GOOGLE_CALENDAR
            ]);
            $company_resource->partialUpdate($company_data)->run();

            return Response::json([
                "quickbooksOnlineCompletedAt" => $now
            ]);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Complete setup wizard
     *
     * @param RequestValidation $validation
     * @return NoContentResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function completeSetupWizard(RequestValidation $validation): NoContentResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }

        try {
            $now = Carbon::now('UTC');
            CompanySetup::where('companyID', $user->companyID)->update([
               'setupComplete' => true,
               'setupCompleteCompletedAt' => $now,
               'updatedAt' => $now,
               'updatedByUserID' => $user->userID
           ]);

            $company_data = Entity::make([
                'id' => $user->companyID,
                'is_setup_wizard' => false,
                'setup_wizard_step' => CompanyResource::SETUP_WIZARD_STEP_COMPLETE
             ]);
            CompanyResource::make(Auth::acl())->partialUpdate($company_data)->run();

            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }
}

<?php

namespace App\Http\Controllers\App\API;

use App\Services\Payment\PaymentLogger;
use App\Services\Payment\ContextualPaymentLogger;
use App\Exceptions\Api\UnprocessableEntityException;
use App\Services\Payment\CoPilot\CoPilotService;
use App\Services\Payment\Exceptions\DuplicatePaymentException;
use App\Services\Payment\Exceptions\GatewayTimeoutException;
use App\Services\Payment\Exceptions\PaymentDeclinedException;
use App\Services\Payment\Propelr\Handlers\PaymentCheckoutHandler;
use App\Services\Payment\Propelr\Handlers\PaymentProcessingHandler;
use App\Services\Payment\PropelrPaymentService;
use App\Traits\Resource\Controller\ActionTrait;
use Common\Models\PropelrMerchant;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Validation;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Validator;
use Monolog\Logger;
use Carbon\Carbon;
use Ramsey\Uuid\Uuid;

class PaymentController
{
    use ActionTrait {
        retrieve as actionRetrieve;
    }

    protected ?ContextualPaymentLogger $logger = null;

    /**
     * Get logger
     *
     * @return ContextualPaymentLogger
     */
    protected function getLog(): ContextualPaymentLogger
    {
        if ($this->logger === null) {
            $this->logger = PaymentLogger::getInstance()->withContext('CONTROLLER');
        }
        return $this->logger;
    }

    /**
     * Render the checkout page using payment link token
     *
     * @param RequestInterface $request
     * @param string $shortToken
     * @return mixed
     * @throws HttpResponseException
     */
    public function showCheckoutPage(RequestInterface $request, string $shortToken)
    {
        try {
            $checkout_handler = new PaymentCheckoutHandler();
            $payment_data = $checkout_handler->prepareCheckoutData($request, $shortToken);

            return Response::view('pages.payment', ['script_data' => $payment_data])
                ->share('include_layout', false)
                ->share('layout-header', false);

        } catch (HttpResponseException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->getLog()->error('Error loading checkout page: ' . $e->getMessage());
            throw new HttpResponseException(500, 'An error occurred while loading the checkout page.');
        }
    }

    /**
     * Process payment using payment link token
     *
     * @param RequestInterface $request
     * @param string $shortToken
     * @return JSONResponse
     */
    public function chargeViaPaymentLink(RequestInterface $request, string $shortToken): JSONResponse
    {
        try {
            $input = $request->input()->all();
            $processing_handler = new PaymentProcessingHandler();
            
            $payment_request = $processing_handler->preparePaymentRequest($input, $shortToken);
            $response_data = $processing_handler->processPaymentAndGetResponse($payment_request);

            $this->getLog()->info(
                'Payment processed successfully via payment link',
                $response_data['logging_context']
            );

            // Remove logging context from response
            unset($response_data['logging_context']);
            return Response::json($response_data, 201);

        } catch (UnprocessableEntityException|ValidationException $e) {
            return $this->logAndRespondWithErrors($e);
        } catch (DuplicatePaymentException $e) {
            $this->getLog()->warning('Duplicate payment attempt blocked: ' . $e->getMessage(), [
                'wait_minutes' => $e->getWaitMinutes(),
                'recent_transaction' => $e->getRecentTransaction()
            ]);
            return Response::json($e->getResponseData(), $e->getCode());
        } catch (GatewayTimeoutException $e) {
            $this->getLog()->warning('Payment gateway timeout: ' . $e->getMessage());
            return Response::json($e->getResponseData(), $e->getCode());
        } catch (PaymentDeclinedException $e) {
            $this->getLog()->error('Payment declined/retryable via payment link: ' . $e->getMessage(), $e->getGatewayDetails());
            return Response::json($e->getResponseData(), $e->getCode());
        } catch (\Exception $e) {
            $this->getLog()->error('Payment processing failed via payment link: ' . $e->getMessage());
            return Response::json(['error' => 'Payment processing failed.'], 500);
        }
    }


    /**
     * Get payment settings for the authenticated user's company
     *
     * @return JSONResponse
     * @throws HttpResponseException
     */
    public function getSettings(): JSONResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                throw new HttpResponseException(401, 'User not authenticated.');
            }

            $company = $user->company;
            $merchant = PropelrMerchant::where('companyUUID', $company->companyUUID)
                ->whereNull('deletedAt')
                ->first();

            if (!$merchant) {
                return Response::json(['error' => 'No merchant found.'], 404);
            }

            $payment_service = new PropelrPaymentService();
            $payment_data = $payment_service->getApiPaymentData($company, $merchant);

            return Response::json($payment_data);
        } catch (\Exception $e) {
            $this->getLog()->error('Error fetching payment settings: ' . $e->getMessage());
            return Response::json(['error' => 'An error occurred while fetching payment settings.'], 500);
        }
    }

    /**
     * Create CoPilot merchant for onboarding
     *
     * @param RequestInterface $request
     * @return JSONResponse
     */
    public function createCoPilotMerchant(RequestInterface $request): JSONResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                throw new HttpResponseException(401, 'User not authenticated.');
            }

            $input = $request->input()->all();
            $validator = $this->validateCoPilotMerchantData($input);

            // todo - filipe - Improve this error handling to provide more specific feedback to users about the nature
            // of what's wrong with the data they provided.
            // Currently the errors are being collected in the ValidationException thrown below.

            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $company = $user->company;
            $copilotService = new CoPilotService($user);
            $company_uuid = UUID::fromBytes($company->companyUUID);
            $result = $copilotService->createMerchant($input, $company_uuid, $user);

            if (!$result['success']) {
                $statusCode = ($result['code'] ?? null) === 'MERCHANT_ALREADY_EXISTS' ? 409 : 400;
                
                return Response::json([
                    'error' => $result['error'],
                    'code' => $result['code'] ?? null,
                    'details' => $result['details'] ?? null
                ], $statusCode);
            }

            return Response::json([
                'message' => 'CoPilot merchant created successfully.',
                'merchantID' => $result['merchantId'],
            ], 201);

        } catch (UnprocessableEntityException|ValidationException $e) {
            return $this->logAndRespondWithErrors($e);
        } catch (\Exception $e) {
            $this->getLog()->error('Error creating CoPilot merchant: ' . $e->getMessage());
            return Response::json(['error' => 'An error occurred while creating CoPilot merchant.'], 500);
        }
    }


    /**
     * Validate CoPilot merchant data
     *
     * @param array $input
     * @return Validator
     */
    private function validateCoPilotMerchantData(array $input): Validator
    {
        $config = FieldConfig::fromArray([
            'merchant' => [
                'rules' => 'required|type[array]',
                'label' => 'Merchant'
            ],
            'merchant.dbaName' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'DBA Name'
            ],
            'merchant.legalBusinessName' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Legal Business Name'
            ],
            'merchant.taxFilingName' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Tax Filing Name'
            ],
            'merchant.taxFilingMethod' => [
                'rules' => 'trim|required|in_array[tax_filing_methods]',
                'label' => 'Tax Filing Method'
            ],
            'merchant.businessStartDate' => [
                'rules' => 'required|to_carbon',
                'label' => 'Business Start Date'
            ],
            'merchant.demographic' => [
                'rules' => 'required|type[array]',
                'label' => 'Demographic'
            ],
            'merchant.demographic.websiteAddress' => [
                'rules' => 'trim|optional|max_length[255]',
                'label' => 'Website Address'
            ],
            'merchant.demographic.businessPhone' => [
                'rules' => 'optional|us_phone_copilot',
                'label' => 'Business Phone'
            ],
            'merchant.demographic.businessAddress' => [
                'rules' => 'required|type[array]',
                'label' => 'Business Address'
            ],
            'merchant.demographic.businessAddress.address1' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Business Address Line 1'
            ],
            'merchant.demographic.businessAddress.city' => [
                'rules' => 'trim|required|max_length[50]',
                'label' => 'Business City'
            ],
            'merchant.demographic.businessAddress.stateCd' => [
                'rules' => 'trim|required|max_length[2]',
                'label' => 'Business State Code'
            ],
            'merchant.demographic.businessAddress.zip' => [
                'rules' => 'trim|required|max_length[10]',
                'label' => 'Business Zip Code'
            ],
            'merchant.demographic.businessAddress.countryCd' => [
                'rules' => 'trim|required|max_length[2]',
                'label' => 'Business Country Code'
            ],
            'merchant.demographic.mailingAddress' => [
                'rules' => 'required|type[array]',
                'label' => 'Mailing Address'
            ],
            'merchant.demographic.mailingAddress.address1' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Mailing Address Line 1'
            ],
            'merchant.demographic.mailingAddress.city' => [
                'rules' => 'trim|required|max_length[50]',
                'label' => 'Mailing City'
            ],
            'merchant.demographic.mailingAddress.stateCd' => [
                'rules' => 'trim|required|max_length[2]',
                'label' => 'Mailing State Code'
            ],
            'merchant.demographic.mailingAddress.zip' => [
                'rules' => 'trim|required|max_length[10]',
                'label' => 'Mailing Zip Code'
            ],
            'merchant.demographic.mailingAddress.countryCd' => [
                'rules' => 'trim|required|max_length[2]',
                'label' => 'Mailing Country Code'
            ],
            'merchant.ownership' => [
                'rules' => 'required|type[array]',
                'label' => 'Ownership'
            ],
            'merchant.ownership.owner' => [
                'rules' => 'required|type[array]',
                'label' => 'Owner'
            ],
            'merchant.ownership.owner.ownerName' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Owner Name'
            ],
            'merchant.ownership.owner.ownerEmail' => [
                'rules' => 'trim|required|email|max_length[100]',
                'label' => 'Owner Email'
            ],
            'merchant.ownership.owner.ownerPhone' => [
                'rules' => 'trim|required|us_phone_copilot|max_length[20]',
                'label' => 'Owner Phone'
            ],
            'merchant.ownership.owner.ownerTitle' => [
                'rules' => 'trim|required|max_length[50]',
                'label' => 'Owner Title'
            ],
            'merchant.ownership.owner.ownerAddress' => [
                'rules' => 'required|type[array]',
                'label' => 'Owner Address'
            ],
            'merchant.ownership.owner.ownerAddress.address1' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Owner Address Line 1'
            ],
            'merchant.ownership.owner.ownerAddress.city' => [
                'rules' => 'trim|required|max_length[50]',
                'label' => 'Owner City'
            ],
            'merchant.ownership.owner.ownerAddress.stateCd' => [
                'rules' => 'trim|required|max_length[2]',
                'label' => 'Owner State Code'
            ],
            'merchant.ownership.owner.ownerAddress.zip' => [
                'rules' => 'trim|required|max_length[10]',
                'label' => 'Owner Zip Code'
            ],
            'merchant.ownership.owner.ownerAddress.countryCd' => [
                'rules' => 'trim|required|max_length[2]',
                'label' => 'Owner Country Code'
            ],
            'merchant.ownership.driversLicenseNumber' => [
                'rules' => 'trim|required|max_length[25]',
                'label' => 'Driver\'s License Number'
            ],
            'merchant.ownership.driversLicenseStateCd' => [
                'rules' => 'trim|required|max_length[2]',
                'label' => 'Driver\'s License State Code'
            ],
            'merchant.ownership.ownershipTypeCd' => [
                'rules' => 'trim|optional|in_array[ownership_types]',
                'label' => 'Ownership Type Code'
            ],
            'merchant.ownership.ownerOwnershipPct' => [
                'rules' => 'optional|type[int]|greater_than[0]|less_than[101]',
                'label' => 'Owner Ownership Percentage'
            ],
            'merchant.ownership.owner.ownerDob' => [
                'rules' => 'required|to_carbon',
                'label' => 'Owner Date of Birth'
            ],
            'merchant.ownership.owner.ownerSSN' => [
                'rules' => 'required',
                'label' => 'Owner SSN'
            ],
            'merchant.ownership.owner.ownerMobilePhone' => [
                'rules' => 'trim|required|us_phone_copilot|us_phone_format',
                'label' => 'Owner Mobile Phone'
            ],
            'merchant.merchantContactInfo' => [
                'rules' => 'required|type[array]',
                'label' => 'Merchant Contact Info'
            ],
            'merchant.merchantContactInfo.contactName' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Contact Name'
            ],
            'merchant.merchantContactInfo.contactEmail' => [
                'rules' => 'trim|required|email|max_length[100]',
                'label' => 'Contact Email'
            ],
            'merchant.merchantContactInfo.contactPhone' => [
                'rules' => 'required|us_phone_copilot|us_phone_format',
                'label' => 'Contact Phone'
            ],
            'merchant.processing' => [
                'rules' => 'required|type[array]',
                'label' => 'Processing'
            ],
            'merchant.processing.volumeDetails' => [
                'rules' => 'required|type[array]',
                'label' => 'Volume Details'
            ],
            'merchant.processing.volumeDetails.averageMonthlyVolume' => [
                'rules' => 'required|numeric|greater_than_equal[0]',
                'label' => 'Average Monthly Volume'
            ],
            'merchant.processing.volumeDetails.highTicketAmount' => [
                'rules' => 'required|numeric|greater_than_equal[0]',
                'label' => 'High Ticket Amount'
            ],
            'merchant.processing.volumeDetails.averageTicketAmount' => [
                'rules' => 'required|numeric|greater_than_equal[0]',
                'label' => 'Average Ticket Amount'
            ],
            'merchant.processing.platformDetails.taxId' => [
                'rules' => 'trim|required|type[string]|exact_length[9]|numeric',
                'label' => 'Tax ID'
            ],
            'merchant.bankDetail' => [
                'rules' => 'required|type[array]',
                'label' => 'Bank Detail'
            ],
            'merchant.bankDetail.depositBank' => [
                'rules' => 'required|type[array]',
                'label' => 'Deposit Bank'
            ],
            'merchant.bankDetail.depositBank.bankAcctNum' => [
                'rules' => 'trim|required|max_length[30]',
                'label' => 'Deposit Account Number'
            ],
            'merchant.bankDetail.depositBank.bankRoutingNum' => [
                'rules' => 'required|aba_routing_transit_number',
                'label' => 'Deposit Routing Number'
            ],
            'merchant.bankDetail.depositBank.bankAcctTypeCd' => [
                'rules' => 'trim|required|in_array[bank_account_types]',
                'label' => 'Deposit Account Type Code'
            ],
            'merchant.bankDetail.depositBank.bankName' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Deposit Bank Name'
            ],
            'merchant.bankDetail.withdrawalBank' => [
                'rules' => 'required|type[array]',
                'label' => 'Withdrawal Bank'
            ],
            'merchant.bankDetail.withdrawalBank.bankAcctNum' => [
                'rules' => 'trim|required|max_length[30]',
                'label' => 'Withdrawal Account Number'
            ],
            'merchant.bankDetail.withdrawalBank.bankRoutingNum' => [
                'rules' => 'required|aba_routing_transit_number',
                'label' => 'Withdrawal Routing Number'
            ],
            'merchant.bankDetail.withdrawalBank.bankAcctTypeCd' => [
                'rules' => 'trim|required|in_array[bank_account_types]',
                'label' => 'Withdrawal Account Type Code'
            ],
            'merchant.bankDetail.withdrawalBank.bankName' => [
                'rules' => 'trim|required|max_length[100]',
                'label' => 'Withdrawal Bank Name'
            ],
            'ownerSiteUser' => [
                'rules' => 'required|type[array]',
                'label' => 'Owner Site User'
            ],
            'ownerSiteUser.firstName' => [
                'rules' => 'trim|required|max_length[50]',
                'label' => 'First Name'
            ],
            'ownerSiteUser.lastName' => [
                'rules' => 'trim|required|max_length[50]',
                'label' => 'Last Name'
            ],
            'ownerSiteUser.email' => [
                'rules' => 'trim|required|email|max_length[100]',
                'label' => 'Email'
            ]
        ]);

        // Store validation arrays for in_array rules
        $config->store('tax_filing_methods', PropelrMerchant::getTaxFilingMethods());
        $config->store('ownership_types', PropelrMerchant::getOwnershipTypes());
        $config->store('bank_account_types', PropelrMerchant::getBankAccountTypes());

        $validation = Validation::make()->config($config);
        return $validation->run($input);
    }

    private function logAndRespondWithErrors($e): JSONResponse
    {
        $message = $e->getMessage();
        $data = $e instanceof ValidationException ? $e->getErrors() : [];
        $errors = $e instanceof UnprocessableEntityException ? $e->getResponse()->getContent() : $e->getErrors();
        $data['errors'] = $errors;
        $this->getLog()->error($message, $data);

        return Response::json([
            'error' => $e->getMessage(),
            'code' => 'VALIDATION_FAILED',
            'errors' => $errors
        ], 400);
    }
}

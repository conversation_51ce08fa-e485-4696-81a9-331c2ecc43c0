<?php

namespace App\Http\Controllers\ApiV1\Company;

use App\Exceptions\Api\BadRequestException;
use App\Resources\Company\SubscriptionResource;
use App\Services\CompanySubscription\Exceptions\CompanySubscriptionException;
use App\Services\CompanySubscriptionService;
use App\Traits\Resource\Controller\ActionTrait;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Exception;

class SubscriptionController
{
    use ActionTrait {
        retrieve as actionRetrieve;
    }

    protected $resource = SubscriptionResource::class;

    public function __construct()
    {
        $this->registerFormat('account-v1', 'application/vnd.adg.fx.account-v1+json');
    }

    protected function handleID($id)
    {
        if ($id === 'current') {
            $id = $this->getResource()->getCurrentByCompanyID(Auth::user()->companyID);
        }
        return (int) $id;
    }

    public function retrieve($id, ResourceRequest $request)
    {
        return $this->actionRetrieve($this->handleID($id), $request);
    }

    public function settle($id)
    {
        try {
            $service = new CompanySubscriptionService;
            $service->settleSubscription(Auth::acl(), $this->handleID($id));

            return Response::create('', 204);
        } catch (CompanySubscriptionException $e) {
            throw new BadRequestException(1011, $e->getMessage());
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    // no need to update other methods as they aren't used at this time
}

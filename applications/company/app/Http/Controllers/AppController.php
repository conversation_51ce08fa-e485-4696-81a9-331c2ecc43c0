<?php

namespace App\Http\Controllers;

use App\Classes\FX\Report;
use App\Resources\CompanyResource;
use App\Resources\Project\EventResource;
use App\Resources\ProjectResource;
use App\Resources\TaskResource;
use App\Resources\UserResource;
use App\Services\CompanyFeatureService;
use App\Services\CompanyFilterService;
use App\Services\CompanySettingService;
use App\Services\DomainService;
use App\Services\GoogleApi\Calendar\Classes\Calendar\Event;
use App\Services\GoogleApi\Services\CalendarService;
use App\Services\GoogleApiService;
use App\Services\Payment\PropelrPaymentService;
use App\Services\QuickbooksService;
use App\Services\TimeService;
use App\Services\TrainingService;
use App\Services\UserSettingService;
use App\Services\WisetackService;
use Carbon\Carbon;
use Common\Models\Brand;
use Common\Models\Company;
use Common\Models\CompanyIntake;
use Common\Models\Feature;
use Common\Models\IntakeIndustry;
use Common\Models\LeadForm;
use Common\Models\PropelrMerchant;
use Common\Models\WisetackMerchant;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\RedirectResponse;
use Core\Components\Http\Responses\ViewResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Http\StaticAccessors\URI;
use Core\Components\Http\StaticAccessors\View;
use Core\StaticAccessors\App;
use Ramsey\Uuid\Uuid;

class AppController
{
    protected $time_service;

    public function __construct(TimeService $time_service)
    {
        $this->time_service = $time_service;
    }

    public function home()
    {
        $user = Auth::user();
        $setting_service = new CompanySettingService($user->company->companyID);

        $business_hours = $setting_service->get('business_hours_config');
        $formatted_business_hours = [];
        if ($business_hours === null) {
            $formatted_business_hours = [
                [
                    'start' => '8:00',
                    'end' => '17:00',
                    'dow' => [1, 2, 3, 4, 5]
                ]
            ];
        } else {
            foreach ($business_hours as $item) {
                if ($item['status'] === 0) {
                    continue;
                }
                $formatted_business_hours[] = [
                    'start' => $item['start'],
                    'end' => $item['end'],
                    'dow' => [$item['day']]
                ];
            }
        }
        return Response::view('pages.app.home', [
            'user' => [
                'first_name' => $user->userFirstName
            ],
            'add_customer' => $user->primary || $user->projectManagement || $user->sales,
            'company' => [
                'latitude' => $user->company->latitude,
                'longitude' => $user->company->longitude
            ],
            'calendar_editable' => ($user->primary || $user->projectManagement ? '1' : '0'),
            'business_hours' => $formatted_business_hours,
            'date_today' => $this->time_service->get(Carbon::now('UTC'))->format('Y-m-d')
        ]);
    }

    public function account()
    {
        $user = Auth::user();
        // Companies without subscriptions and payment profiles will have subscription id set to null
        // These are special use cases where the company needs free access to show/sell the software
        $company = $user->company;
        if (!$user->primary || $company->subscriptionID === null || $company->resellerID === 2) { // @todo remove hard coded reseller when possible
            return Response::redirect()->toRoute('page.app.home');
        }
        $feature_service = new CompanyFeatureService($user->companyID);
        return Response::view('pages.app.account', [
            'company_id' => $company->companyID,
            'company_name' => $company->name,
            'user_count' => UserResource::make(Auth::acl())->activeUsersByCompanyID($user->companyID),
            'ach_allowed' => $feature_service->has(Feature::ACH_PAYMENT)
        ])->share('include_layout', false);
    }

    public function appointments(): ViewResponse
    {
        $user = Auth::user();
        $filter_service = new CompanyFilterService($user->companyID);

        $view = View::fetch('pages.app.appointments', [
            'script_data' => [
                'export' => $user->primary,
                'billing_allowed' => $user->primary || $user->projectManagement || $user->sales,
                'filter_options' => [
                    'all_users' => $filter_service->getAllActiveUsers(),
                    'cities' => $filter_service->getCustomerCities(),
                    'states' => $filter_service->getCustomerStates(),
                    'postal_codes' => $filter_service->getCustomerPostalCodes()
                ]
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function companyProfile()
    {
        $user = Auth::user();
        if (!$user->primary) {
            return Response::redirect()->toRoute('page.app.home');
        }
        $company_feature = new CompanyFeatureService($user->companyID);
        $company = $user->company;

        /** @var DomainService $domain_service */
        $domain_service = App::get(DomainService::class);
        $brand_id = $domain_service->findByCompanyID($user->companyID)->brandID;
        $brand = Brand::find($brand_id);

        $google_api = new GoogleApiService($user);
        $google_calendar = new CalendarService($google_api);
        $google_status = $google_calendar->getStatus();

        $wisetack_service = new WisetackService($user);
        $company_uuid = strval(Uuid::fromBytes($user->company->companyUUID));
        $wisetack_merchant = $wisetack_service->getMerchantByCompanyUUIDString($company_uuid) ?: null;
        $lead_form = LeadForm::where('companyID', $company->companyID);
        $lead_website_form_uuid = $lead_form->first() ? strtoupper(bin2hex($lead_form->first()->leadFormID)) : null;
        $lead_website_form_status = $lead_form->first() ? $lead_form->first()->isActive : false;
        $lead_website_form_token = $lead_form->first() ? $lead_form->first()->token : null;
        $lead_website_form_url = URI::route('lead-forms-action.ingest')->build();

        $filter_service = new CompanyFilterService($company->companyID);

        $is_mysalesman_industry = CompanyIntake::where('companyID', $user->companyID)
            ->whereIn('intakeIndustryID', [IntakeIndustry::FENCING_STAINING_DECKS, IntakeIndustry::LANDSCAPE_TURF_IRRIGATION])
            ->exists();

        return Response::view('pages.app.company-profile', [
            'is_setup_wizard' => $user->isCompanySetupWizard,
            'brand_name' => $brand->name,
            'company_name' => $company->name,
            'available_users' => CompanyResource::make(Auth::acl())->getAvailableUserCount($user->companyID),
            'todayDateShortEmailPreview' => date('n/j/Y'),
            'primaryPhoneNumber' => $company->primaryPhone->phoneNumber,
            'primaryPhoneDescription' => $company->primaryPhone->description,
            'address' => $company->address.', '.$company->city.', '.$company->state.''.$company->zip,
            'website' => $company->website,
            'userID' => $user->userID,
            'userFirstName' => $user->userFirstName,
            'userLastName' => $user->userLastName,
            'userBio' => $user->userBio,
            'userEmail' => $user->userEmail,
            'userPhoneNumber' => $user->primaryPhone->phoneNumber,
            'userPhoneDescription' => $user->primaryPhone->phoneDescription,
            'featureMediaLibrary' => $company_feature->has(Feature::MEDIA_LIBRARY, true),
            'featureTextMessaging' => $company_feature->has(Feature::TEXT_MESSAGING, true),
            'featureMarketplace' => $company_feature->has(Feature::MARKETPLACE, true),
            'featureProductAttributes' => $company_feature->has(Feature::PRODUCT_ATTRIBUTES, false),
            'featureProductComponents' => $company_feature->has(Feature::PRODUCT_COMPONENTS, true),
            'featureBidFollowUps' => $company_feature->has(Feature::BID_FOLLOW_UPS, true),
            'featureLeadsWebsiteForm' => $company_feature->has(Feature::WEBSITE_LEADS_FORM, true),
            'featurePayments' => $company_feature->has(Feature::PAYMENTS, false),
            'google_is_enabled' => $google_calendar->isEnabled() || $google_status === CalendarService::STATUS_CONNECTED,
            'google_status' => $google_status,
            'feature_wisetack_api' => $company_feature->has(Feature::WISETACK_API, true),
            'wisetack_merchant' => $wisetack_merchant,
            'lead_website_form_uuid' => $lead_website_form_uuid,
            'lead_website_form_status' => $lead_website_form_status,
            'lead_website_form_url' => $lead_website_form_url,
            'lead_website_form_token' => $lead_website_form_token,
            'filter_options_units' => $filter_service->getCompanyUnits(),
            'filter_options_product_categories' => $filter_service->getCompanyProductCategories(),
            'feature_mysalesman' => $company_feature->has(Feature::MYSALESMAN, false),
            'is_mysalesman_industry' => $is_mysalesman_industry,
        ])->share('include_layout', false);
    }

    public function crewManagement()
    {
        $user = Auth::user();
        $feature_service = new CompanyFeatureService($user->companyID);
        if (
            !$feature_service->has(Feature::CREW_MANAGEMENT, false) ||
            (!$user->primary && !$user->timecardApprover)
        ) {
            return Response::redirect()->toRoute('page.app.home');
        }
        return Response::legacyFile('pages.app.crew-management', 'crew-management.php');
    }

    public function customers(): ViewResponse
    {
        $user = Auth::user();
        $filter_service = new CompanyFilterService($user->companyID);

        $view = View::fetch('pages.app.customers', [
            'script_data' => [
                'add' => $user->primary || $user->projectManagement || $user->sales,
                'export' => $user->primary,
                'delete' => $user->primary,
                'filter_options' => [
                    'all_users' => $filter_service->getAllActiveUsers(),
                    'cities' => $filter_service->getCustomerCities(),
                    'states' => $filter_service->getCustomerStates(),
                    'postal_codes' => $filter_service->getCustomerPostalCodes()
                ]
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function customerAdd()
    {
        $user = Auth::user();
        if (!$user->primary && !$user->projectManagement && !$user->sales) {
            return Response::redirect()->toRoute('page.app.home');
        }
        return Response::legacyFile('pages.app.customer-add', 'customer-add.php');
    }

    public function customerManagement()
    {
        // @todo check access restrictions, none defined in legacy file
        return Response::legacyFile('pages.app.customer-management', 'customer-management.php');
    }

    public function customReports(): RedirectResponse|ViewResponse
    {
        $user = Auth::user();
        $feature_service = new CompanyFeatureService($user->companyID);
        if (!$feature_service->has(Feature::CUSTOM_REPORTS) && (!$user->primary || !$user->metrics)) {
            return Response::redirect()->toRoute('page.app.home');
        }
        $view = View::fetch('pages.app.custom-reports')
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function drawings(): RedirectResponse|ViewResponse
    {
        $user = Auth::user();
        $feature_service = new CompanyFeatureService($user->companyID);
        if (!$feature_service->has(Feature::DRAWING_APP, true)) {
            return Response::redirect()->toRoute('page.app.home');
        }
        $view = View::fetch('pages.app.drawings')
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function financing():RedirectResponse|ViewResponse
    {
        $user = Auth::user();

        $merchant = WisetackMerchant::where('companyUUID', $user->company->companyUUID)
            ->whereNull('deletedAt')
            ->first();

        if (!$merchant) {
            return Response::redirect()->toRoute('page.app.home');
        }

        $is_financing_enabled = Wisetackservice::isWisetackEnabledAndApproved($user->companyID, $merchant);
        if (!$is_financing_enabled) {
            return Response::redirect()->toRoute('page.app.home');
        }

        $view = View::fetch('pages.app.financing')
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function payments(): RedirectResponse|ViewResponse
    {
        $user = Auth::user();

        $payment_service = new PropelrPaymentService();
        try {
            $payment_service->authorizeFeatureAccess($user->company, true);
        } catch (\Exception $e) {
            return Response::redirect()->toRoute('page.app.home');
        }

        $merchant = PropelrMerchant::where('companyUUID', $user->company->companyUUID)
            ->whereNull('deletedAt')
            ->first();

        if (!$merchant) {
            return Response::redirect()->toRoute('page.app.home');
        }

        $view = View::fetch('pages.app.payments')
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function notification(): ViewResponse
    {
        $view = View::fetch('pages.app.notification', [])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function leads(): ViewResponse
    {
        $user = Auth::user();
        $feature_service = new CompanyFeatureService($user->companyID);
        if (!$feature_service->has(Feature::LEADS,  true)) {
            throw new HttpResponseException(403);
        }
        $filter_service = new CompanyFilterService($user->companyID);

        $view = View::fetch('pages.app.leads', [
            'script_data' => [
                'user_id' => $user->userID,
                'is_primary' => $user->primary,
                'task_association_lead' => TaskResource::ASSOCIATION_TYPE_LEAD,
                'task_status_active' => TaskResource::STATUS_ACTIVE,
                'task_status_completed' => TaskResource::STATUS_COMPLETED,
                'filter_options' => [
                    'marketing_sources' => $filter_service->getMarketingSources(),
                    'all_users' => $filter_service->getAllActiveUsers(),
                    'project_types' => $filter_service->getProjectTypes()
                ],
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function marketing()
    {
        $user = Auth::user();
        if (!$user->primary && !$user->marketing) {
            return Response::redirect()->toRoute('page.app.home');
        }
        return Response::view('pages.app.marketing');
    }

    public function marketplace(): RedirectResponse|ViewResponse
    {
        $user = Auth::user();
        $feature_service = new CompanyFeatureService($user->companyID);
        if (!$feature_service->has(Feature::MARKETPLACE, true)) {
            return Response::redirect()->toRoute('page.app.home');
        }
        $view = View::fetch('pages.app.marketplace')
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function metrics()
    {
        $user = Auth::user();
        if (!$user->primary && !$user->metrics) {
            return Response::redirect()->toRoute('page.app.home');
        }

        $types = [
            'OpenBidsPerSalesman' => 'Open Bids Per Salesman',
            'SalesTotalPerSalesman' => 'Sales Total Per Salesman',
            'NumberOfProjectsSoldPerSalesman' => 'Projects Sold Per Salesman',
            'AvgPriceOfProjectsSold' => 'Average Price Of Projects Sold',
            'AvgPricePerProjectsBid' => 'Average Price Per Projects Bid',
            'AvgTimeFromBidToSale' => 'Average Time From Bid To Sale',
            'LeadConversion' => 'Lead Conversion',
            Report::TYPE_LEAD_CONVERSION => 'Lead Conversion Report',
            Report::TYPE_EMPLOYEE_LEAD_CONVERSION => 'Employee Lead Conversion Report',
            'Marketing' => 'Marketing (Primary Source)',
            Report::TYPE_COMPANY => 'Overall Sales Report',
            Report::TYPE_EMPLOYEE_PROJECT_LEADS => 'Employee Sales Report (Project Leads)',
            Report::TYPE_EMPLOYEE_CUSTOMER_LEADS => 'Employee Sales Report (Customer Leads)',
            Report::TYPE_EMPLOYEE_SALES_PERFORMANCE => 'Employee Sales Performance Report',
            Report::TYPE_PRODUCT => 'Sales Report by Product'
        ];

        return Response::view('pages.app.metrics', [
            'report_types' => $types
        ]);
    }

    public function properties(): ViewResponse
    {
        $user = Auth::user();
        $filter_service = new CompanyFilterService($user->companyID);

        $view = View::fetch('pages.app.properties', [
            'script_data' => [
                'export' => $user->primary,
                'delete' => $user->primary,
                'filter_options' => [
                    'all_users' => $filter_service->getAllActiveUsers(),
                    'cities' => $filter_service->getPropertyCities(),
                    'states' => $filter_service->getPropertyStates(),
                    'postal_codes' => $filter_service->getPropertyPostalCodes(),
                    'counties' => $filter_service->getPropertyCounties(),
                    'townships' => $filter_service->getPropertyTownships(),
                ]
            ]
        ])->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function projectLegacy(RequestInterface $request)
    {
        $id = $request->get('pid');
        $path = is_numeric($id) ? "/{$id}" : '';
        return Response::redirect()->toRoute('page.app.projects', ['path' => $path]);
    }

    public function projects()
    {
        $user = Auth::user();
        $company = $user->company;
        $company_feature = new CompanyFeatureService($company->companyID);
        $quickbooks_service = new QuickbooksService($company->companyID);
        $setting_service = new CompanySettingService($company->companyID);
        $wisetack_service = new WisetackService($user);

        $google_api = new GoogleApiService($user);
        $calendar_service = new CalendarService($google_api);
        $company_uuid = strval(Uuid::fromBytes($user->company->companyUUID));
        $wisetack_merchant = $wisetack_service->getMerchantByCompanyUUIDString($company_uuid) ?: null;

        $filter_service = new CompanyFilterService($company->companyID);

        $business_hours = $setting_service->get('business_hours_config');
        $formatted_business_hours = [];
        if ($business_hours === null) {
            $formatted_business_hours = [
                [
                    'start' => '8:00',
                    'end' => '17:00',
                    'dow' => [1, 2, 3, 4, 5]
                ]
            ];
        } else {
            foreach ($business_hours as $item) {
                if ($item['status'] === 0) {
                    continue;
                }
                $formatted_business_hours[] = [
                    'start' => $item['start'],
                    'end' => $item['end'],
                    'dow' => [$item['day']]
                ];
            }
        }

        return Response::view('pages.app.projects', [
            'script_data' => [
                'can_export' => $user->primary,
                'can_delete' => $user->primary,
                'sales_only' => !$user->primary && !$user->projectManagement,
                'user' => [
                    'id' => $user->userID,
                    'billing_allowed' => $user->primary || $user->projectManagement || $user->sales,
                    'can_edit' => $user->primary || $user->projectManagement || $user->sales,
                    'primary' => $user->primary,
                    'project_management' => $user->projectManagement,
                    'sales' => $user->sales,
                    'installation' => $user->installation
                ],
                'features' => [
                    'files' => $company_feature->has(Feature::PROJECT_FILES, true),
                    'product_components' => $company_feature->has(Feature::PRODUCT_COMPONENTS, true),
                    'bid_follow_ups' => $company_feature->has(Feature::BID_FOLLOW_UPS, true),
                    'tasks' => $company_feature->has(Feature::TASKS, true),
                    'wisetack_api' => $company_feature->has(Feature::WISETACK_API, true),
                ],
                'wisetack' => [
                    'merchant' => $wisetack_merchant,
                    'is_merchant_approved' => $wisetack_merchant ? $wisetack_merchant->isApproved() : false,
                ],
                'google_calendar' => [
                    'project_event_sync_allowed' => $calendar_service->isConnected() && $calendar_service->hasCalendarForEventType(Event::TYPE_PROJECT)
                ],
                'company' => [
                    'connection_status' => $quickbooks_service->isConnected(),
                    'use_quickbooks_invoice' => $setting_service->get('quickbooks_use_invoice', true),
                    'bid_follow_up_notifications' => $setting_service->get('bid_follow_up_notifications', false),
                    'marketing_source_required' => $setting_service->get('marketing_source_required', false),
                    'is_financing_required_for_all_projects' => $setting_service->get('wisetack_financing_required_for_all_projects', false),
                    'project_install_collaboration' => $setting_service->get('project_install_collaboration', true),
                    'project_costing_for_primary_only' => $setting_service->get('project_costing_for_primary_only', false),
                    'result_required' => $setting_service->get('result_required', false),
                    'project_type_required' => $setting_service->get('project_type_required', false),
                    'default_invoices' => $company->defaultInvoices,
                    'latitude' => $company->latitude,
                    'longitude' => $company->longitude,
                    'business_hours' => $formatted_business_hours
                ],
                'filter_options' => [
                    'marketing_sources' => $filter_service->getMarketingSources(),
                    'salespeople' => $filter_service->getSalesUsers(),
                    'all_users' => $filter_service->getAllActiveUsers(),
                    'project_types' => $filter_service->getProjectTypes(),
                    'result_types' => $filter_service->getResultTypes()
                ],
                'invoice_split' => [
                    'bid_acceptance' => $company->invoiceSplitBidAcceptance,
                    'project_complete' => $company->invoiceSplitProjectComplete
                ],
                'pending_delay' => EventResource::PENDING_DELAY,
                'current_date' => date('Y-m-d'),
                'task_association_project' => TaskResource::ASSOCIATION_TYPE_PROJECT,
                'task_status_active' => TaskResource::STATUS_ACTIVE,
                'task_status_completed' => TaskResource::STATUS_COMPLETED,
            ]
        ])->share('include_layout', false);
    }

    public function signup(): ViewResponse
    {
        $user = Auth::user();

        if (!$user->primary || $user->company->status !== Company::STATUS_SIGNUP) {
            throw new HttpResponseException(403);
        }

        $view = View::fetch('pages.app.signup')
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function setupWizard(): ViewResponse
    {
        $user = Auth::user();
        if (!$user->primary || !$user->isCompanySetupWizard) {
            throw new HttpResponseException(403);
        }

        $google_api = new GoogleApiService($user);
        $google_calendar = new CalendarService($google_api);
        $google_status = $google_calendar->getStatus();

        /** @var DomainService $domain_service */
        $domain_service = App::get(DomainService::class);
        $brand_id = $domain_service->findByCompanyID($user->companyID)->brandID;
        $brand = Brand::find($brand_id);

        $training_status = TrainingService::isUserInTraining($user);



        $wisetack_service = new WisetackService($user);
        $company_feature = new CompanyFeatureService($user->company->companyID);
        $company_uuid = strval(Uuid::fromBytes($user->company->companyUUID));
        $wisetack_merchant = $wisetack_service->getMerchantByCompanyUUIDString($company_uuid) ?: null;

        $view = View::fetch('pages.app.setup-wizard', [
            'script_data' => [
                'brand_name' => $brand->name,
                'status' => $user->companyStatus,
                'current_step' => $user->companySetupWizardStep,
                'available_users' => CompanyResource::make(Auth::acl())->getAvailableUserCount($user->companyID),
                'google_status' => $google_status,
                'is_training' => $training_status,
                'features' => [
                    'wisetack_api' => $company_feature->has(Feature::WISETACK_API, true),
                ],
                'wisetack' => [
                    'merchant' => $wisetack_merchant,
                    'is_merchant_approved' => $wisetack_merchant ? $wisetack_merchant->isApproved() : false,
                ],
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function tasks(): ViewResponse
    {
        $user = Auth::user();
        $feature_service = new CompanyFeatureService($user->companyID);
        if (!$feature_service->has(Feature::TASKS,  true)) {
            throw new HttpResponseException(403);
        }
        $filter_service = new CompanyFilterService($user->companyID);

        $view = View::fetch('pages.app.tasks', [
            'script_data' => [
                'user_id' => $user->userID,
                'is_primary' => $user->primary,
                'filter_options' => [
                    'all_users' => $filter_service->getAllActiveUsers()
                ]
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function training(): ViewResponse|RedirectResponse
    {
        $user = Auth::user();
        if (!TrainingService::isUserInTraining($user)) {
            return Response::redirect()->toRoute('page.app.home');
        }
        $days_ago = (new Carbon($user->userAdded, 'UTC'))->diffInDays();
        $first_project = ProjectResource::make(Auth::acl())->firstProject();
        $first_project_id = null;
        if ($first_project !== null) {
            $first_project_id = $first_project->projectID;
        }
        $view = View::fetch('pages.app.training', [
            'script_data' => [
                'first_project_id' => $first_project_id,
                'demo_link' => $user->company->successManager->hubspotScheduleDemoLink,
                'is_primary' => $user->primary,
                'show_finalize' => $days_ago > 30,
                'is_setup_wizard' => $user->isCompanySetupWizard
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function userProfile(): ViewResponse
    {
        $user = Auth::user();
        $feature_service = new CompanyFeatureService($user->companyID);
        $calendar_feed = $feature_service->has(Feature::CALENDAR_FEED, true) && ($user->sales || $user->installation);
        $google_api = new GoogleApiService($user);
        $google_calendar = new CalendarService($google_api);
        $google_enabled = $google_calendar->isEnabled();
        $google_status = $google_calendar->getStatus();

        /** @var DomainService $domain_service */
        $domain_service = App::get(DomainService::class);
        $brand_id = $domain_service->findByCompanyID($user->companyID)->brandID;
        $brand = Brand::find($brand_id);

        $view = View::fetch('pages.app.user-profile', [
            'script_data' => [
                'brand_name' => $brand->name,
                'integrations' => $calendar_feed || $google_enabled || $google_status === CalendarService::STATUS_CONNECTED,
                'calendar_feed' => $calendar_feed,
                'google' => [
                    'is_enabled' => $google_enabled || $google_status === CalendarService::STATUS_CONNECTED,
                    'status' => $google_status
                ]
            ]
        ])
            ->share('include_layout', false)
            ->share('legacy_assets', false);
        return Response::view($view);
    }

    public function viewBid()
    {
        return Response::legacyFile('pages.app.view-bid', 'view-bid.php');
    }
}

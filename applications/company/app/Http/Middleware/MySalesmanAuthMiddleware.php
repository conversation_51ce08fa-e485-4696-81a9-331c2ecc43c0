<?php

namespace App\Http\Middleware;

use App\Exceptions\Api\UnauthorizedException;
use App\Services\CompanyFeatureService;
use App\Services\MySalesman\Utils\MySalesmanHelper;
use Common\Models\Feature;
use Core\Exceptions\AppException;
use Closure;
use Common\Models\MySalesmanCredential;
use Common\Models\User;
use Core\Components\Auth\Classes\Auth;
use Core\Components\Http\StaticAccessors\Response;
use Exception;

class MySalesmanAuthMiddleware
{
    protected $auth;

    public function __construct(Auth $auth)
    {
        $this->auth = $auth;
    }

    public function handle($request, Closure $next)
    {
        try {
            $header_key = MySalesmanCredential::API_AUTH_HEADER;
            $api_key = $request->input()->header($header_key);

            if (!$api_key) {
                return Response::json(['error' => "Unauthorized. Header $header_key is missing."], 401);
            }

            $user = $this->getUserFromApiKey($api_key);

            $helper = new MySalesmanHelper();
            $helper->checkIntegrationFeatureEnabled($user);
            $this->auth->setUser($user);
        } catch (Exception $e) {
            return Response::json(['error' => 'Unauthorized. Invalid or expired token.'], 401);
        }

        return $next($request);
    }

    /**
     * Validate api key
     *
     * @throws Exception
     */
    protected function getUserFromApiKey($key): User
    {
        $credential = MySalesmanCredential::where('key', $key)
            ->where('isActive', 1)
            ->first();

        if (!$credential) {
            throw new UnauthorizedException(1002);
        }

        $user = $credential->getUser();

        if (!$user) {
            throw new UnauthorizedException(1002);
        }

        return $user;
    }

    /** Check if integration Feature is enabled for the company
     * @throws AppException
     * @throws Exception
     */
    protected function checkIntegrationFeatureEnabled(User $user)
    {
        $company_id = $user->companyID;
        $company_feature = new CompanyFeatureService($company_id);
        if (!$company_feature->has(Feature::MY_SALESMAN)) {
            throw new Exception("Integration not enabled for this company");
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Resources\Bid\Item\Section;

use App\ResourceDelegates\Bid\Item\Section\FormDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\BidItemSectionForm;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, ImmutableRelationException, RequestFailedException};
use Exception;

/**
 * Class FormResource
 *
 * @package App\Resources\Bid\Item\Section
 */
class FormResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~self::ACTION_SEARCH;

    /**
     * @var string Table name
     */
    protected $table = 'bidItemSectionForms';

    /**
     * @var string Model class name
     */
    protected $model = BidItemSectionForm::class;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(FormDelegate::class);
    }

    /**
     * Finalize forms by section id
     *
     * Marks associated form item entries as locked so they can no longer be edited
     *
     * @param string $section_id Uuid
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function finalizeBySectionID(string $section_id): void
    {
        $form_item_entry_resource = $this->relationResource('form_item_entry');

        $form_item_entry_id_field = $this->getFields()->get('form_item_entry_id');

        $section_id = $this->getFields()->get('section_id')->saveValue($section_id);
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.bidItemSectionID', $section_id)
            ->get([$form_item_entry_id_field->getColumn($this->getTableAlias(), true, false)])
            ->each(function ($form) use ($form_item_entry_resource, $form_item_entry_id_field) {
                $form_item_entry_resource->partialUpdate(Entity::make([
                    'id' => $form_item_entry_id_field->outputValueFromModel($form),
                    'is_locked' => true
                ]))->run();
            });
    }

    /**
     * Delete forms by section id
     *
     * @param string $section_id Uuid
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteBySectionID(string $section_id): void
    {
        $section_id = $this->getFields()->get('section_id')->saveValue($section_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.bidItemSectionID', $section_id)
            ->get()
            ->each(function ($form) use ($primary_field) {
                $id = $primary_field->outputValueFromModel($form);
                try {
                    $this->delete(Entity::make([
                        $primary_field->getName() => $id
                    ]))->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('Form is immutable'), $e, [
                        'bid_item_section_form_id' => $id
                    ]);
                } catch (ImmutableRelationException $e) {
                    throw $this->replaceBulkActionException(new ImmutableRelationException('Form has immutable relation'), $e, [
                        'bid_item_section_form_id' => $id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete form'), $e, [
                        'bid_item_section_form_id' => $id
                    ]);
                }
            });
    }
}

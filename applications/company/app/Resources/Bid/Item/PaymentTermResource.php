<?php

declare(strict_types=1);

namespace App\Resources\Bid\Item;

use App\ResourceDelegates\Bid\Item\PaymentTermDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\BidItemPaymentTerm;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, ImmutableRelationException, RequestFailedException};
use Exception;

/**
 * Class PaymentTermResource
 *
 * @package App\Resources\Bid\Item
 */
class PaymentTermResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    const TYPE_ONE_TIME = 1;
    const TYPE_INSTALLMENT = 2;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = (self::ACTION_GROUP_READ_ONLY_FULL | self::ACTION_GROUP_CREATE | self::ACTION_GROUP_UPDATE | self::ACTION_GROUP_DELETE) & ~(self::ACTION_GROUP_BATCH | self::ACTION_SEARCH);

    /**
     * @var string Table name
     */
    protected $table = 'bidItemPaymentTerms';

    /**
     * @var string Model class name
     */
    protected $model = BidItemPaymentTerm::class;

    /**
     * Get available types
     *
     * @return array
     */
    public static function getTypes(): array
    {
        return [static::TYPE_ONE_TIME, static::TYPE_INSTALLMENT];
    }

    /**
     * Map of model types to resource types
     *
     * Since we have to store the type as a model id in persistent storage to make polymorphic relations
     * work, we need to convert type to a more usable value for the resource
     *
     * @return array
     */
    public static function getTypeMap(): array
    {
        return [
            BidItemPaymentTerm::TYPE_ONE_TIME => static::TYPE_ONE_TIME,
            BidItemPaymentTerm::TYPE_INSTALLMENT => static::TYPE_INSTALLMENT
        ];
    }

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(PaymentTermDelegate::class);
    }

    /**
     * Delete payment term by bid item id
     *
     * @param string $item_id Uuid
     * @param bool $force
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteByBidItemID(string $item_id, bool $force = false): void
    {
        $item_id = $this->getFields()->get('bid_item_id')->saveValue($item_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.bidItemID', $item_id)
            ->get()
            ->each(function ($payment_term) use ($primary_field, $force) {
                $id = $primary_field->outputValueFromModel($payment_term);
                try {
                    $this->delete(Entity::make([
                        $primary_field->getName() => $id
                    ]))->force($force)->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('Payment term is immutable'), $e, [
                        'bid_item_payment_term_id' => $id
                    ]);
                } catch (ImmutableRelationException $e) {
                    throw $this->replaceBulkActionException(new ImmutableRelationException('Payment term has immutable relation'), $e, [
                        'bid_item_payment_term_id' => $id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete payment term'), $e, [
                        'bid_item_payment_term_id' => $id
                    ]);
                }
            });
    }
}

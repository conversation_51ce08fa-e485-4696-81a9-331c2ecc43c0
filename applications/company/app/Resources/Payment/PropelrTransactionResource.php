<?php

declare(strict_types=1);

namespace App\Resources\Payment;

use App\ResourceDelegates\Payment\PropelrTransactionDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\PropelrTransaction;
use Core\Components\Resource\Classes\Resource;

/**
 * PropelrTransaction Resource
 *
 * @package App\Resources\Payment
 */
class PropelrTransactionResource extends Resource
{
    const STATUS_CAPTURED = PropelrTransaction::STATUS_CAPTURED;
    const STATUS_PENDING_SETTLEMENT = PropelrTransaction::STATUS_PENDING_SETTLEMENT;
    const STATUS_UNDER_REVIEW = PropelrTransaction::STATUS_UNDER_REVIEW;
    const STATUS_SETTLED = PropelrTransaction::STATUS_SETTLED;
    const STATUS_FUNDED = PropelrTransaction::STATUS_FUNDED;
    const STATUS_VOIDED = PropelrTransaction::STATUS_VOIDED;
    const STATUS_REFUNDED = PropelrTransaction::STATUS_REFUNDED;
    const STATUS_AUTH_DECLINED = PropelrTransaction::STATUS_AUTH_DECLINED;
    const STATUS_RETRYABLE = PropelrTransaction::STATUS_RETRYABLE;
    const STATUS_SETTLEMENT_REJECTED = PropelrTransaction::STATUS_SETTLEMENT_REJECTED;
    const STATUS_SETTLEMENT_ERROR = PropelrTransaction::STATUS_SETTLEMENT_ERROR;
    const STATUS_ZERO_AMOUNT = PropelrTransaction::STATUS_ZERO_AMOUNT;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_READ_ONLY_FULL | self::ACTION_GROUP_DELETE | self::ACTION_SEARCH | self::ACTION_FILTER | self::ACTION_SORT;
    protected $table = 'propelrTransactions';
    protected $model = PropelrTransaction::class;
    protected $generate_id = true;
    protected $allow_no_user = false;


    /**
     * Get available transaction statuses
     *
     * @return array<int>
     */
    public static function getStatuses(): array
    {
        return [
            static::STATUS_CAPTURED,
            static::STATUS_PENDING_SETTLEMENT,
            static::STATUS_UNDER_REVIEW,
            static::STATUS_SETTLED,
            static::STATUS_FUNDED,
            static::STATUS_VOIDED,
            static::STATUS_REFUNDED,
            static::STATUS_AUTH_DECLINED,
            static::STATUS_RETRYABLE,
            static::STATUS_SETTLEMENT_REJECTED,
            static::STATUS_SETTLEMENT_ERROR,
            static::STATUS_ZERO_AMOUNT,
        ];
    }

    /**
     * Get status names mapped to status codes
     *
     * @return string>
     */
    public static function getFormattedStatusNames($transaction): string
    {
        if (!empty($transaction->refundedAt)) {
            return 'Refunded';
        }

        if (!empty($transaction->voidedAt)) {
            return 'Voided';
        }

        $names =  [
            static::STATUS_CAPTURED => 'Captured',
            static::STATUS_PENDING_SETTLEMENT => 'Pending Settlement',
            static::STATUS_UNDER_REVIEW => 'Under Review',
            static::STATUS_SETTLED => 'Settled',
            static::STATUS_FUNDED => 'Funded',
            static::STATUS_VOIDED => 'Voided',
            static::STATUS_REFUNDED => 'Refunded',
            static::STATUS_AUTH_DECLINED => 'Auth Declined',
            static::STATUS_RETRYABLE => 'Retryable',
            static::STATUS_SETTLEMENT_REJECTED => 'Settlement Rejected',
            static::STATUS_SETTLEMENT_ERROR => 'Settlement Error',
            static::STATUS_ZERO_AMOUNT => 'Zero Amount',
        ];

        return $names[(int) $transaction->status] ?? 'Unknown';
    }

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(PropelrTransactionDelegate::class);
    }
}
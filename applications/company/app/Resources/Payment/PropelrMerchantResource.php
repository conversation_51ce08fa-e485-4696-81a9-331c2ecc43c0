<?php
declare(strict_types=1);

namespace App\Resources\Payment;

use App\ResourceDelegates\Payment\PropelrMerchantDelegate;
use Common\Models\PropelrMerchant;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Ramsey\Uuid\Uuid;

/**
 * Class PropelrMerchantResource
 *
 * @package App\Resources\Payment
 */
class PropelrMerchantResource extends Resource
{
    protected $table = 'propelrMerchants';
    protected $model = PropelrMerchant::class;
    protected $available_actions = self::ACTION_CREATE | self::ACTION_UPDATE | self::ACTION_PARTIAL_UPDATE | self::ACTION_GET_ENTITY | self::ACTION_GET_COLLECTION | self::ACTION_DELETE;
    protected $allow_no_user = true;
    protected $generate_id = true;






    /**
     * Get merchant by company UUID
     *
     * @param string $companyUUID
     * @return PropelrMerchant|null
     */
    public function getByCompanyUUID(string $uuid): ?PropelrMerchant
    {
        $company_uuid = UUID::fromString($uuid)->getBytes();
        return PropelrMerchant::where('companyUUID', $company_uuid)
            ->whereNull('deletedAt')
            ->first();
    }

    /**
     * Soft delete a PropelrMerchant by company UUID
     *
     * @param string $companyUUID
     * @return bool
     */
    public function deleteByCompanyUUID(string $company_uuid): bool
    {
        $merchant = $this->getByCompanyUUID($company_uuid);

        if (!$merchant) {
            return false;
        }

        $this->delete(Entity::make([
            'propelrMerchantID' => $merchant->propelrMerchantID
        ]))
        ->run();

        return true;
    }

    /**
     * Boot method to register delegate
     */
    protected static function boot(): void
    {
        static::delegate(PropelrMerchantDelegate::class);
    }
}
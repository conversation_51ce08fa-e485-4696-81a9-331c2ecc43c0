<?php
declare(strict_types=1);

namespace App\Resources\Payment;

use Common\Models\PaymentLink;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Traits\ActionTrait;

/**
 * Class PaymentLinkResource
 *
 * @package App\Resources\Payment
 */
class PaymentLinkResource extends Resource
{
    use ActionTrait;

    protected $table = 'paymentLinks';
    protected $model = PaymentLink::class;
    protected $available_actions = self::ACTION_CREATE | self::ACTION_UPDATE | self::ACTION_DELETE | self::ACTION_GET_ENTITY | self::ACTION_GET_COLLECTION;
    protected $allow_no_user = false;

    /**
     * Find a payment link by its short token
     *
     * @param string $token
     * @return PaymentLink|null
     */
    public function findByToken(string $token): ?PaymentLink
    {
        return PaymentLink::byToken($token)
            ->active()
            ->first();
    }

    /**
     * Find payment links by merchant ID
     *
     * @param string $merchantID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findByMerchantID(string $merchantID)
    {
        return PaymentLink::where('propelrMerchantID', $merchantID)
            ->active()
            ->get();
    }

    /**
     * Find payment links by transaction metadata (replaced findByBidItemID)
     *
     * @param string $bidItemID
     * @return array Transaction information for bid item
     */
    public function getBidPaymentStatus(string $bidItemID): array
    {
        $paymentLinkService = new PaymentLinkService();
        return $paymentLinkService->getBidPaymentStatus($bidItemID);
    }

    /**
     * Expire old specific payment links (simplified - no expiration logic)
     * 
     * @return int Always returns 0 since links don't expire anymore
     */
    public function expireOldLinks(): int
    {
        // Payment links are simplified and don't have expiration logic anymore
        return 0;
    }

    /**
     * Soft delete payment links by merchant ID
     *
     * @param string $merchantID
     * @return bool
     */
    public function deleteByMerchantID(string $merchantID): bool
    {
        $links = PaymentLink::where('propelrMerchantID', $merchantID)
            ->whereNull('deletedAt')
            ->get();

        foreach ($links as $link) {
            $this->delete(Entity::make([
                'paymentLinkID' => $link->paymentLinkID
            ]))
            ->run();
        }

        return true;
    }

    static public function getStatuses(): array
    {
        // Payment links are simplified and don't have statuses anymore
        return [];
    }
}
<?php

namespace App\Resources;

use App\Interfaces\ResourceCompanyMediaInterface;
use App\ResourceDelegates\MediaDelegate;
use App\Resources\Bid\Item\MediaResource as BidItemMedia;
use App\Traits\Resource\CompanyMediaTrait;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\Media;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Scope;

class MediaResource extends Resource implements ResourceCompanyMediaInterface
{
    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    use CompanyMediaTrait;
    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'media';
    protected $model = Media::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    protected static function boot()
    {
        static::delegate(MediaDelegate::class);
    }

    /**
     * Get all defaults for company and copy them to passed bid
     **
     * @param string $item_id Bid item uuid
     */
    public function createDefaultsForBidItem($item_id)
    {
        $scope = Scope::make()
            ->fields(['id'])
            ->filter('status', 'eq', static::STATUS_ACTIVE)
            ->filter('is_bid_default', 'eq', true)
            ->query(function ($query) {
                return $query->ordered();
            });
        $media = $this->collection()->scope($scope)->run();
        if (count($media) > 0) {
            $item_media_resource = BidItemMedia::make($this->acl());
            $order = 1;
            foreach ($media as $item) {
                $new_item = Entity::make([
                    'id' => $item_media_resource->generateId(),
                    'bid_item_id' => $item_id,
                    'item_id' => $item->id,
                    'order' => $order++,
                    'type' => $item_media_resource::TYPE_LIBRARY,
                ]);
                $item_media_resource->create($new_item)->run();
            }
        }
    }
}

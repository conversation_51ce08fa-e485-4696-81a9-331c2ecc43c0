<?php

namespace App\Resources\User;

use App\ResourceDelegates\User\SettingDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\UserSetting;
use Core\Components\Resource\Classes\Resource;

class SettingResource extends Resource
{
    const TYPE_STRING = 1;
    const TYPE_INT = 2;
    const TYPE_FLOAT = 3;
    const TYPE_BOOL = 4;
    const TYPE_ARRAY = 5;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_SEARCH);

    protected $table = 'userSettings';
    protected $model = UserSetting::class;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(SettingDelegate::class);
    }

    public static function getSettingConfig()
    {
        return [
            'calendar_user_sort' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'nullable' => true,
                    'optional' => true,
//                    'check_calendar_user_sort' => true
                ]
            ],
            'email_notification_task_assigned' => [
                'type' => static::TYPE_BOOL
            ],
            'app_notification_task_assigned' => [
                'type' => static::TYPE_BOOL
            ],


            'email_notification_task_due' => [
                'type' => static::TYPE_BOOL
            ],
            'app_notification_task_due' => [
                'type' => static::TYPE_BOOL
            ],


            'email_notification_lead_assigned' => [
                'type' => static::TYPE_BOOL
            ],
            'app_notification_lead_assigned' => [
                'type' => static::TYPE_BOOL
            ],


            'email_notification_project_assigned' => [
                'type' => static::TYPE_BOOL
            ],
            'app_notification_project_assigned' => [
                'type' => static::TYPE_BOOL
            ],


            'email_notification_appointment_scheduled' => [
                'type' => static::TYPE_BOOL
            ],

            'app_notification_appointment_scheduled' => [
                'type' => static::TYPE_BOOL
            ],

            'email_notification_bid_viewed' => [
                'type' => static::TYPE_BOOL
            ],
            'app_notification_bid_viewed' => [
                'type' => static::TYPE_BOOL
            ],

            'email_notification_bid_accepted' => [
                'type' => static::TYPE_BOOL
            ],
            'app_notification_bid_accepted' => [
                'type' => static::TYPE_BOOL
            ],

            'email_notification_bid_rejected' => [
                'type' => static::TYPE_BOOL
            ],
            'app_notification_bid_rejected' => [
                'type' => static::TYPE_BOOL
            ]
        ];
    }

    public static function isValidName($name)
    {
        $config = self::getSettingConfig();
        return isset($config[$name]);
    }

    public static function getSettingValidationRules($name, $value)
    {
        $type_map = [
            static::TYPE_STRING => 'string',
            static::TYPE_INT => 'integer',
            static::TYPE_FLOAT => 'float',
            static::TYPE_BOOL => 'boolean',
            static::TYPE_ARRAY => 'array'
        ];

        $config = self::getSettingConfig();
        $setting = $config[$name];
        $rules = ['type' => $type_map[$setting['type']]];
        if (isset($setting['rules'])) {
            $rules = array_merge($rules, $setting['rules']);
        }
        return $rules;
    }

    public static function getQueryValue($name, $value)
    {
        if ($value === null) {
            return $value;
        }
        $config = self::getSettingConfig();
        switch ($config[$name]['type']) {
            case static::TYPE_INT:
            case static::TYPE_FLOAT:
                $value = (string) $value;
                break;
            case static::TYPE_BOOL:
                $value = $value === true ? '1' : '0';
                break;
            case static::TYPE_ARRAY:
                $value = json_encode($value);
                break;
        }
        return $value;
    }

    public static function getOutputValue($name, $value)
    {
        if ($value === null) {
            return $value;
        }
        $config = self::getSettingConfig();
        // depending on value type, convert data
        switch ($config[$name]['type']) {
            case static::TYPE_STRING:
                $value = (string) $value;
                break;
            case static::TYPE_INT:
                $value = (int) $value;
                break;
            case static::TYPE_FLOAT:
                $value = (float) $value;
                break;
            case static::TYPE_BOOL:
                $value = $value === '1';
                break;
            case static::TYPE_ARRAY:
                $value = json_decode($value, true);
                if (!is_array($value)) {
                    $value = [];
                }
                break;
        }
        return $value;
    }

    public function isNameInUse($user_id, $name)
    {
        return $this->newQuery()->where('userID', $user_id)->where('name', $name)->count() !== 0;
    }
}

<?php

declare(strict_types=1);

namespace app\Resources\Project;

use App\ResourceDelegates\Project\ProjectTypeLegacyDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\ProjectTypeLegacy;
use Core\Components\Resource\Classes\{Resource};

/**
 * Class ProjectTypeLegacyResource
 *
 * @package App\Resources\Project
 */
class ProjectTypeLegacyResource extends Resource
{
    use BulkActionTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_SEARCH | self::ACTION_GROUP_BATCH);

    /**
     * @var string Table name
     */
    protected $table = 'projectTypesLegacy';

    /**
     * @var string Model class name
     */
    protected $model = ProjectTypeLegacy::class;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(ProjectTypeLegacyDelegate::class);
    }

}

<?php

declare(strict_types=1);

namespace App\Resources\Company\Form\Item\Field;

use App\Interfaces\Resource\FormFieldProductResourceInterface;
use App\ResourceDelegates\Company\Form\Item\Field\ProductDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanyFormItemFieldProduct;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;

/**
 * Class ProductResource
 *
 * @package App\Resources\Company\Form\Item\Field
 */
class ProductResource extends Resource implements FormFieldProductResourceInterface
{
    use UserActionTrackingTrait;

    /**
     * @var int Actions which are allowed
     */
    protected $available_actions = (self::ACTION_GROUP_READ_ONLY | self::ACTION_GROUP_CREATE | self::ACTION_NESTED_UPDATE | self::ACTION_DELETE) & ~self::ACTION_GROUP_BATCH;

    /**
     * @var string Table name
     */
    protected $table = 'companyFormItemFieldProducts';

    /**
     * @var string Model associated with resource
     */
    protected $model = CompanyFormItemFieldProduct::class;

    /**
     * @var bool Determines if resource can be used without user info
     */
    protected $allow_no_user = true;

    /**
     * Get available types
     *
     * @return array
     */
    public static function getTypes()
    {
        return [
            static::TYPE_PRODUCT_CATEGORY, static::TYPE_PRODUCT_ITEM
        ];
    }

    /**
     * Get available actions
     *
     * @return array
     */
    public static function getActions()
    {
        return [
            static::ACTION_ADD, static::ACTION_REMOVE
        ];
    }

    /**
     * Boot resource
     */
    protected static function boot()
    {
        static::delegate(ProductDelegate::class);
    }

    /**
     * Delete any products of specified field id whose id is not in passed array
     *
     * @param string $field_id
     * @param array $product_ids
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteMissingProductsByFieldID(string $field_id, array $product_ids): void
    {
        $primary_field = $this->getPrimaryField();
        $field_id = $this->getFields()->get('field_id')
            ->saveValue($field_id);
        $product_ids = array_map(function ($id) use ($primary_field) {
            return $primary_field->saveValue($id);
        }, $product_ids);
        $this->newScopedQuery()
            ->where("{$this->table}.companyFormItemFieldID", $field_id)
            ->whereNotIn("{$this->table}.companyFormItemFieldProductID", $product_ids)
            ->each(function ($product) use ($primary_field) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($product)
                ]))
                    ->force()
                    ->run();
            });
    }
}

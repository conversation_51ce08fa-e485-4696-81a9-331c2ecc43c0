<?php
declare(strict_types=1);

namespace App\Resources\LeadForm;

use App\ResourceDelegates\LeadForm\FieldDelegate;
use Common\Models\LeadFormField;
use Core\Components\Resource\Classes\Resource;

/**
 * Class FieldResource
 *
 * @package App\Resources
 */
class FieldResource extends Resource
{
    const TYPE_FREEFORM = 1;
    const TYPE_DROPDOWN = 2;
    const TYPE_TEXTAREA = 3;
    const TYPE_BOOLEAN = 4;
    const TYPE_FILE = 5;

    const LABEL_DEFAULT_FIRST_NAME = 'First Name';
    const LABEL_DEFAULT_LAST_NAME = 'Last Name';
    const LABEL_DEFAULT_EMAIL = 'Email';
    const LABEL_DEFAULT_PHONE = 'Phone';
    const LABEL_DEFAULT_ADDRESS = 'Address';
    const LABEL_DEFAULT_MARKETING_SOURCE = 'How did you hear about us?';
    const LABEL_DEFAULT_PROJECT_TYPE = 'Type of service needed';
    const LABEL_DEFAULT_CUSTOMER_NOTES = 'Notes';
    CONST LABEL_DEFAULT_EMAIL_CHECKBOX = 'I agree to receiving emails';

    CONST LABEL_DEFAULT_UPLOAD_FILE = 'Upload File';
    CONST LABEL_DEFAULT_APPOINTMENT_REQUEST = 'What days/times work best for you? (Choose 3)';

    const REFERENCE_FIRST_NAME = 'first_name';
    const REFERENCE_LAST_NAME = 'last_name';
    const REFERENCE_EMAIL = 'email';
    const REFERENCE_PHONE = 'phone';
    const REFERENCE_ADDRESS = 'address';
    const REFERENCE_MARKETING_SOURCE = 'marketing_source';
    const REFERENCE_PROJECT_TYPE = 'project_type';
    const REFERENCE_CUSTOMER_NOTES = 'customer_notes';
    const REFERENCE_EMAIL_CHECKBOX = 'email_checkbox';
    const REFERENCE_UPLOAD_FILE = 'upload_file';
    const REFERENCE_APPOINTMENT_REQUEST = 'appointment_request';

    protected $table = 'leadFormFields';
    protected $model = LeadFormField::class;
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);
    protected $allow_no_user = false;
    protected $generate_id = true;

    public static function getTypes(): array
    {
        return [self::TYPE_FREEFORM, self::TYPE_DROPDOWN, self::TYPE_TEXTAREA];
    }

    public static function getAllReferences(): array
    {
        return [
            self::REFERENCE_FIRST_NAME,
            self::REFERENCE_LAST_NAME,
            self::REFERENCE_EMAIL,
            self::REFERENCE_PHONE,
            self::REFERENCE_ADDRESS,
            self::REFERENCE_MARKETING_SOURCE,
            self::REFERENCE_PROJECT_TYPE,
            self::REFERENCE_CUSTOMER_NOTES,
            self::REFERENCE_EMAIL_CHECKBOX,
            self::REFERENCE_UPLOAD_FILE,
            self::REFERENCE_APPOINTMENT_REQUEST,
        ];
    }

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(FieldDelegate::class);
    }
}

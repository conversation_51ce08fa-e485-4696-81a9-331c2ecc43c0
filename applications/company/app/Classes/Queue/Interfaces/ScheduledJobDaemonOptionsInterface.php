<?php

declare(strict_types=1);

namespace App\Classes\Queue\Interfaces;

use Carbon\Carbon;
use Core\Components\Queue\Interfaces\DaemonOptionsInterface;

/**
 * Interface ScheduledJobDaemonOptionsInterface
 *
 * @package App\Classes\Queue\Interfaces
 */
interface ScheduledJobDaemonOptionsInterface extends DaemonOptionsInterface
{
    /**
     * Set now time instance
     *
     * @param Carbon|null $now
     */
    public function setNow(?Carbon $now): void;

    /**
     * Get now time instance
     *
     * @return Carbon|null
     */
    public function getNow(): ?Carbon;

    /**
     * Set if worker stops when all jobs are done
     *
     * @param bool $stop
     */
    public function setStopWhenEmpty(bool $stop): void;

    /**
     * Get stop when empty setting
     *
     * @return bool
     */
    public function getStopWhenEmpty(): bool;
}

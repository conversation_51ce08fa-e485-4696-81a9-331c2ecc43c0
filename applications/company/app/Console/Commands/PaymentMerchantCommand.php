<?php
declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Acl;
use Core\Classes\Str;
use App\Traits\Console\{NowTrait, OutputExceptionTrait};
use App\Services\CompanyFeatureService;
use App\Services\Payment\CoPilot\Api\CoPilotApiService;
use App\Services\Payment\PaymentLinkService;
use App\ResourceDelegates\Payment\PropelrMerchantDelegate;
use App\Resources\Payment\PropelrMerchantResource;
use App\Services\Email\Types\User\PaymentsLiveType;
use Core\StaticAccessors\Auth;
use Common\Models\Company;
use Common\Models\Feature;
use Common\Models\PropelrMerchant;
use Common\Models\PaymentLink;
use Core\Components\Console\Commands\BaseCommand;
use Core\Components\DB\StaticAccessors\DB;
use Throwable;
use Ramsey\Uuid\Uuid;
use function Core\Functions\env;

class PaymentMerchantCommand extends BaseCommand
{
    use NowTrait;
    use OutputExceptionTrait;

    /**
     * Get company from command arguments
     */
    protected function getCompany(): ?Company
    {
        $company_id = $this->console->get('company-id', 'Company ID');
        if (!is_numeric($company_id)) {
            $this->console->error('Company ID must be numeric');
            return null;
        }
        $company_id = (int)$company_id;
        $company = Company::find($company_id);
        if ($company === null) {
            $this->console->error('Unable to find company with ID: %s', $company_id);
            return null;
        }
        $this->console->info('Company: %s', $company->name);
        if (!$this->console->confirm('Is the above company correct?')) {
            $this->console->error('Aborted');
            return null;
        }
        return $company;
    }

    /**
     * Get merchant credentials from command arguments or prompt user
     */
    protected function getMerchantCredentials(): ?array
    {
        if ($this->args->has('test')) {
            $this->console->info('Using testing credentials (--test flag detected)');
            return [
                'cc_mid' => '800000009664',
                'ach_mid' => 'BCX202592124552',
                'gateway_username' => 'testing',
                'gateway_password' => 'testing123'
            ];
        }

        // Check for custom parameters
        $cc_mid = $this->args->get('cc-mid');
        $ach_mid = $this->args->get('ach-mid');
        $gateway_username = $this->args->get('gateway-username');
        $gateway_password = $this->args->get('gateway-password');

        if ($cc_mid && $ach_mid && $gateway_username && $gateway_password) {
            $this->console->info('Using provided custom credentials');
            return [
                'cc_mid' => $cc_mid,
                'ach_mid' => $ach_mid,
                'gateway_username' => $gateway_username,
                'gateway_password' => $gateway_password
            ];
        }

        // Check if partial params were provided
        $provided_params = array_filter([
            'cc-mid' => $cc_mid,
            'ach-mid' => $ach_mid,
            'gateway-username' => $gateway_username,
            'gateway-password' => $gateway_password
        ]);

        if (!empty($provided_params)) {
            $this->console->error('When providing custom credentials, all parameters are required:');
            $this->console->error('  --cc-mid, --ach-mid, --gateway-username, --gateway-password');
            $this->console->error('Provided: %s', implode(', ', array_keys($provided_params)));
            $this->console->error('Use --test flag for default testing credentials');
            return null;
        }

        $this->console->info('Interactive mode - please provide merchant credentials:');
        
        $cc_mid = $this->console->get('cc-mid', 'Credit Card MID');
        if (!$cc_mid) {
            $this->console->error('Credit Card MID is required');
            return null;
        }

        $ach_mid = $this->console->get('ach-mid', 'ACH MID');
        if (!$ach_mid) {
            $this->console->error('ACH MID is required');
            return null;
        }

        $gateway_username = $this->console->get('gateway-username', 'Gateway Username');
        if (!$gateway_username) {
            $this->console->error('Gateway Username is required');
            return null;
        }

        $gateway_password = $this->console->get('gateway-password', 'Gateway Password');
        if (!$gateway_password) {
            $this->console->error('Gateway Password is required');
            return null;
        }

        return [
            'cc_mid' => $cc_mid,
            'ach_mid' => $ach_mid,
            'gateway_username' => $gateway_username,
            'gateway_password' => $gateway_password
        ];
    }

    /**
     * Onboard a new payment merchant with testing credentials
     */
    public function onboardMerchant(): void
    {
        try {
            if (($company = $this->getCompany()) === null) {
                return;
            }

            // Get merchant credentials based on command arguments
            $credentials = $this->getMerchantCredentials();
            if ($credentials === null) {
                return;
            }

            $this->console->line('Creating merchant with credentials:');
            $this->console->line('  Credit Card MID: %s', $credentials['cc_mid']);
            $this->console->line('  ACH MID: %s', $credentials['ach_mid']);
            $this->console->line('  Username: %s', $credentials['gateway_username']);
            $this->console->line('  Password: %s', $credentials['gateway_password']);
            $this->console->line();

            // Check if merchant with either MID already exists
            $existing_cc_merchant = PropelrMerchant::where('ccMID', $credentials['cc_mid'])->first();
            $existing_ach_merchant = PropelrMerchant::where('achMID', $credentials['ach_mid'])->first();
            
            if ($existing_cc_merchant) {
                $this->console->error('Merchant with CC MID %s already exists (ID: %s)', $credentials['cc_mid'], $existing_cc_merchant->propelrMerchantID);
                return;
            }
            
            if ($existing_ach_merchant) {
                $this->console->error('Merchant with ACH MID %s already exists (ID: %s)', $credentials['ach_mid'], $existing_ach_merchant->propelrMerchantID);
                return;
            }

            // Generate payment link token - ensure uniqueness before transaction
            $max_attempts = 10;
            $attempts = 0;
            do {
                $short_token = Str::random(32, Str::CHARS_GROUP_ALPHA_NUMERIC);
                $existing_link = PaymentLink::where('shortToken', $short_token)->first();
                $attempts++;
            } while ($existing_link && $attempts < $max_attempts);
            
            if ($existing_link) {
                $this->console->error('Unable to generate unique token after %d attempts', $max_attempts);
                return;
            }

            // Create merchant and payment link in a transaction
            try {
                $result = DB::transaction(function () use ($company, $credentials, $short_token) {
                    $merchant = PropelrMerchant::create([
                        'propelrMerchantID' => UUID::uuid4()->getBytes(),
                        'companyUUID' => $company->companyUUID,
                        'ccMID' => $credentials['cc_mid'],
                        'achMID' => $credentials['ach_mid'],
                        'status' => PropelrMerchant::APPLICATION_STATUS_LIVE,
                        'gatewayStatus' => PropelrMerchant::GATEWAY_STATUS_BOARDED,
                        'gatewayUsername' => $credentials['gateway_username'],
                        'gatewayPasswordEnc' => $credentials['gateway_password'], // Plain text for testing (// @TODO: FILIPE: Use AWS KMS here in the future.
                        'boardedAt' => $this->getNow(),
                        'submittedAt' => $this->getNow(),
                        'createdByUserID' => null, // System user for console operations
                    ]);

                    // Create payment link
                    $payment_link = PaymentLink::create([
                        'paymentLinkID' => UUID::uuid4()->getBytes(),
                        'propelrMerchantID' => $merchant->propelrMerchantID,
                        'shortToken' => $short_token,
                        'createdByUserID' => null, // System user for console operations
                    ]);

                    return [
                        'merchant' => $merchant,
                        'payment_link' => $payment_link,
                    ];
                });

                $merchant = $result['merchant'];
                $payment_link = $result['payment_link'];

                $this->console->info('✓ Merchant created successfully');
                $this->console->info('✓ Payment link created successfully');

                // Check if payments feature should be enabled
                $payments_feature_enabled = false;
                if ($this->args->has('enable-payments-feature')) {
                    try {
                        $feature_service = CompanyFeatureService::getByCompanyID($company->companyID);
                        $feature_service->enable(Feature::PAYMENTS);
                        $payments_feature_enabled = true;
                        $this->console->info('✓ Payments feature enabled for company');
                    } catch (Throwable $feature_error) {
                        $this->console->line('Failed to enable payments feature: %s', $feature_error->getMessage());
                    }
                }

            } catch (Throwable $transaction_error) {
                $this->console->error('Failed to create merchant and payment link: %s', $transaction_error->getMessage());
                $this->console->error('All changes have been rolled back.');
                return;
            }

            $base_url = env('APP_BASE_URL', 'http://localhost');

            // Display summary
            $this->console->line();
            $this->console->line('═══════════════════════════════════════');
            $this->console->line('Merchant Onboarding Complete');
            $this->console->line('═══════════════════════════════════════');
            $this->console->line('Merchant Details:');
            $this->console->line('  - Merchant ID: %s', UUID::fromBytes($merchant->propelrMerchantID)->toString());
            $this->console->line('  - Credit Card MID: %s', $credentials['cc_mid']);
            $this->console->line('  - ACH MID: %s', $credentials['ach_mid']);
            $this->console->line('  - Company: %s (UUID: %s)', $company->name, UUID::fromBytes($company->companyUUID)->toString());
            $this->console->line('  - Status: Approved & Boarded');
            $this->console->line('  - Payments Feature: %s', $payments_feature_enabled ? 'Enabled' : 'Not Enabled');
            $this->console->line();
            $this->console->line('Payment Link Details:');
            $this->console->line('  - Link ID: %s', UUID::fromBytes($payment_link->paymentLinkID)->toString());
            $this->console->line('  - URL: %s', rtrim($base_url, '/') . '/payments/' . $short_token);
            $this->console->line('═══════════════════════════════════════');
            
            $this->console->info('Merchant onboarding completed successfully!');

        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }

    /**
     * Generate signature URL for CoPilot merchant
     */
    public function generateSignature(): void
    {
        try {
            // Get CoPilot merchant ID or company ID from arguments
            $copilot_merchant_id = $this->args->get('copilot-merchant-id');
            $company_id = $this->args->get('company-id');
            $regenerate = $this->args->has('regenerate');
            $debug = $this->args->has('debug');

            if (!$copilot_merchant_id && !$company_id) {
                $this->console->error('Either --copilot-merchant-id or --company-id is required');
                $this->console->error('Usage: php console payments:merchant:generate-signature --copilot-merchant-id=123 [--regenerate] [--debug]');
                $this->console->error('   or: php console payments:merchant:generate-signature --company-id=456 [--regenerate] [--debug]');
                return;
            }

            if ($debug) {
                $this->console->info('Debug mode enabled - detailed logging will be shown');
            }

            $merchant = null;
            if ($copilot_merchant_id) {
                $merchant = PropelrMerchant::where('copilotMerchantID', $copilot_merchant_id)
                    ->whereNull('deletedAt')
                    ->first();
                    
                if (!$merchant) {
                    $this->console->error('No merchant found with CoPilot Merchant ID: %s', $copilot_merchant_id);
                    return;
                }
            } else {
                // Find by company ID
                $company = Company::where('companyID', $company_id)->first();
                if (!$company) {
                    $this->console->error('No company found with ID: %s', $company_id);
                    return;
                }

                $merchant = PropelrMerchant::where('companyUUID', $company->companyUUID)
                    ->whereNull('deletedAt')
                    ->first();
                    
                if (!$merchant) {
                    $this->console->error('No merchant found for company ID: %s', $company_id);
                    return;
                }
            }

            $this->console->info('Found merchant ID: %s', $merchant->propelrMerchantID);
            $this->console->info('Company UUID: %s', $merchant->companyUUID);
            $this->console->info('CoPilot Merchant ID: %s', $merchant->copilotMerchantID);

            if (!$merchant->copilotMerchantID) {
                $this->console->error('CoPilot merchant not created. Please create merchant first.');
                return;
            }

            // Check if regenerate flag is needed
            if (!$regenerate) {
                if ($merchant->signatureUrl) {
                    $this->console->error('Merchant already has signature URL: %s', $merchant->signatureUrl);
                    $this->console->error('Use --regenerate flag to override existing signature URL and status');
                    return;
                }

                if ($merchant->status !== PropelrMerchant::APPLICATION_STATUS_CREATED) {
                    $this->console->error('Merchant not in correct status for signature generation.');
                    $this->console->error('Current status: %s', $merchant->getApplicationStatusName());
                    $this->console->error('Expected status: CREATED');
                    $this->console->error('Use --regenerate flag to override status check');
                    return;
                }
            } else {
                $this->console->line('--regenerate flag detected. Will override existing signature URL and status.');
            }

            // Initialize services
            $merchant_delegate = new PropelrMerchantDelegate();
            $merchant_resource = PropelrMerchantResource::make(Acl::make());
            $copilot_service = new CoPilotApiService();

            $this->console->info('Generating signature URL...');

            // Call CoPilot API
            $result = $copilot_service->generateSignature($merchant->copilotMerchantID);

            if (!$result['success']) {
                $this->console->error('CoPilot signature generation failed:');
                
                if (isset($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        $this->console->error('  - %s', $error['message'] ?? 'Unknown error');
                        if (isset($error['field'])) {
                            $this->console->error('    Field: %s', $error['field']);
                        }
                        if (isset($error['code'])) {
                            $this->console->error('    Code: %s', $error['code']);
                        }
                    }

                    // Store error details using merchant delegate
                    $merchant_delegate->recordSignatureError($merchant, $result['errors'][0]);
                }

                $this->console->error('Signature generation failed.');
                return;
            }

            // Update merchant with PENDING_SIGNATURE status using merchant delegate
            $merchant_delegate->transitionToPendingSignature($merchant, $result['signatureUrl'], $merchant_resource);

            $this->console->line();
            $this->console->line('═══════════════════════════════════════');
            $this->console->line('Signature Generation Complete');
            $this->console->line('═══════════════════════════════════════');
            $this->console->line('Merchant ID: %s', $merchant->propelrMerchantID);
            $this->console->line('CoPilot Merchant ID: %s', $merchant->copilotMerchantID);
            $this->console->line('Signature URL: %s', $result['signatureUrl']);
            $this->console->line('Status: PENDING_SIGNATURE');
            $this->console->line('Action: sign_document');
            $this->console->line('═══════════════════════════════════════');
            
            $this->console->info('✓ Signature URL generated successfully!');

        } catch (Throwable $e) {
            $this->console->error('Error generating signature: %s', $e->getMessage());
            $this->outputException($e);
        }
    }

    /**
     * Set payment merchant to go-live status
     */
    public function goLiveMerchant(): void
    {
        try {
            if (($company = $this->getCompany()) === null) {
                return;
            }

            $merchant = PropelrMerchant::where('companyUUID', $company->companyUUID)
                ->whereNull('deletedAt')
                ->first();

            if (!$merchant) {
                $this->console->error('No merchant found for company: %s', $company->name);
                return;
            }

            $this->console->info('Found merchant:');
            $this->console->info('  - Merchant ID: %s', UUID::fromBytes($merchant->propelrMerchantID)->toString());
            $this->console->info('  - Current Status: %s', $merchant->getApplicationStatusName());
            $this->console->info('  - Ready to Process: %s', $merchant->getReadyToProcessName());

            if (!in_array($merchant->status, [PropelrMerchant::APPLICATION_STATUS_BOARDED, PropelrMerchant::APPLICATION_STATUS_LIVE])) {
                $this->console->error('Merchant must have status "boarded" or "live" to go live.');
                $this->console->error('Current status: %s', $merchant->getApplicationStatusName());
                return;
            }

            // Check if already ready to process
            if ($merchant->isReadyToProcess === PropelrMerchant::IS_READY_TO_PROCESS_READY) {
                $this->console->line('Merchant is already set to ready to process.');
                if (!$this->console->confirm('Do you want to continue anyway?')) {
                    $this->console->error('Aborted');
                    return;
                }
            }

            // Find existing payment link
            $payment_link = PaymentLink::where('propelrMerchantID', $merchant->propelrMerchantID)->first();
            $payment_link_created = false;

            if (!$payment_link) {
                $this->console->info('No payment link found. Creating one...');

                try {
                    $payment_link_service = new PaymentLinkService();
                    $merchant_uuid = UUID::fromBytes($merchant->propelrMerchantID)->toString();
                    $payment_link = $payment_link_service->createLinkForMerchant($merchant_uuid, 0);

                    if (!$payment_link) {
                        $this->console->error('Failed to create payment link');
                        return;
                    }

                    $payment_link_created = true;
                    $this->console->info('✓ Payment link created successfully');

                } catch (Throwable $e) {
                    $this->console->error('Failed to create payment link: %s', $e->getMessage());
                    return;
                }
            }

            // Update merchant to ready-to-process
            try {
                $merchant->update([
                    'isReadyToProcess' => PropelrMerchant::IS_READY_TO_PROCESS_READY,
                    'isReadyToProcessAt' => $this->getNow(),
                ]);

                $this->console->info('✓ Merchant set to ready to process');

            } catch (Throwable $e) {
                $this->console->error('Failed to update merchant: %s', $e->getMessage());
                return;
            }

            $base_url = env('APP_BASE_URL', 'http://localhost');

            // Display summary
            $this->console->line();
            $this->console->line('═══════════════════════════════════════');
            $this->console->line('Merchant Go-Live Complete');
            $this->console->line('═══════════════════════════════════════');
            $this->console->line('Company: %s', $company->name);
            $this->console->line('Merchant ID: %s', UUID::fromBytes($merchant->propelrMerchantID)->toString());
            $this->console->line('Status: %s', $merchant->getApplicationStatusName());
            $this->console->line('Ready to Process: %s', $merchant->getReadyToProcessName());
            $this->console->line();
            $this->console->line('Payment Link:');
            $this->console->line('  - Status: %s', $payment_link_created ? 'Created' : 'Existing');
            $this->console->line('  - URL: %s', rtrim($base_url, '/') . '/payments/' . $payment_link->shortToken);
            $this->console->line('═══════════════════════════════════════');

            $this->console->info('✓ Merchant go-live completed successfully!');

            // Send email notification to customer
            PaymentsLiveType::send(['merchant_id' => $merchant->propelrMerchantID]);
            $this->console->info('✓ Email notification sent to primary users');

        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }
}
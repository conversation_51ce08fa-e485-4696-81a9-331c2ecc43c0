<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Log;
use App\Services\Payment\PaymentLogger;
use App\Services\Payment\Propelr\Api\CardConnectApiService;
use Carbon\Carbon;
use Common\Models\PropelrMerchant;
use Common\Models\PropelrTransaction;
use Core\Components\Console\Commands\BaseCommand;
use Exception;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Formatter\JsonFormatter;

/**
 * Class SettlementReconcilerCommand
 *
 * Reconciles CardConnect settlement statuses by querying the settlestat endpoint
 * and updating transaction records with settlement information.
 *
 * @package App\Console\Commands
 */
class SettlementReconcilerCommand extends BaseCommand
{
    private CardConnectApiService $card_connect_api;
    private Logger $reconciliation_logger;
    private bool $dry_run = false;
    private int $processed_count = 0;
    private int $settled_count = 0;
    private int $under_review_count = 0;
    private int $rejected_count = 0;
    private int $pending_count = 0;
    private int $error_count = 0;
    private int $skipped_count = 0;
    private string $settlement_date_et;

    public function __construct()
    {
        $this->card_connect_api = new CardConnectApiService();
        $this->reconciliation_logger = $this->createReconciliationLogger();
    }

    /**
     * Create a dedicated logger for reconciliation with JSON formatting
     */
    private function createReconciliationLogger(): Logger
    {
        return Log::create('payments_reconciliation', [
            'file' => 'payments_reconciliation.log',
            'app_processor' => false // Disable app processor for cleaner JSON logs
        ]);
    }

    /**
     * Generate transaction key for logging (format: mid:retref)
     */
    private function getTransactionKey(string $mid, string $retref): string
    {
        return $mid . ':' . $retref;
    }

    /**
     * Log structured reconciliation data with transaction_key for easy filtering
     */
    private function logReconciliation(string $level, string $message, array $context = [], ?string $transaction_key = null): void
    {
        $log_data = ['context' => $context];
        
        if ($transaction_key) {
            $log_data['transaction_key'] = $transaction_key;
        }

        switch (strtolower($level)) {
            case 'info':
                $this->reconciliation_logger->info($message, $log_data);
                break;
            case 'error':
                $this->reconciliation_logger->error($message, $log_data);
                break;
            case 'warning':
                $this->reconciliation_logger->warning($message, $log_data);
                break;
            default:
                $this->reconciliation_logger->info($message, $log_data);
        }
    }

    /**
     * Reconcile settlement statuses for CardConnect transactions
     * 
     * @throws Exception
     */
    public function reconcile(): void
    {
        $this->dry_run = $this->args->has('dry-run');
        $merchant_id = $this->args->get('merchant-id');
        $date = $this->args->get('date');

        if (!$date) {
            $this->console->error('--date parameter is required (format: YYYY-MM-DD)');
            return;
        }

        if ($this->dry_run) {
            $this->console->info('DRY RUN MODE - No changes will be made to the database');
        }

        $this->console->info('Starting settlement reconciliation...');
        $merchants = $this->getMerchantsToProcess($merchant_id);
        
        if ($merchants->isEmpty()) {
            $this->console->error('No merchants found to process');
            return;
        }

        $this->console->info('Processing %d merchant(s)', $merchants->count());
        $date = $this->getDatesToProcess($date);
        
        foreach ($merchants as $merchant) {
            $this->processMerchantSettlement($merchant, $date);
        }

        $this->printSummary();
    }

    /**
     * Get merchants to process based on command options
     */
    private function getMerchantsToProcess(?string $merchant_id)
    {
        $query = PropelrMerchant::query()
            ->where('gatewayStatus', PropelrMerchant::GATEWAY_STATUS_BOARDED)
            ->where('status', PropelrMerchant::APPLICATION_STATUS_APPROVED)
            ->whereNull('deletedAt');

        if ($merchant_id) {
            $query->where(function($q) use ($merchant_id) {
                $q->where('ccMID', $merchant_id)
                  ->orWhere('achMID', $merchant_id);
            });
        }

        return $query->get();
    }

    /**
     * Get dates to process in MMDD format for CardConnect API
     */
    private function getDatesToProcess(?string $date): string
    {
        if ($date) {
            // Validate YYYY-MM-DD format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                throw new Exception('Date must be in YYYY-MM-DD format (e.g., 2025-08-15)');
            }
            
            // Store the original date for settlementDate field
            $this->settlement_date_et = $date;

            $parsed_date = \DateTime::createFromFormat('Y-m-d', $date);
            return $parsed_date->format('md');  // MMDD format
        }

        // Default: yesterday only in America/New_York timezone
        $ny_timezone = new \DateTimeZone('America/New_York');
        $yesterday = Carbon::now($ny_timezone)->subDay();
        
        // Store the original date for settlementDate field
        $this->settlement_date_et = $yesterday->format('Y-m-d');

        return $yesterday->format('md'); // MMDD format
    }

    /**
     * Process settlement reconciliation for a specific merchant (both MIDs)
     */
    private function processMerchantSettlement(PropelrMerchant $merchant, string $date): void
    {
        $this->console->info('Processing merchant: CC MID %s, ACH MID %s', 
            $merchant->ccMID, 
            $merchant->achMID
        );

        // Process Credit Card MID
        if ($merchant->ccMID) {
            $this->console->line('  Processing Credit Card MID: %s', $merchant->ccMID);
            $this->processMerchantMid($merchant, $merchant->ccMID, 'CC', $date);
        }

        // Process ACH MID  
        if ($merchant->achMID) {
            $this->console->line('  Processing ACH MID: %s', $merchant->achMID);
            $this->processMerchantMid($merchant, $merchant->achMID, 'ACH', $date);
        }
    }

    /**
     * Process settlement reconciliation for a specific merchant and MID
     */
    private function processMerchantMid(PropelrMerchant $merchant, string $mid, string $mid_type, string $date): void
    {
        try {
            $this->processSettlementDate($merchant, $mid, $mid_type, $date);
        } catch (Exception $e) {
            $this->console->error('    Error processing %s MID %s: %s', $mid_type, $mid, $e->getMessage());
            $this->error_count++;

            // Log structured error for monitoring
            PaymentLogger::getInstance()->error('RECONCILE:SETTLEMENT', 'Settlement reconciliation API error', [
                'merchant_mid' => $mid,
                'mid_type' => $mid_type,
                'date' => $date,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Process settlement data for a specific merchant and date
     */
    private function processSettlementDate(PropelrMerchant $merchant, string $mid, string $mid_type, string $date): void
    {
        $this->console->line('    Querying settlements for %s MID %s on date: %s', $mid_type, $mid, $date);
        $batches = $this->card_connect_api->settlestat($mid, $date);

        if (empty($batches)) {
            $this->console->line('  No batches found for date %s', $date);
            return;
        }

        $this->console->info('  Found %d batch(es) for processing', count($batches));

        foreach ($batches as $batch) {
            if (empty($batch['txns'])) {
                $this->console->line('  Batch %s has no transactions, skipping', $batch['batchid'] ?? 'unknown');
                continue;
            }

            // Normalize response -- handle both single object and array of objects
            $transactions = [];
            if (isset($batch['txns']['retref'])) {
                // Single transaction object
                $transactions = [$batch['txns']];
            } elseif (is_array($batch['txns'])) {
                // Array of transaction objects
                $transactions = $batch['txns'];
            }
            
            $this->console->info('  Batch %s: Found %d transaction(s) for processing', 
                $batch['batchid'] ?? 'unknown', 
                count($transactions)
            );

            foreach ($transactions as $settlement_txn) {
                $this->processTransaction($merchant, $settlement_txn, $batch, $mid, $mid_type);
            }
        }
    }

    /**
     * Process a single settlement transaction
     */
    private function processTransaction(PropelrMerchant $merchant, array $settlement_txn, array $batch, string $mid, string $mid_type): void
    {
        $retref = $settlement_txn['retref'] ?? null;
        $setlstat = $settlement_txn['setlstat'] ?? null;

        if (!$retref) {
            $this->console->info('Transaction missing retref, skipping');
            $this->error_count++;
            return;
        }

        $this->processed_count++;
        $transaction_key = $this->getTransactionKey($mid, $retref);

        // Find transaction in database
        $transaction = PropelrTransaction::query()
            ->where('retref', $retref)
            ->where('propelrMerchantID', $merchant->propelrMerchantID)
            ->first();

        if (!$transaction) {
            $this->console->info('Transaction %s not found in database', $retref);
            $this->logReconciliation('error', 'Transaction not found in database', [
                'retref' => $retref,
                'merchant_mid' => $mid,
                'mid_type' => $mid_type,
                'settlement_date' => $this->settlement_date_et
            ], $transaction_key);
            $this->error_count++;
            return;
        }

        // Skip if already in terminal settled state (settled or funded)
        if ($transaction->isSettled() || $transaction->isFunded()) {
            $this->console->line('Transaction %s already in terminal state (%s), skipping', $retref, $transaction->getStatusName());
            $this->logReconciliation('info', 'Transaction already in terminal state, skipping', [
                'retref' => $retref,
                'merchant_mid' => $mid,
                'mid_type' => $mid_type,
                'current_status' => $transaction->getStatusName(),
                'settlement_date' => $this->settlement_date_et
            ], $transaction_key);
            $this->skipped_count++;
            return;
        }

        // Process based on settlement status
        switch ($setlstat) {
            case 'Y':
                $this->processSettledStatus($transaction, $settlement_txn, $batch, $mid, $mid_type);
                break;
            case 'W':
                $this->processUnderReviewStatus($transaction, $settlement_txn, $batch, $mid, $mid_type);
                break;
            case 'N':
                $this->processRejectedStatus($transaction, $settlement_txn, $batch, $mid, $mid_type);
                break;
            case 'F':
            case 'T':
                $this->processErrorStatus($transaction, $settlement_txn, $batch, $setlstat, $mid, $mid_type);
                break;
            case 'R':
            default:
                $this->processPendingStatus($transaction, $settlement_txn, $batch, $mid, $mid_type);
                break;
        }
    }

    /**
     * Process a successfully settled transaction (Y)
     */
    private function processSettledStatus(PropelrTransaction $transaction, array $settlement_txn, array $batch, string $mid, string $mid_type): void
    {
        $retref = $settlement_txn['retref'];
        $this->console->info('  Transaction %s: Marking as SETTLED', $retref);
        $transaction_key = $this->getTransactionKey($mid, $retref);
        $old_status = $transaction->getStatusName();
        $new_status = PropelrTransaction::getStatusNameByCode(PropelrTransaction::STATUS_SETTLED);
        
        // Validate settled amount before processing
        $settled_amount = $settlement_txn['setlamount'] ?? null;
        
        // Skip settlement if amount is null, zero, negative, or invalid format
        if ($settled_amount === null || 
            !preg_match('/^\d+(\.\d{1,2})?$/', $settled_amount) || 
            (float)$settled_amount <= 0) {
            
            PaymentLogger::getInstance()->error('RECONCILE:SETTLEMENT', 'Invalid or missing settled amount - skipping settlement', [
                'retref' => $retref,
                'amount' => $settled_amount,
                'merchant_mid' => $mid
            ]);
            
            $this->console->error('  Transaction %s: Invalid or missing amount, skipping settlement', $retref);
            $this->skipped_count++;
            return;
        }
        
        if (!$this->dry_run) {
            $transaction->status = PropelrTransaction::STATUS_SETTLED;
            $transaction->settledAmount = $settled_amount;
            $transaction->setlStat = 'Y';
            
            // Get batch data from batch root level
            $transaction->batchID = $batch['batchid'] ?? null;
            $transaction->hostBatch = $batch['hostbatch'] ?? null;
            
            $transaction->settlementDate = $this->settlement_date_et;
            
            if (empty($transaction->settledAt)) {
                $transaction->settledAt = Carbon::now();
            }
            
            if (isset($settlement_txn['cardtype'])) {
                $cardtype = $settlement_txn['cardtype'];
                
                // Check if this is an ACH transaction and set account type
                if ($cardtype === 'ESAV') {
                    $transaction->achAccountType = PropelrTransaction::ACH_ACCOUNT_TYPE_SAVINGS;
                    $transaction->cardBrand = null; // Clear card brand for ACH
                } elseif ($cardtype === 'ECHK') {
                    $transaction->achAccountType = PropelrTransaction::ACH_ACCOUNT_TYPE_CHECKING;
                    $transaction->cardBrand = null; // Clear card brand for ACH
                } else {
                    // Regular credit card transaction
                    $transaction->cardBrand = $cardtype;
                }
            }
            
            $transaction->save();
        }
        
        // Log status change
        $this->logReconciliation('info', 'Transaction status updated', [
            'retref' => $retref,
            'merchant_mid' => $mid,
            'mid_type' => $mid_type,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'settled_amount' => $settled_amount,
            'batch_id' => $batch['batchid'] ?? null,
            'settlement_date' => $this->settlement_date_et,
            'dry_run' => $this->dry_run
        ], $transaction_key);
        
        // Validate amount
        $this->validateSettlementAmount($transaction, $settlement_txn);
        $this->settled_count++;
    }

    /**
     * Process a transaction under review (W)
     */
    private function processUnderReviewStatus(PropelrTransaction $transaction, array $settlement_txn, array $batch, string $mid, string $mid_type): void
    {
        $retref = $settlement_txn['retref'];
        $this->console->info('Transaction %s: UNDER_REVIEW', $retref);
        $transaction_key = $this->getTransactionKey($mid, $retref);
        $old_status = $transaction->getStatusName();
        $new_status = PropelrTransaction::getStatusNameByCode(PropelrTransaction::STATUS_UNDER_REVIEW);
        
        if (!$this->dry_run) {
            $transaction->status = PropelrTransaction::STATUS_UNDER_REVIEW;
            $transaction->setlStat = 'W';
            $transaction->settlementDate = $this->settlement_date_et;
            $transaction->save();
        }
        
        // Log status change
        $this->logReconciliation('info', 'Transaction status updated', [
            'retref' => $retref,
            'merchant_mid' => $mid,
            'mid_type' => $mid_type,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'batch_id' => $batch['batchid'] ?? null,
            'settlement_date' => $this->settlement_date_et,
            'dry_run' => $this->dry_run
        ], $transaction_key);
        
        $this->under_review_count++;
    }

    /**
     * Process a rejected transaction (N)
     */
    private function processRejectedStatus(PropelrTransaction $transaction, array $settlement_txn, array $batch, string $mid, string $mid_type): void
    {
        $retref = $settlement_txn['retref'];
        $this->console->error('Transaction %s: SETTLEMENT_REJECTED', $retref);
        $transaction_key = $this->getTransactionKey($mid, $retref);
        $old_status = $transaction->getStatusName();
        $new_status = PropelrTransaction::getStatusNameByCode(PropelrTransaction::STATUS_SETTLEMENT_REJECTED);
        
        if (!$this->dry_run) {
            $transaction->status = PropelrTransaction::STATUS_SETTLEMENT_REJECTED;
            $transaction->setlStat = 'N';
            $transaction->settlementDate = $this->settlement_date_et;
            $transaction->save();
        }
        
        // Log status change with error level
        $this->logReconciliation('error', 'Transaction status updated - REJECTED', [
            'retref' => $retref,
            'merchant_mid' => $mid,
            'mid_type' => $mid_type,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'batch_id' => $batch['batchid'] ?? null,
            'settlement_date' => $this->settlement_date_et,
            'dry_run' => $this->dry_run
        ], $transaction_key);
        
        $this->rejected_count++;
    }

    /**
     * Process a transaction with settlement error (F/T)
     */
    private function processErrorStatus(PropelrTransaction $transaction, array $settlement_txn, array $batch, string $setlstat, string $mid, string $mid_type): void
    {
        $error_type = $setlstat === 'F' ? 'FORMAT_ERROR' : 'TOKEN_ERROR';
        $retref = $settlement_txn['retref'];
        $this->console->error('Transaction %s: SETTLEMENT_ERROR (%s)', $retref, $error_type);
        $transaction_key = $this->getTransactionKey($mid, $retref);
        $old_status = $transaction->getStatusName();
        $new_status = PropelrTransaction::getStatusNameByCode(PropelrTransaction::STATUS_SETTLEMENT_ERROR);
        
        if (!$this->dry_run) {
            $transaction->status = PropelrTransaction::STATUS_SETTLEMENT_ERROR;
            $transaction->setlStat = $setlstat;
            $transaction->settlementDate = $this->settlement_date_et;
            $transaction->save();
        }
        
        // Log status change with error level
        $this->logReconciliation('error', 'Transaction status updated - SETTLEMENT_ERROR', [
            'retref' => $retref,
            'merchant_mid' => $mid,
            'mid_type' => $mid_type,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'error_type' => $error_type,
            'setlstat' => $setlstat,
            'batch_id' => $batch['batchid'] ?? null,
            'settlement_date' => $this->settlement_date_et,
            'dry_run' => $this->dry_run
        ], $transaction_key);
        
        $this->error_count++;
    }

    /**
     * Process a pending transaction (R or unknown)
     */
    private function processPendingStatus(PropelrTransaction $transaction, array $settlement_txn, array $batch, string $mid, string $mid_type): void
    {
        $retref = $settlement_txn['retref'];
        $this->console->info('Transaction %s: PENDING_SETTLEMENT', $retref);
        $transaction_key = $this->getTransactionKey($mid, $retref);
        $old_status = $transaction->getStatusName();
        $new_status = PropelrTransaction::getStatusNameByCode(PropelrTransaction::STATUS_PENDING_SETTLEMENT);
        
        if (!$this->dry_run) {
            $transaction->status = PropelrTransaction::STATUS_PENDING_SETTLEMENT;
            $transaction->setlStat = 'R';
            $transaction->settlementDate = $this->settlement_date_et;
            $transaction->save();
        }
        
        // Log status change
        $this->logReconciliation('info', 'Transaction status updated', [
            'retref' => $retref,
            'merchant_mid' => $mid,
            'mid_type' => $mid_type,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'batch_id' => $batch['batchid'] ?? null,
            'settlement_date' => $this->settlement_date_et,
            'dry_run' => $this->dry_run
        ], $transaction_key);
        
        $this->pending_count++;
    }

    /**
     * Validate settlement amount against expected amount
     */
    private function validateSettlementAmount(PropelrTransaction $transaction, array $settlement_txn): void
    {
        if (!isset($settlement_txn['setlamount']) || !$transaction->totalAmount) {
            return;
        }
        
        $settled_amount = (float) $settlement_txn['setlamount'];
        $expected_amount = (float) $transaction->totalAmount;
        
        if (abs($settled_amount - $expected_amount) > 0.01) {
            $this->console->error(sprintf(
                'Amount mismatch for %s: Expected %.2f, Settled %.2f',
                $settlement_txn['retref'],
                $expected_amount,
                $settled_amount
            ));
            
            PaymentLogger::getInstance()->error('RECONCILE:SETTLEMENT', 'Settlement amount mismatch', [
                'retref' => $settlement_txn['retref'],
                'merchant_mid' => $transaction->merchant->mid ?? 'unknown',
                'expected_amount' => $expected_amount,
                'settled_amount' => $settled_amount,
                'transaction_id' => $transaction->propelrTransactionID
            ]);
            
            $this->error_count++;
        }
    }

    /**
     * Print summary of reconciliation results
     */
    private function printSummary(): void
    {
        $this->console->line('');
        $this->console->info('Settlement Reconciliation Summary:');
        $this->console->info('Date: %s', $this->settlement_date_et);
        $this->console->info('Processed: %d transactions', $this->processed_count);
        $this->console->info('Settled: %d transactions', $this->settled_count);
        $this->console->info('Under Review: %d transactions', $this->under_review_count);
        $this->console->info('Pending Settlement: %d transactions', $this->pending_count);
        $this->console->info('Rejected: %d transactions', $this->rejected_count);
        $this->console->info('Skipped (already settled): %d transactions', $this->skipped_count);
        $this->console->info('Errors: %d', $this->error_count);
        
        if ($this->dry_run) {
            $this->console->info('');
            $this->console->info('DRY RUN - No changes were made to the database');
        }
        
        // Log summary for monitoring
        PaymentLogger::getInstance()->info('RECONCILE:SETTLEMENT', 'Settlement reconciliation completed', [
            'settlement_date' => $this->settlement_date_et,
            'processed_count' => $this->processed_count,
            'settled_count' => $this->settled_count,
            'under_review_count' => $this->under_review_count,
            'pending_count' => $this->pending_count,
            'rejected_count' => $this->rejected_count,
            'skipped_count' => $this->skipped_count,
            'error_count' => $this->error_count,
            'dry_run' => $this->dry_run
        ]);
    }
}
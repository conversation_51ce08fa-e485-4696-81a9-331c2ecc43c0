<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Acl;
use App\Resources\System\Form\ItemResource;
use App\Services\Form\Types\SystemType;
use App\Traits\Console\{DataImportExportTrait, OutputExceptionTrait};
use Common\DataSources\{SystemFormCategorySource, SystemFormItemSource};
use Core\Components\Console\Commands\BaseCommand;
use Core\Components\Resource\Classes\Scope;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Throwable;

/**
 * Class SystemFormCommand
 *
 * @package App\Console\Commands
 */
class SystemFormCommand extends BaseCommand
{
    use DataImportExportTrait;
    use OutputExceptionTrait;

    /**
     * Setup system forms
     *
     * @throws AppException
     */
    public function setup(): void
    {
        try {
            $categories = new SystemFormCategorySource();
            $categories->setup();

            $forms = new SystemFormItemSource();
            $forms->setup($categories);
        } catch (Throwable $e) {
            $this->outputException($e);
        }
    }

    /**
     * Import system form from PHP file include, JSON file, or external URL
     */
    public function import(): void
    {
        try {
            $form = $this->fetchData([
                getcwd() . DIRECTORY_SEPARATOR,
                Path::resource('forms/')
            ]);
            if (is_array($form)) {
                $form = SystemType::import($form);
            } elseif (!($form instanceof SystemType)) {
                $this->console->error('Form must be an instance of a system type');
                return;
            }
            $acl = Acl::make();

            if ($this->console->confirm('Do you want to replace an existing form?')) {
                $existing_forms = [];
                $form_scope = Scope::make()
                    ->filter('status', 'eq', ItemResource::STATUS_ACTIVE)
                    ->sort('name', 'asc');
                ItemResource::make($acl)->collection()
                    ->scope($form_scope)
                    ->run()
                    ->each(function ($form) use (&$existing_forms) {
                        $existing_forms[$form['id']] = $form['name'];
                    });

                if (count($existing_forms) === 0) {
                    $this->console->info('No existing forms available');
                    if (!$this->console->confirm('Do you want to continue?')) {
                        $this->console->error('Form import aborted');
                        return;
                    }
                } else {
                    $this->console->line('Choose a form to replace:');
                    $this->console->line();
                    $replace_form_id = $this->console->menu($existing_forms, [
                        'cancel' => true,
                        'cancel_title' => '[cancel]'
                    ]);
                    if ($replace_form_id === false) {
                        $this->console->error('Form import aborted');
                        return;
                    }
                    if (!$this->console->confirm("You chose to replace the '{$existing_forms[$replace_form_id]}' form, is this correct?")) {
                        $this->console->error('Form import aborted');
                        return;
                    }
                    $copy_categories = $this->console->confirm('Do you want to copy all current categories to the new form?');
                    $form->setReplaceForm($replace_form_id, false, false, $copy_categories);
                }
            }
            $form->save($acl);
            $this->console->info('Form successfully imported');
        } catch (Throwable $e) {
            $this->console->error('Unable to import form - Reason:');
            $this->outputException($e);
        }
    }

    /**
     * Export system form
     *
     * @throws AppException
     * @throws \App\Services\Form\Exceptions\FormException
     * @throws \App\Services\Form\Exceptions\ImportException
     */
    public function export(): void
    {
        $id = $this->console->get('id', 'System Form ID');
        $type = SystemType::getByID(Acl::make(), $id);
        if ($this->args->has('to-array')) {
            $data = $type->toArray();
        } elseif ($this->args->has('to-client-format')) {
            $data = $type->toClientFormat();
        } else {
            $data = $type->export($this->args->has('with-structure'));
        }
        $this->outputArray($data);
    }
}

<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Acl;
use App\ResourceDelegates\Payment\PropelrMerchantDelegate;
use App\Resources\Payment\PropelrMerchantResource;
use App\Services\Payment\CoPilot\Api\CoPilotApiService;
use App\Services\Payment\CoPilot\CoPilotCredentials;
use App\Services\Payment\PaymentLogger;
use Carbon\Carbon;
use Common\Models\PropelrMerchant;
use Core\Components\Console\Commands\BaseCommand;
use Exception;

/**
 * Class CoPilotMerchantStatusCommand
 *
 * Fetches merchant status from CoPilot API and updates local records
 * with signature status and merchant application status.
 *
 * Command: payments:merchant:onboard:fetch
 *
 * @package App\Console\Commands
 */
class CoPilotMerchantStatusCommand extends BaseCommand
{
    private CoPilotApiService $copilot_api_service;
    private PropelrMerchantDelegate $merchant_delegate;
    private PropelrMerchantResource $merchant_resource;
    private int $processed_count = 0;
    private int $updated_count = 0;
    private int $error_count = 0;
    private int $skipped_count = 0;
    private bool $dry_run = false;
    
    private int $skipped_signature_calls_count = 0;
    private int $skipped_merchant_calls_count = 0;
    private int $skipped_merchants_count = 0;
    private bool $force_refresh = false;

    public function __construct()
    {
        $this->copilot_api_service = new CoPilotApiService();
        $this->merchant_delegate = new PropelrMerchantDelegate();
        $this->merchant_resource = PropelrMerchantResource::make(Acl::make());
    }

    /**
     * Fetch CoPilot merchant status and update local records
     * 
     * @throws Exception
     */
    public function fetchStatus(): void
    {
        $this->skipped_signature_calls_count = 0;
        $this->skipped_merchant_calls_count = 0;
        $this->skipped_merchants_count = 0;
        
        $this->dry_run = $this->args->has('dry-run');
        $this->force_refresh = $this->args->has('force-refresh');
        $company_uuid = $this->args->get('company-uuid');
        $copilot_merchant_id = $this->args->get('copilot-merchant-id');

        if ($this->dry_run) {
            $this->console->info('DRY RUN MODE - No changes will be made to the database');
        }

        $this->console->info('Starting CoPilot merchant status fetch...');
        $merchants = $this->getMerchantsToProcess($company_uuid, $copilot_merchant_id);
        
        if ($merchants->isEmpty()) {
            $this->console->error('No CoPilot merchants found to process');
            return;
        }

        $this->console->info('Processing %d CoPilot merchant(s)', $merchants->count());
        
        foreach ($merchants as $merchant) {
            $this->processMerchantStatus($merchant);
        }

        $this->printSummary();
    }

    /**
     * Get merchants to process based on command options
     */
    private function getMerchantsToProcess(?string $company_uuid, ?string $copilot_merchant_id)
    {
        $query = PropelrMerchant::query()
            ->whereNotNull('copilotMerchantID')
            ->whereNull('deletedAt');

        if ($company_uuid) {
            $query->where('companyUUID', $company_uuid);
        }

        if ($copilot_merchant_id) {
            $query->where('copilotMerchantID', $copilot_merchant_id);
        }

        return $query->get();
    }

    /**
     * Process status fetch for a single merchant
     */
    private function processMerchantStatus(PropelrMerchant $merchant): void
    {
        $this->processed_count++;
        $this->console->info('================================');
        $this->console->info('Processing merchant: CoPilot ID %s', $merchant->copilotMerchantID);

        try {
            if (!$this->force_refresh && !$merchant->needsStatusUpdate()) {
                $this->skipped_merchants_count++;
                return;
            }

            $signature_result = null;
            if ($this->force_refresh || !$merchant->isSignatureStatusFinal()) {
                $signature_result = $this->copilot_api_service->getSignatureStatus($merchant->copilotMerchantID);
            } else {
                $this->skipped_signature_calls_count++;
            }
            
            $status_result = null;
            if ($this->force_refresh || !$merchant->isApplicationStatusFinal()) {
                $status_result = $this->copilot_api_service->getMerchantStatus($merchant->copilotMerchantID);
            } else {
                $this->skipped_merchant_calls_count++;
            }

            $updates = [];
            $has_updates = false;

            // Process signature status results
            if ($signature_result !== null) {
                if ($signature_result['success']) {
                    $this->console->line('  Signature status fetch: SUCCESS');
                    
                    if (isset($signature_result['signatureStatusCd'])) {
                        $new_status = $signature_result['signatureStatusCd'] === 'SIGNED' 
                            ? PropelrMerchant::SIGNATURE_STATUS_SIGNED 
                            : PropelrMerchant::SIGNATURE_STATUS_PENDING;
                        
                        if ($merchant->signatureStatus != $new_status) {
                            $updates['signatureStatus'] = $new_status;
                            $has_updates = true;
                            $this->console->info('  Signature status updated: %s -> %s', 
                                $merchant->getSignatureStatusName(),
                                $new_status === PropelrMerchant::SIGNATURE_STATUS_SIGNED ? 'Signed' : 'Pending'
                            );
                        }
                    }
                    
                    if (isset($signature_result['signedDatetime']) && $signature_result['signedDatetime']) {
                        try {
                            $raw_datetime = $signature_result['signedDatetime'];
                            $parsed_date = Carbon::createFromFormat('m/d/Y h:i:s A', $raw_datetime);
                            $new_signed_at = $parsed_date->format('Y-m-d H:i:s');
                            
                            if ($merchant->signedAt !== $new_signed_at) {
                                $updates['signedAt'] = $new_signed_at;
                                $has_updates = true;
                                $this->console->line('  Signed date updated: %s (parsed from: %s)', $new_signed_at, $raw_datetime);
                            }
                        } catch (Exception $e) {
                            $this->console->error('  Failed to parse signed date: %s (raw: %s)', $e->getMessage(), $signature_result['signedDatetime']);
                            PaymentLogger::getInstance()->error('COPILOT:STATUS:FETCH', 'Failed to parse signedDatetime', [
                                'company_uuid' => $merchant->companyUUID,
                                'copilot_merchant_id' => $merchant->copilotMerchantID,
                                'raw_signed_datetime' => $signature_result['signedDatetime'],
                                'error' => $e->getMessage()
                            ]);
                        }
                    }

                } else {
                    $this->console->error('  Signature status fetch: FAILED');
                    PaymentLogger::getInstance()->error('COPILOT:STATUS:FETCH', 'CoPilot signature status fetch failed', [
                        'company_uuid' => $merchant->companyUUID,
                        'copilot_merchant_id' => $merchant->copilotMerchantID,
                        'errors' => $signature_result['errors'] ?? []
                    ]);
                }
            }

            // Process merchant status results
            if ($status_result !== null) {
                if ($status_result['success']) {
                    $this->console->line('  Merchant status fetch: SUCCESS');
                    
                    // Store old status to check if it changed
                    $old_status = $merchant->status;
                    
                    // Update status using PropelrMerchantDelegate
                    $this->merchant_delegate->updateFromCoPilotStatus(
                        $merchant, 
                        $status_result['applicationBoardingStatus'],
                        $this->merchant_resource,
                        $status_result['gatewayBoardingStatus'] ?? null
                    );
                    
                    // Check if status was updated
                    if ($merchant->status !== $old_status) {
                        $has_updates = true;
                        $this->console->info('  Application status updated: %s -> %s', 
                            $old_status,
                            $merchant->status
                        );
                    }
                    
                    // Log the status information for monitoring
                    PaymentLogger::getInstance()->info('COPILOT:STATUS:FETCH', 'CoPilot merchant status fetched', [
                        'company_uuid' => $merchant->companyUUID,
                        'copilot_merchant_id' => $merchant->copilotMerchantID,
                        'application_boarding_status' => $status_result['applicationBoardingStatus'] ?? 'unknown',
                        'gateway_boarding_status' => $status_result['gatewayBoardingStatus'] ?? 'unknown'
                    ]);
                    
                    $this->console->info('  Application Status: %s', $status_result['applicationBoardingStatus'] ?? 'unknown');
                    $this->console->info('  Gateway Status: %s', $status_result['gatewayBoardingStatus'] ?? 'unknown');
                } else {
                    $this->console->error('  Merchant status fetch: FAILED');
                    PaymentLogger::getInstance()->error('COPILOT:STATUS:FETCH', 'CoPilot merchant status fetch failed', [
                        'company_uuid' => $merchant->companyUUID,
                        'copilot_merchant_id' => $merchant->copilotMerchantID,
                        'errors' => $status_result['errors'] ?? []
                    ]);
                }
            }

            // Apply updates if any
            if ($has_updates && !empty($updates)) {
                if (!$this->dry_run) {
                    $merchant->update($updates);
                    $this->console->info('  Database updated successfully');
                } else {
                    $this->console->info('  Would update database (DRY RUN)');
                }
                $this->updated_count++;
            } else {
                $this->console->line('  No updates needed');
            }

        } catch (Exception $e) {
            $this->console->error('  Error processing merchant: %s', $e->getMessage());
            $this->error_count++;

            PaymentLogger::getInstance()->error('COPILOT:STATUS:FETCH', 'CoPilot merchant status fetch error', [
                'company_uuid' => $merchant->companyUUID,
                'copilot_merchant_id' => $merchant->copilotMerchantID,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Print summary of status fetch results
     */
    private function printSummary(): void
    {
        $this->console->line('');
        $this->console->info('CoPilot Merchant Status Fetch Summary:');
        $this->console->info('Processed: %d merchants', $this->processed_count);
        $this->console->info('Updated: %d merchants', $this->updated_count);
        $this->console->info('Skipped: %d merchants', $this->skipped_count);
        $this->console->info('Errors: %d', $this->error_count);
        
        // Display optimization metrics
        $this->console->line('');
        $this->console->info('Optimization Metrics:');
        $this->console->info('Skipped signature API calls: %d', $this->skipped_signature_calls_count);
        $this->console->info('Skipped merchant API calls: %d', $this->skipped_merchant_calls_count);
        $this->console->info('Skipped merchants entirely: %d', $this->skipped_merchants_count);
        
        if ($this->force_refresh) {
            $this->console->info('');
            $this->console->info('FORCE REFRESH MODE');
        }
        
        if ($this->dry_run) {
            $this->console->info('');
            $this->console->info('DRY RUN - No changes were made to the database');
        }
        
        // Log summary for monitoring
        PaymentLogger::getInstance()->info('COPILOT:STATUS:FETCH', 'CoPilot merchant status fetch completed', [
            'processed_count' => $this->processed_count,
            'updated_count' => $this->updated_count,
            'skipped_count' => $this->skipped_count,
            'error_count' => $this->error_count,
            'skipped_signature_calls_count' => $this->skipped_signature_calls_count,
            'skipped_merchant_calls_count' => $this->skipped_merchant_calls_count,
            'skipped_merchants_count' => $this->skipped_merchants_count,
            'force_refresh' => $this->force_refresh,
            'dry_run' => $this->dry_run
        ]);
    }
}
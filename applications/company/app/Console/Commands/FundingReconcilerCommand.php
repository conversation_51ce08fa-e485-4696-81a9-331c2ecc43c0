<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Classes\Log;
use App\Services\Payment\PaymentLogger;
use App\Services\Payment\Propelr\Api\CardConnectApiService;
use Carbon\Carbon;
use Common\Models\PropelrMerchant;
use Common\Models\PropelrTransaction;
use Core\Components\Console\Commands\BaseCommand;
use Exception;

/**
 * Class FundingReconcilerCommand
 *
 * Reconciles funding status by querying CardConnect funding endpoint
 * and marking transactions as funded when deposits are confirmed.
 *
 * @package App\Console\Commands
 */
class FundingReconcilerCommand extends BaseCommand
{
    private CardConnectApiService $card_connect_api;
    private bool $dry_run = false;
    private int $processed_count = 0;
    private int $funded_count = 0;
    private int $unmatched_count = 0;
    private int $ach_reject_count = 0;
    private int $error_count = 0;
    private string $funding_date;

    public function __construct()
    {
        $this->card_connect_api = new CardConnectApiService();
    }

    /**
     * Reconcile funding statuses for CardConnect transactions
     * 
     * @throws Exception
     */
    public function reconcile(): void
    {
        $this->dry_run = $this->args->has('dry-run');
        $merchant_id = $this->args->get('merchant-id');
        $date = $this->args->get('date');

        if (!$date) {
            $this->console->error('--date parameter is required (format: YYYY-MM-DD)');
            return;
        }

        if ($this->dry_run) {
            $this->console->info('DRY RUN MODE - No changes will be made');
        }

        $this->console->info('Starting funding reconciliation...');
        $merchants = $this->getMerchantsToProcess($merchant_id);
        
        if ($merchants->isEmpty()) {
            $this->console->error('No merchants found to process');
            return;
        }

        $this->console->info('Processing %d merchant(s)', $merchants->count());
        $date = $this->getDatesToProcess($date);

        foreach ($merchants as $merchant) {
            $this->processMerchantFunding($merchant, $date);
        }

        $this->printSummary();
    }

    /**
     * Get merchants to process based on command options
     */
    private function getMerchantsToProcess(?string $merchant_id)
    {
        $query = PropelrMerchant::query()
            ->where('gatewayStatus', PropelrMerchant::GATEWAY_STATUS_BOARDED)
            ->where('status', PropelrMerchant::APPLICATION_STATUS_APPROVED)
            ->whereNull('deletedAt');

        if ($merchant_id) {
            $query->where(function($q) use ($merchant_id) {
                $q->where('ccMID', $merchant_id)
                  ->orWhere('achMID', $merchant_id);
            });
        }

        return $query->get();
    }

    /**
     * Get dates to process in MMDD format for CardConnect API
     */
    private function getDatesToProcess(?string $date): string
    {
        if ($date) {
            // Validate YYYY-MM-DD format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                throw new Exception('Date must be in YYYY-MM-DD format (e.g., 2025-08-15)');
            }
            
            // Store the original date for fundedAt field
            $this->funding_date = $date;

            $parsed_date = \DateTime::createFromFormat('Y-m-d', $date);
            return $parsed_date->format('md');  // MMDD format
        }

        // Default: yesterday only in America/New_York timezone
        $ny_timezone = new \DateTimeZone('America/New_York');
        $yesterday = Carbon::now($ny_timezone)->subDay();
        
        // Store the original date for fundedAt field
        $this->funding_date = $yesterday->format('Y-m-d');

        return $yesterday->format('md'); // MMDD format
    }

    /**
     * Process funding reconciliation for a specific merchant (both MIDs)
     */
    private function processMerchantFunding(PropelrMerchant $merchant, string $date): void
    {
        $this->console->info('Processing merchant: CC MID %s, ACH MID %s', 
            $merchant->ccMID, 
            $merchant->achMID
        );

        // Process Credit Card MID
        if ($merchant->ccMID) {
            $this->console->line('  Processing Credit Card MID: %s', $merchant->ccMID);
            $this->processMerchantMid($merchant, $merchant->ccMID, 'CC', $date);
        }

        // Process ACH MID  
        if ($merchant->achMID) {
            $this->console->line('  Processing ACH MID: %s', $merchant->achMID);
            $this->processMerchantMid($merchant, $merchant->achMID, 'ACH', $date);
        }
    }

    /**
     * Process funding reconciliation for a specific merchant and MID
     */
    private function processMerchantMid(PropelrMerchant $merchant, string $mid, string $mid_type, string $date): void
    {
        try {
            $page = null;
            $has_more = true;

            while ($has_more) {
                $response = $this->card_connect_api->funding(
                    $mid,
                    $date,
                    $page
                );

                // Validate response structure
                if (empty($response)) {
                    $this->console->line('    Empty response for %s MID %s', $mid_type, $mid);
                    break;
                }

                // Extract funding ID from first funding record (we only care about the first one)
                $funding_id = null;
                $funding_date = $this->funding_date;
                if (!empty($response['fundings']) && is_array($response['fundings'])) {
                    $first_funding = $response['fundings'][0];
                    $funding_id = isset($first_funding['fundingid']) ? (string) $first_funding['fundingid'] : null;
                    
                    // Process the funding record for logging
                    $this->processFundingRecord($merchant, $first_funding, $mid_type);
                }

                // Process transactions if they exist, passing the funding ID
                if (!empty($response['txns']) && is_array($response['txns'])) {
                    if ($funding_id) {
                        $this->processTransactions($merchant, $response['txns'], $funding_id, $funding_date);
                    } else {
                        $this->console->line('    No funding ID found for %s MID, skipping transaction processing', $mid_type);
                    }
                } else {
                    $this->console->line('    No transactions found for %s MID %s', $mid_type, $mid);
                }

                $has_more = $response['hasMore'] ?? false;
                if ($has_more) {
                    $page = ($page ?? 0) + 1;
                }
            }

        } catch (Exception $e) {
            $this->console->error('    Error processing %s MID %s: %s', 
                $mid_type, 
                $mid, 
                $e->getMessage()
            );
            $this->error_count++;

            PaymentLogger::getInstance()->error('RECONCILE:FUNDING', 'Funding API error', [
                'merchant_mid' => $mid,
                'mid_type' => $mid_type,
                'date' => $this->funding_date,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Process funding records for a merchant
     */
    private function processFundingRecords(PropelrMerchant $merchant, array $fundings): void
    {

    }

    /**
     * Process transactions from the response
     */
    private function processTransactions(PropelrMerchant $merchant, array $txns, string $funding_id, string $funding_date): void
    {
        foreach ($txns as $txn) {
            $retref = $txn['retref'] ?? null;
            if (!$retref) {
                $this->console->info('  Transaction missing retref in funding data');
                $this->unmatched_count++;
                continue;
            }

            $transaction = PropelrTransaction::query()
                ->where('retref', $retref)
                ->where('propelrMerchantID', $merchant->propelrMerchantID)
                ->first();

            if (!$transaction) {
                $this->console->info('  Transaction %s not found in database', $retref);
                $this->unmatched_count++;
                continue;
            }

            $this->processed_count++;

            // Check transaction type and status
            if ($transaction->type === PropelrTransaction::TRANSACTION_TYPE_CREDIT_CARD) {
                $this->processCreditCardFunding($transaction, $txn, $funding_id, $funding_date);
            } else if ($transaction->type === PropelrTransaction::TRANSACTION_TYPE_ACH) {
                $this->processACHFunding($transaction, $txn, $funding_id, $funding_date);
            }
        }
    }

    /**
     * Process credit card funding
     */
    private function processCreditCardFunding(
        PropelrTransaction $transaction,
        array $txn,
        ?string $funding_id,
        string $funding_date
    ): void {
        $status = $txn['status'] ?? null;
        
        if ($status === 'Processed' || $status === 'Settled') {
            // Validate funding data before marking as funded
            if (!$this->validateFundingData($transaction, $funding_id, $funding_date, $txn)) {
                return; // Skip marking as FUNDED if validation fails
            }
            
            $this->console->info('  Transaction %s: Marking as FUNDED', $transaction->retref);
            
            if (!$this->dry_run) {
                $transaction->status = PropelrTransaction::STATUS_FUNDED;
                $transaction->fundingID = $funding_id;
                $transaction->fundedAt = $funding_date;
                $transaction->settledAmount = $txn['amount'] ?? null;
                $transaction->cardBrand = $txn['cardbrand'] ?? null;
                $transaction->save();
            }
            
            $this->funded_count++;
        } else {
            $this->console->line('  Transaction %s: Status %s (not funded)', 
                $transaction->retref, 
                $status
            );
        }
    }

    /**
     * Process ACH funding
     */
    private function processACHFunding(
        PropelrTransaction $transaction,
        array $txn,
        ?string $funding_id,
        string $funding_date
    ): void {
        $status = $txn['status'] ?? null;
        $ach_return_code = $txn['achreturncode'] ?? null;
        
        if ($ach_return_code) {
            $this->console->info('  ACH Transaction %s: Return code %s',
                $transaction->retref,
                $ach_return_code
            );
            
            if (!$this->dry_run) {
                $transaction->achReturnCode = $ach_return_code;
                $transaction->save();
            }
            
            $this->ach_reject_count++;
            
            PaymentLogger::getInstance()->warning('RECONCILE:FUNDING', 'ACH return detected', [
                'retref' => $transaction->retref,
                'ach_return_code' => $ach_return_code,
                'merchant_mid' => $transaction->merchant->mid ?? 'unknown'
            ]);
        } else if ($status === 'Processed' || $status === 'Settled') {
            // Validate funding data before marking as funded
            if (!$this->validateFundingData($transaction, $funding_id, $funding_date, $txn)) {
                return; // Skip marking as FUNDED if validation fails
            }
            
            // ACH successfully funded
            $this->console->info('  ACH Transaction %s: Marking as FUNDED', $transaction->retref);
            
            if (!$this->dry_run) {
                $transaction->status = PropelrTransaction::STATUS_FUNDED;
                $transaction->fundingID = $funding_id;
                $transaction->fundedAt = $funding_date;
                $transaction->settledAmount = $txn['amount'] ?? null;

                if (isset($txn['cardbrand'])) {
                    $cardbrand = $txn['cardbrand'];
                    if ($cardbrand === 'ESAV') {
                        $transaction->achAccountType = PropelrTransaction::ACH_ACCOUNT_TYPE_SAVINGS;
                    } elseif ($cardbrand === 'ECHK') {
                        $transaction->achAccountType = PropelrTransaction::ACH_ACCOUNT_TYPE_CHECKING;
                    }
                }
                
                $transaction->save();
            }
            
            $this->funded_count++;
        }
    }

    /**
     * Process individual funding record
     */
    private function processFundingRecord(PropelrMerchant $merchant, array $funding, string $mid_type): void
    {
        //@todo - In the future: save this to a propelrfunding table.
        $funding_id = isset($funding['fundingid']) ? (string)$funding['fundingid'] : null;
        if (!$funding_id) {
            $this->console->info('  Funding record missing fundingid, skipping');
            return;
        }

        $net_sales = $funding['netsales'] ?? 0;
        $total_funding = $funding['totalfunding'] ?? 0;
        $funding_date = $funding['datechanged'] ?? $this->funding_date;
        
        $this->console->line(sprintf(
            '  Funding ID %s: Net sales $%.2f, Total funding $%.2f', 
            $funding_id, 
            $net_sales, 
            $total_funding
        ));
        
        PaymentLogger::getInstance()->info('RECONCILE:FUNDING', 'Funding record processed', [
            'merchant_mid' => $merchant->mid,
            'funding_id' => $funding_id,
            'net_sales' => $net_sales,
            'total_funding' => $total_funding,
            'funding_date' => $funding_date
        ]);
    }

    /**
     * Validate funding data before marking transaction as FUNDED
     *
     * @param PropelrTransaction $transaction
     * @param string|null $funding_id
     * @param string $funding_date
     * @param array $txn
     * @return bool
     */
    private function validateFundingData(PropelrTransaction $transaction, ?string $funding_id, string $funding_date, array $txn): bool
    {
        if (empty($funding_id) || trim($funding_id) === '') {
            PaymentLogger::getInstance()->error('RECONCILE:FUNDING', 'Invalid funding_id - cannot mark transaction as FUNDED', [
                'retref' => $transaction->retref,
                'funding_id' => $funding_id,
                'merchant_mid' => $transaction->merchant->mid ?? 'unknown'
            ]);
            $this->console->error('  Transaction %s: Invalid funding_id, skipping FUNDED status', $transaction->retref);
            $this->error_count++;
            return false;
        }

        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $funding_date)) {
            PaymentLogger::getInstance()->error('RECONCILE:FUNDING', 'Invalid funding_date format - cannot mark transaction as FUNDED', [
                'retref' => $transaction->retref,
                'funding_date' => $funding_date,
                'expected_format' => 'YYYY-MM-DD',
                'merchant_mid' => $transaction->merchant->mid ?? 'unknown'
            ]);
            $this->console->error('  Transaction %s: Invalid funding_date format, skipping FUNDED status', $transaction->retref);
            $this->error_count++;
            return false;
        }

        $txn_amount = $txn['amount'] ?? null;
        if ($txn_amount === null || 
            !preg_match('/^\d+(\.\d{1,2})?$/', $txn_amount) || 
            (float)$txn_amount <= 0) {
            
            PaymentLogger::getInstance()->error('RECONCILE:FUNDING', 'Invalid or missing transaction amount - cannot mark transaction as FUNDED', [
                'retref' => $transaction->retref,
                'amount' => $txn_amount,
                'funding_id' => $funding_id,
                'merchant_mid' => $transaction->merchant->mid ?? 'unknown'
            ]);
            $this->console->error('  Transaction %s: Invalid or missing amount, skipping FUNDED status', $transaction->retref);
            $this->error_count++;
            return false;
        }

        return true;
    }

    /**
     * Print summary of reconciliation results
     */
    private function printSummary(): void
    {
        $this->console->line('');
        $this->console->info('Funding Reconciliation Summary:');
        $this->console->info('Date: %s', $this->funding_date);
        $this->console->info('Processed: %d transactions', $this->processed_count);
        $this->console->info('Funded: %d transactions', $this->funded_count);
        $this->console->info('Unmatched: %d transactions', $this->unmatched_count);
        $this->console->info('ACH Returns: %d', $this->ach_reject_count);
        $this->console->info('Errors: %d', $this->error_count);
        
        if ($this->dry_run) {
            $this->console->info('');
            $this->console->info('DRY RUN - No changes were made');
        }
        
        PaymentLogger::getInstance()->info('RECONCILE:FUNDING', 'Funding reconciliation completed', [
            'date' => $this->funding_date,
            'processed_count' => $this->processed_count,
            'funded_count' => $this->funded_count,
            'unmatched_count' => $this->unmatched_count,
            'ach_reject_count' => $this->ach_reject_count,
            'error_count' => $this->error_count,
            'dry_run' => $this->dry_run
        ]);
    }
}
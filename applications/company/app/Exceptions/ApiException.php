<?php

namespace App\Exceptions;

use App\Classes\Log;
use Closure;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\StaticAccessors\Response;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Config;
use Monolog\Logger;
use Throwable;

class ApiException extends HttpResponseException
{
    const DEFAULT_EXCEPTION = '__default__';

    /**
     * @var null|Logger
     */
    protected static $logger = null;

    /**
     * Get configured logger instance
     *
     * @return Logger
     */
    public static function getLogger()
    {
        if (self::$logger === null) {
            self::$logger = Log::create('api', [
                'email' => [
                    'subject' => 'API Error'
                ],
                'slack' => [
                    'username' => 'api',
                    'include_context' => false
                ],
                'file' => 'api_v1.log'
            ]);
        }
        return self::$logger;
    }

    /**
     * Log exception
     *
     * @param Throwable $e
     */
    public static function log(Throwable $e)
    {
        self::getLogger()->error($e->getMessage(), [
            'exception' => $e
        ]);
    }

    /**
     * Get configuration data based on exception type
     *
     * @param Throwable $e
     * @return array
     */
    protected static function getExceptionConfig(Throwable $e)
    {
        $exceptions = Config::get('api.exception_map', []);
        $key = get_class($e);
        $key = isset($exceptions[$key]) ? $key : static::DEFAULT_EXCEPTION;
        $config = $exceptions[$key];
        // allow individual exceptions to override config
        if ($e instanceof AppException) {
            $config = array_merge($config, $e->storage('_config', []));
        }
        if (!isset($config['include_last'])) {
            $config['include_last'] = true;
        }
        return $config;
    }

    /**
     * Create exception instance using config
     *
     * @param Throwable $e
     * @param array $config
     * @param bool $append_prev
     * @return mixed
     */
    protected static function createExceptionFromConfig(Throwable $e, array $config, $append_prev = true)
    {
        if (is_object($config['exception']) && $config['exception'] instanceof Closure) {
            $exception = $config['exception']($e, $config['code']);
        } else {
            $message = !isset($config['use_message']) || $config['use_message'] ? $e->getMessage() : '';
            $exception = new $config['exception']($config['code'], $message);
        }
        if ($e instanceof AppException) {
            $response = $e->storage('_response');
            if (is_object($response) && $response instanceof Closure) {
                $exception->response($response);
            }
        }
        if ($config['log'] ?? false) {
            static::log($e);
        }
        if ($append_prev && $config['include_last']) {
            $prev_errors = [];
            $curr_exception = $e;
            do {
                $last = $curr_exception instanceof AppException ? $curr_exception->getLastException() : $curr_exception->getPrevious();
                if ($last !== null) {
                    $last_config = static::getExceptionConfig($last);
                    $prev_errors[] = static::createExceptionFromConfig($last, $last_config, false)
                        ->getResponse()->toArray();
                    if (!$last_config['include_last']) {
                        break;
                    }
                }
                $curr_exception = $last;
            } while ($last !== null);
            if (count($prev_errors) > 0) {
                $exception->response(function ($response) use ($prev_errors) {
                    $response->meta('prev_errors', $prev_errors);
                });
            }
        }
        return $exception;
    }

    /**
     * Create instance from existing exception
     *
     * Uses exception map to generate proper response exception with code and message
     *
     * @param Throwable $e
     * @return ApiException
     */
    public static function fromException(Throwable $e)
    {
        if ($e instanceof self) {
            return $e;
        }
        $config = static::getExceptionConfig($e);
        return static::createExceptionFromConfig($e, $config);
    }

    /**
     * Get error message based on code
     *
     * @param int|string $code
     * @return mixed
     */
    protected static function getErrorMessage($code)
    {
        return Config::get("api.error_codes.{$code}", '');
    }

    /**
     * ApiException constructor
     *
     * @param int|string $code
     * @param string $message
     * @param mixed ...$args
     */
    public function __construct($code, $message = '', ...$args)
    {
        if ($message === '') {
            $message = static::getErrorMessage($code);
        } else if (count($args) > 0) {
            $message = vsprintf($message, $args);
        }
        $this->response = Response::api()->error($code, $message);
    }
}

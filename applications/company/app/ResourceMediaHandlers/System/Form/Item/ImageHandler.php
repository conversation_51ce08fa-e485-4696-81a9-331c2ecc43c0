<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\System\Form\Item;

use Core\Components\Http\Responses\FileResponse;
use Core\Components\Resource\Exceptions\MediaNotFoundException;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\MediaHandlers\BaseHandler;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\PolyCreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Exceptions\AppException;

/**
 * Class ImageHandler
 *
 * @package App\ResourceMediaHandlers\System\Form\Item
 */
class ImageHandler extends BaseHandler
{
    /**
     * Save image using id
     *
     * @param string $source_path
     * @param string $id
     * @throws AppException
     */
    protected function saveImage(string $source_path, string $id): void
    {
        $base_path = $this->version->getPath();
        if (!is_dir($base_path)) {
            @mkdir($base_path, 0755, true);
        }
        $new_path = $base_path . $id;
        if (!rename($source_path, $new_path)) {
            throw new AppException('Unable to rename file from \'%s\' to \'%s\'', $source_path, $new_path);
        }
    }

    /**
     * Handle media field data during create request
     *
     * Create file entity by adding a request to the batch of the poly create and assign id to drawing before it's saved.
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws AppException
     */
    public function create(Entity $entity, PolyCreateRequest $request)
    {
        $request->getRequest()->attach('handle.after', function (CreateRequest $request, string $id) use ($entity) {
            $this->saveImage($entity->get('tmp_name'), $id);
        });
    }

    /**
     * Handle media field data during update request
     *
     * If no image id is available, then we create file entity and assign the id to the drawing. Otherwise, we update
     * the existing file entity and flag the repair plan as invalid to make sure it gets regenerated.
     *
     * @param Entity $entity
     * @param UpdateRequest $request
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws AppException
     */
    public function update(Entity $entity, UpdateRequest $request)
    {
        $id = $request->resource()->getPrimaryField()->outputValueFromModel($request->getModel());

        $this->saveImage($entity->get('tmp_name'), $id);
    }

    /**
     * Get response for media controller
     *
     * @param string $id
     * @param array $config
     * @return FileResponse
     * @throws MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $item = $this->resource->findOrFail($id);

        $id = $this->resource->getPrimaryField()->outputValueFromModel($item);
        $path = $this->version->getPath($id);
        if (!file_exists($path)) {
            throw new MediaNotFoundException('No image found for system form item');
        }
        return Response::file($path);
    }
}

<?php

namespace App\ResourceDelegates;

use App\Models\LeadForm;
use App\Resources\LeadForm\FieldResource;
use App\Resources\LeadFormResource;
use App\Services\CompanySettingService;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\LeadFormField;
use Common\Models\User;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\Rules;
use Core\Exceptions\AppException;
use Ramsey\Uuid\{Uuid};

class LeadFormDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    /**
     * Build relations for LeadForm.
     */
    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('fields')
            ->modelRelation('fields')
            ->resource(FieldResource::class);
        return $list;
    }

    /**
     * Build fields for LeadForm.
     */
    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->primary()
            ->typeUuid()
            ->column('leadFormID')
            ->validation('Id', 'required|uuid')
            ->onAction(LeadFormResource::ACTION_CREATE, function (Field $field) {
                return $field->validation('Lead Form ID', 'required|uuid');
            })
            ->label('Id');

        $list->field('token')
            ->column('token')
            ->disableAction([LeadFormResource::ACTION_UPDATE, LeadFormResource::ACTION_PARTIAL_UPDATE]);

        $list->field('company_id')
            ->column('companyID')
            ->internalFilter()
            ->onAction(LeadFormResource::ACTION_CREATE, function (Field $field) {
                return $field->validation('Company ID', 'required|type[int]');
            })
            ->disableAction([LeadFormResource::ACTION_UPDATE, LeadFormResource::ACTION_PARTIAL_UPDATE]);

        $list->field('title')
            ->column('title')
            ->validation('Form Title', 'required|type[string]|max_length[50]');

        $list->field('save_button_label')
            ->column('saveButtonLabel')
            ->validation('Save Button Label', 'required|type[string]|max_length[50]');

        $list->field('default_assigned_to_user_id')
            ->column('defaultAssignedToUserID')
            ->validation('Default Assigned To User ID', 'nullable|optional|type[int]')
            ->onAction([LeadFormResource::ACTION_UPDATE, LeadFormResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->validation('Default Assigned To User ID', 'nullable|optional|type[int]|check_user_id');
            })
            ->disableAction([LeadFormResource::ACTION_CREATE]);

        $list->field('is_send_notifications')
            ->column('isSendNotifications')
            ->validation('Send Notifications', 'optional|type[bool]')
            ->onAction([LeadFormResource::ACTION_UPDATE, LeadFormResource::ACTION_PARTIAL_UPDATE], function (Field $field) {
                return $field->validation('Send Notifications', 'optional|type[bool]');
            })
            ->disableAction([LeadFormResource::ACTION_CREATE]);

        $list->field('additional_email_recipients')
            ->label('Additional Email Recipients')
            ->ignoreColumn()
            ->noSave()
            ->validation('Additional Email Recipients', 'nullable|optional|type[array]max_count[5]')
            ->value(function ($model) {
                if (!$model) return [];
                $svc = new CompanySettingService($model->companyID);
                return $svc->get('leads_additional_email_recipients', []);
            });

        $list->field('api_key')
            ->column('apiKey')
            ->typeUuid()
            ->validation('API Key', 'nullable|optional|type[string]')
            ->disableAction([LeadFormResource::ACTION_CREATE]);

        $list->field('google_tag_id')
            ->column('googleTagID')
            ->validation('Google Tag ID', 'nullable|optional|type[string]|max_length[255]');

        $list->field('is_active')
            ->column('isActive')
            ->validation('Is Active', 'required|type[bool]');

        $list->field('api_key_created_at')
            ->column('apiKeyCreatedAt')
            ->disableAction([LeadFormResource::ACTION_CREATE]);

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, LeadFormResource $resource)
    {
        $rules->register('check_user_id', function ($id) use ($resource) {
            if (User::query()->whereKey($id)->count() === 1) {
                return true;
            }
            return 'check_user_id';
        }, [
            'check_user_id' => 'Unable to find user'
        ]);

        return $rules;
    }


    /**
     * Handle actions before LeadForm is created.
     *
     * @param $model_data
     * @param CreateRequest $request
     * @return mixed
     */
    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['companyID'] = $user->companyID;
        }

        $model_data['token'] = Uuid::uuid4()->toString();
        $model_data['title'] = LeadFormResource::LABEL_DEFAULT_FORM_TITLE;
        $model_data['saveButtonLabel'] = LeadFormResource::LABEL_DEFAULT_SAVE_BUTTON;
        $model_data['isActive'] = true;

        return $model_data;
    }


    /**
     * Handle update logic for LeadForm.
     *
     * @param $model_data
     * @param UpdateRequest $request
     * @return mixed
     * @throws AppException
     */
    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $payload = $request->getEntity();
        $user = $request->resource()->acl()->user();

        // if attempt is to deactivate the form, other fields don't matter.
        if (!$payload->get('is_active')) {
            $model_data['isActive'] = 0;
            return $model_data;
        }

        $model_data['isActive'] = 1;
        $model_data['title'] = $payload->get('title');
        $model_data['saveButtonLabel'] = $payload->get('save_button_label');
        $model_data['defaultAssignedToUserID'] = $payload->get('default_assigned_to_user_id');
        $model_data['isSendNotifications'] = $payload->get('is_send_notifications');
        $model_data['googleTagID'] = $payload->get('google_tag_id');

        $settings_service = new CompanySettingService($user->companyID);
        $settings_service->set('leads_additional_email_recipients', $payload->get('additional_email_recipients'));
        $settings_service->save();

        $lead_form_id     = $request->getModel()->leadFormID;
        $fields_from_req  = $payload->get('fields', []);
        $allowed_refs    = FieldResource::getAllReferences();

        foreach ($fields_from_req as $reference => $data) {
            if (!in_array($reference, $allowed_refs, true)) {
                continue;
            }

            $field = LeadFormField::where('leadFormID', $lead_form_id)
                ->where('reference', $reference)
                ->first();

            if (!$field) {
                $field = new LeadFormField();
                $field->leadFormID = $lead_form_id;
                $field->reference  = $reference;
                $field->isEnabled   = 1;
                $field->isRequired  = 1;

                if ($reference === FieldResource::REFERENCE_APPOINTMENT_REQUEST) {
                    $field->fieldType = FieldResource::TYPE_FREEFORM;
                }
                else if ($reference === FieldResource::REFERENCE_EMAIL_CHECKBOX) {
                    $field->fieldType = FieldResource::TYPE_BOOLEAN;
                }
                else if ($reference === FieldResource::REFERENCE_UPLOAD_FILE) {
                    $field->fieldType = FieldResource::TYPE_FILE;
                }
                $field->label       = $data['label'];
                $field->instruction = $data['instruction']  ?? null;
            }

            $field->isEnabled   = array_key_exists('is_enabled',  $data) ? ($data['is_enabled'] ? 1 : 0) : $field->isEnabled;
            $field->isRequired  = array_key_exists('is_required', $data) ? ($data['is_required'] ? 1 : 0) : $field->isRequired;
            $field->fieldType   = $data['field_type']   ?? $field->fieldType;
            $field->label       = $data['label']        ?? $field->label;
            $field->instruction = $data['instruction']  ?? $field->instruction;

            $field->save();
        }

        return $model_data;
    }

    public function queryScopeGlobal($query, LeadFormResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    /**
     * Determine if a specific action is allowed for the LeadForm resource.
     */
    public function actionAllowed($action, LeadFormResource $resource)
    {
        if (in_array($action, [LeadFormResource::ACTION_GET_COLLECTION, LeadFormResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    /**
     * Configure the scope for LeadForm queries.
     */
    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'title', 'save_button_label', 'created_at', 'updated_at', 'default_assigned_to_user_id',
                    'is_send_notifications'
                ]);
                break;
            case 'form-v1':
                $scope->fields([
                    'id', 'token', 'title', 'company_id', 'save_button_label', 'default_assigned_to_user_id',
                    'google_tag_id', 'is_send_notifications', 'additional_email_recipients', 'is_active', 'api_key',
                    'api_key_created_at'
                ]);
                $scope->with([
                    'fields' => [
                        'fields' => [
                            'field_type', 'label', 'is_enabled', 'is_required', 'reference', 'instruction'
                        ]
                    ]
                ]);
                break;
        }
    }
}

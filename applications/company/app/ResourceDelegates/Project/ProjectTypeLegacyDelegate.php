<?php

namespace app\ResourceDelegates\Project;

use app\Resources\Project\ProjectTypeLegacyResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\Uuid;

class ProjectTypeLegacyDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;


    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('projectTypeID')
            ->noSave();

        $list->field('company_id')
            ->column('companyID', true)
            ->validation('Company Id', 'required|type[int]|check_company_id');

        $list->field('type')
            ->label('Type');

        $list->field('order')
            ->validation('Order', 'required|type[int]');

        $this->timestampFields($list, true, true, true);

        return $list;
    }

    public function validationRules(Rules $rules, ProjectTypeLegacyResource $resource)
    {
        $rules->register('check_company_id', function ($id) use ($resource) {
            if ($resource->relationResource('company')->entityExists($id)) {
                return true;
            }
            return 'check_company_id';
        }, [
            'check_company_id' => 'Unable to find company'
        ]);

        return $rules;
    }

    public function queryScopeGlobal($query, ProjectTypeLegacyResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }

        if (!$user->primary && !$user->projectManagement && $user->sales) {
            return $query->ofUser($user);
        }

        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
            case 'list-v1':
            case 'info-v1':
            case 'export-v1':
                $scope->fields([
                    'id', 'type', 'order', 'created_at'
                ], true);
                break;
        }
    }
}

<?php

namespace App\ResourceDelegates\Bid\Item;

use App\Resources\Bid\Item\Product\AdditionalCostLineItemResource;
use App\Resources\Bid\Item\Product\MaterialLineItemResource;
use App\Resources\Bid\Item\ProductLineItemResource;
use App\Resources\Product\Item\PriceResource;
use App\Resources\Product\ItemResource;
use App\Resources\UnitResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Ramsey\Uuid\UuidInterface;

class ProductLineItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('product_item')->resource(ItemResource::class);
        $list->oneOrMany('product_item_price')->resource(PriceResource::class);
        $list->oneOrMany('unit')->resource(UnitResource::class);
        $list->oneOrMany('materials')->resource(MaterialLineItemResource::class);
        $list->oneOrMany('additional_costs')->modelRelation('additionalCosts')->resource(AdditionalCostLineItemResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('bidItemProductLineItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([ProductLineItemResource::ACTION_CREATE, ProductLineItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('product_item_id')
            ->typeUuid()
            ->column('productItemID', true)
            ->validation('Product Item Id', 'required|uuid|check_product_item_id')
            ->onAction(ProductLineItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('product_item_price_id')
            ->typeUuid()
            ->column('productItemPriceID', true)
            ->validation('Product Item Price Id', 'required|uuid|check_product_item_price_id')
            ->onAction(ProductLineItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('name')
            ->immutable();

        $list->field('description')
            ->immutable();

        $list->field('pricing_disclaimer')
            ->column('pricingDisclaimer')
            ->immutable();

        $list->field('is_intangible')
            ->column('isIntangible')
            ->immutable();

        $list->field('unit_id')
            ->typeUuid()
            ->column('unitID')
            ->immutable();

        $list->field('unit_name')
            ->column('unitName')
            ->immutable();

        $list->field('unit_abbreviation')
            ->column('unitAbbreviation')
            ->immutable();

        $list->field('adjustment_mode')
            ->column('adjustmentMode')
            ->validation('Adjustment Mode', 'nullable|optional|type[int]|in_array[adjustment_modes]');

        $list->field('adjustment_type')
            ->column('adjustmentType')
            ->validation('Adjustment Type', 'nullable|optional|type[int]|in_array[adjustment_types]');

        $list->field('adjustment')
            ->validation('Adjustment', 'nullable|required_if[is_adjustment_type]|type[string]|numeric');

        $list->field('is_component_adjustment')
            ->column('isComponentAdjustment')
            ->validation('Is Component Adjustment', 'nullable|optional|type[bool]');

        $list->field('component_amount')
            ->column('componentAmount')
            ->validation('Component Amount', 'nullable|optional|type[string]|numeric');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, ProductLineItemResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_product_item_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('product_item')->entityExists($id->toString())) {
                return true;
            }
            return 'check_product_item_id';
        }, [
            'check_product_item_id' => 'Unable to find product item'
        ]);

        $rules->register('check_product_item_price_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('product_item_price')->entityExists($id->toString())) {
                return true;
            }
            return 'check_product_item_price_id';
        }, [
            'check_product_item_price_id' => 'Unable to find product item price'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('adjustment_modes', ProductLineItemResource::getAdjustmentModes());
        $config->store('adjustment_types', ProductLineItemResource::getAdjustmentTypes());
        $config->store('is_adjustment_type', function ($item_id, Validator $validator) {
            return $validator->data('adjustment_type') !== null;
        });
        return $config;
    }

    protected function addProductData($model_data, $request)
    {
        /** @var CreateRequest|UpdateRequest $request */
        /** @var UuidInterface $product_item_id */
        $product_item_id = $request->getValidatedEntity()->get('product_item_id');

        /** @var ItemResource $product_item_resource */
        $product_item_resource = $request->resource()->relationResource('product_item');

        $product_item_scope = Scope::make()
            ->fields(['name', 'description', 'pricing_disclaimer', 'is_intangible'])
            ->with([
                'unit' => [
                    'fields' => ['id', 'name', 'abbreviation']
                ]
            ]);
        $product_item = $product_item_resource->entity($product_item_id->toString())->scope($product_item_scope)->run();

        $fields = $request->resource()->getfields();

        $names = [
            'name' => 'name',
            'description' => 'description',
            'pricing_disclaimer' => 'pricing_disclaimer',
            'is_intangible' => 'is_intangible',
            'unit_id' => 'unit.id',
            'unit_name' => 'unit.name',
            'unit_abbreviation' => 'unit.abbreviation'
        ];
        foreach ($names as $name => $key) {
            $field = $fields->get($name);
            $column = $field->getcolumn(null, false, false);
            $model_data[$column] = $field->saveValue($product_item->get($key));
        }

        return $model_data;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        return $this->addProductData($model_data, $request);
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        return $this->addProductData($model_data, $request);
    }

    protected function createNestedAdditionalCosts(CreateRequest $request)
    {
        $additional_costs = $request->getValidatedEntity()->get('additional_costs', []);
        if ($additional_costs === null || count($additional_costs) === 0) {
            return;
        }
        try {
            $item_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
            $request->resource()->relationResource('additional_costs')
                ->batchCreate(Collection::fromArray($additional_costs))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($item_id) {
                    $entity->set('bid_item_product_line_item_id', $item_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create additional costs - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'additional_costs' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function createNestedMaterials(CreateRequest $request)
    {
        $materials = $request->getValidatedEntity()->get('materials', []);
        if ($materials === null || count($materials) === 0) {
            return;
        }
        try {
            $item_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
            $request->resource()->relationResource('materials')
                ->batchCreate(Collection::fromArray($materials))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($item_id) {
                    $entity->set('bid_item_product_line_item_id', $item_id);
                    return $entity;
                })
                ->run();
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to create materials - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'materials' => $errors
                ]);
            }
            throw $exception;
        }
    }

    public function anyCreateSaveAfter(CreateRequest $request)
    {
        $this->createNestedMaterials($request);
        $this->createNestedAdditionalCosts($request);
    }

    protected function updateNestedMaterials(UpdateRequest $request)
    {
        /** @var MaterialLineItemResource $materials_resource */
        $materials_resource = $request->resource()->relationResource('materials');
        $item_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());

        $materials = $request->getValidatedEntity()->get('materials', []);
        if ($materials === null || count($materials) === 0) {
            $materials_resource->deleteByProductLineItemID($item_id);
            return;
        }
        try {
            $ids = $materials_resource->batchUpdateOrCreate(Collection::fromArray($materials))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($item_id) {
                    $entity->set('bid_item_product_line_item_id', $item_id);
                    return $entity;
                })
                ->run();
            $materials_resource->deleteMissingMaterialsByProductLineItemID($item_id, $ids);
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to update materials - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'materials' => $errors
                ]);
            }
            throw $exception;
        }
    }

    protected function updateNestedAdditionalCosts(UpdateRequest $request)
    {
        $item_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());
        /** @var AdditionalCostLineItemResource $materials_resource */
        $additional_cost_resource = $request->resource()->relationResource('additional_costs');

        $additional_costs = $request->getValidatedEntity()->get('additional_costs', []);
        if ($additional_costs === null || count($additional_costs) === 0) {
            $additional_cost_resource->deleteByProductLineItemID($item_id);
            return;
        }
        try {

            $ids = $additional_cost_resource->batchUpdateOrCreate(Collection::fromArray($additional_costs))
                ->nested()
                ->attach('entity', function (Entity $entity) use ($item_id) {
                    $entity->set('bid_item_product_line_item_id', $item_id);
                    return $entity;
                })
                ->run();
            $additional_cost_resource->deleteMissingAdditionalCostsByItemID($item_id, $ids);
        } catch (ValidationException $e) {
            $errors = $e->getErrors();
            $exception = new ValidationException('Unable to update additional costs - Reason: %s', $e->getMessage());
            if (count($errors) > 0) {
                $exception->setErrors([
                    'additional_costs' => $errors
                ]);
            }
            throw $exception;
        }
    }

    public function anyUpdateSaveAfter(UpdateRequest $request)
    {
        $this->updateNestedMaterials($request);
        $this->updateNestedAdditionalCosts($request);
    }

    public function deleteSaveBefore(DeleteRequest $request)
    {
        $resource = $request->resource();
        $item_id = $request->getFields()->primaryField()->outputValueFromModel($request->getModel());

        /** @var MaterialLineItemResource $material_resource */
        $material_resource = $resource->relationResource('materials');
        $material_resource->deleteByProductLineItemID($item_id);

        /** @var AdditionalCostLineItemResource $additional_cost_resource */
        $additional_cost_resource = $resource->relationResource('additional_costs');
        $additional_cost_resource->deleteByProductLineItemID($item_id);
    }

    public function queryScopeGlobal($query, ProductLineItemResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function scopeBuildBefore(Scope $scope)
    {
        if ($scope->getFormat() === 'bid-v1' || $scope->getFormat() === 'detail-v1') {
            $scope->fields([
                'id', 'product_item_id', 'product_item_price_id', 'name', 'description', 'pricing_disclaimer',
                'is_intangible', 'unit_id', 'unit_name', 'unit_abbreviation', 'adjustment_mode', 'adjustment_type',
                'adjustment', 'is_component_adjustment', 'component_amount'
            ], true);
            $scope->with(['additional_costs', 'materials']);
        }
    }
}

<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Payment;

use App\Resources\AdditionalCostResource;
use App\Resources\Payment\PropelrMerchantResource;
use App\Resources\Payment\PropelrTransactionResource;
use App\Resources\Payment\PaymentLinkResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\PropelrTransaction;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Classes\Scope;

/**
 * Payment Transaction Resource Delegate
 * 
 *
 * @package App\ResourceDelegates\Payment
 */
class PropelrTransactionDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('merchant')->resource(PropelrMerchantResource::class);
        return $list;
    }


    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id', true)
            ->primary()
            ->column('propelrTransactionID')
            ->typeUuid()
            ->label('Transaction ID');

        $list->field('merchant_id')
            ->typeUuid()
            ->column('propelrMerchantID', true)
            ->label('Merchant ID');


        // === Transaction Identity ===
        $list->field('retref')
            ->column('retref', true)
            ->validation('Gateway Reference', 'required|type[string]|max_length[16]')
            ->label('Gateway Reference');

        $list->field('order_id')
            ->column('orderID')
            ->validation('Order ID', 'nullable|optional|type[string]|max_length[50]')
            ->label('Order ID');

        $list->field('invoice_number')
            ->column('invoiceNumber')
            ->validation('Invoice Number', 'nullable|optional|type[string]|max_length[50]')
            ->label('Invoice Number');


        // === Transaction Classification ===
        $list->field('type')
            ->column('type', true)
            ->validation('Transaction Type', 'required|type[int]')
            ->onAction(AdditionalCostResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->label('Transaction Type');


        $list->field('status')
            ->column('status', true)
            ->validation('Status', 'required|type[int]')
            ->onAction(AdditionalCostResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->label('Status');

        $list->field('status_name')
            ->onDemand()
            ->label('Status Name')
            ->value(function (PropelrTransaction $transaction) {
                return PropelrTransactionResource::getFormattedStatusNames($transaction);
            });

        $list->field('ach_account_type')
            ->column('achAccountType')
            ->validation('ACH Account Type', 'nullable|optional|type[int]')
            ->label('ACH Account Type');


        // === Financial Information ===
        $list->field('base_amount')
            ->column('baseAmount')
            ->validation('Base Amount', 'required|type[string]|numeric')
            ->label('Base Amount');

        $list->field('credit_card_processing_fee')
            ->column('creditCardProcessingFee')
            ->validation('Credit Card Processing Fee', 'nullable|optional|type[string]|numeric')
            ->label('Credit Card Processing Fee');

        $list->field('total_amount')
            ->column('totalAmount', true)
            ->validation('Total Amount', 'required|type[string]|numeric')
            ->label('Total Amount');

        $list->field('currency')
            ->column('currency')
            ->validation('Currency', 'type[string]|max_length[3]')
            ->label('Currency');


        // === Customer Information ===
        $list->field('customer_first_name')
            ->column('customerFirstName')
            ->validation('Customer First Name', 'nullable|optional|type[string]|max_length[100]')
            ->label('Customer First Name');

        $list->field('customer_last_name')
            ->column('customerLastName')
            ->validation('Customer Last Name', 'nullable|optional|type[string]|max_length[100]')
            ->label('Customer Last Name');

        $list->field('customer_email')
            ->column('customerEmail')
            ->validation('Customer Email', 'nullable|optional|type[string]|max_length[255]')
            ->label('Customer Email');

        $list->field('customer_phone')
            ->column('customerPhone')
            ->validation('Customer Phone', 'nullable|optional|type[string]|max_length[20]')
            ->label('Customer Phone');


        // === Payment Method Details ===
        $list->field('payment_token')
            ->column('paymentToken')
            ->validation('Payment Token', 'nullable|optional|type[string]|max_length[36]')
            ->label('Payment Token');

        $list->field('last4')
            ->column('last4')
            ->validation('Last 4 Digits', 'nullable|optional|type[string]|max_length[4]')
            ->label('Last 4 Digits');

        $list->field('exp_month')
            ->column('expMonth')
            ->validation('Expiration Month', 'nullable|optional|type[int]|greater_than_equal[1]|less_than_equal[12]')
            ->label('Expiration Month');

        $list->field('exp_year')
            ->column('expYear')
            ->validation('Expiration Year', 'nullable|optional|type[int]|greater_than_equal[' . date('Y') . ']|less_than_equal[2050]')
            ->label('Expiration Year');

        $list->field('card_brand')
            ->column('cardBrand')
            ->validation('Card Brand', 'nullable|optional|type[string]|max_length[20]')
            ->label('Card Brand');

        $list->field('routing_number')
            ->column('routingNumber')
            ->validation('Routing Number', 'nullable|optional|type[string]|max_length[9]')
            ->label('Routing Number');


        // === Processing Details ===
        $list->field('entry_mode')
            ->column('entryMode')
            ->validation('Entry Mode', 'nullable|optional|type[string]|max_length[20]')
            ->label('Entry Mode');

        $list->field('setl_stat')
            ->column('setlStat')
            ->validation('Settlement Status', 'nullable|optional|type[string]|max_length[32]')
            ->label('Settlement Status');

        $list->field('batch_id')
            ->column('batchID')
            ->validation('Batch ID', 'nullable|optional|type[string]')
            ->label('Batch ID');

        $list->field('comm_card')
            ->column('commCard')
            ->validation('Commercial Card', 'nullable|optional|type[string]|max_length[1]')
            ->label('Commercial Card');

        $list->field('bin_type')
            ->column('binType')
            ->validation('BIN Type', 'nullable|optional|type[string]|max_length[10]')
            ->label('BIN Type');


        // === Gateway Response Data ===
        $list->field('auth_code')
            ->column('authCode')
            ->validation('Authorization Code', 'nullable|optional|type[string]|max_length[10]')
            ->label('Authorization Code');

        $list->field('gateway_response_code')
            ->column('gatewayResponseCode')
            ->validation('Gateway Response Code', 'nullable|optional|type[string]|max_length[10]')
            ->label('Gateway Response Code');

        $list->field('gateway_response_message')
            ->column('gatewayResponseMessage')
            ->validation('Gateway Response Message', 'nullable|optional|type[string]|max_length[255]')
            ->label('Gateway Response Message');

        $list->field('card_response_code')
            ->column('cardResponseCode')
            ->validation('Card Response Code', 'nullable|optional|type[string]|max_length[10]')
            ->label('Card Response Code');

        $list->field('card_response_message')
            ->column('cardResponseMessage')
            ->validation('Card Response Message', 'nullable|optional|type[string]|max_length[255]')
            ->label('Card Response Message');

        $list->field('resp_stat')
            ->column('respStat')
            ->validation('Response Status', 'nullable|optional|type[string]|max_length[1]')
            ->label('Response Status');

        $list->field('processor_code')
            ->column('processorCode')
            ->validation('Processor Code', 'nullable|optional|type[string]|max_length[4]')
            ->label('Processor Code');


        // === Verification Results ===
        $list->field('avs_resp')
            ->column('avsResp')
            ->validation('AVS Response', 'nullable|optional|type[string]|max_length[1]')
            ->label('AVS Response');

        $list->field('cvv_resp')
            ->column('cvvResp')
            ->validation('CVV Response', 'nullable|optional|type[string]|max_length[1]')
            ->label('CVV Response');


        // === Transaction State Management ===
        $list->field('void_reference')
            ->column('voidReference')
            ->validation('Void Reference', 'nullable|optional|type[string]|max_length[16]')
            ->label('Void Reference');

        $list->field('refund_reference')
            ->column('refundReference')
            ->validation('Refund Reference', 'nullable|optional|type[string]|max_length[16]')
            ->label('Refund Reference');


        // === Raw Data & Metadata ===
        $list->field('raw_gateway_json')
            ->column('rawGatewayJSON')
            ->validation('Raw Gateway JSON', 'nullable|optional|type[string]')
            ->label('Raw Gateway JSON');

        $list->field('profile_id')
            ->column('profileID')
            ->validation('Profile ID', 'nullable|optional|type[string]|max_length[36]')
            ->label('Profile ID');

        $list->field('metadata')
            ->column('metadata')
            ->validation('Metadata', 'nullable|optional|type[string]')
            ->label('Metadata');

        // === Settlement & Funding Information ===
        $list->field('settled_amount')
            ->column('settledAmount')
            ->validation('Settled Amount', 'nullable|optional|type[string]|numeric')
            ->label('Settled Amount');

        $list->field('funding_id')
            ->column('fundingID')
            ->validation('Funding ID', 'nullable|optional|type[string]|max_length[50]')
            ->label('Funding ID');

        $list->field('settlement_date')
            ->column('settlementDate')
            ->validation('Settlement Date', 'nullable|optional|type[string]')
            ->label('Settlement Date');

        $list->field('funded_at')
            ->column('fundedAt')
            ->validation('Funded At', 'nullable|optional|type[string]')
            ->label('Funded At');

        $list->field('host_batch')
            ->column('hostBatch')
            ->validation('Host Batch', 'nullable|optional|type[string]|max_length[50]')
            ->label('Host Batch');

        $list->field('ach_return_code')
            ->column('achReturnCode')
            ->validation('ACH Return Code', 'nullable|optional|type[string]|max_length[10]')
            ->label('ACH Return Code');


        // === Timestamps ===
        $list->field('authorized_at')
            ->column('authorizedAt')
            ->validation('Authorized At', 'nullable|optional|type[string]')
            ->label('Authorized At');

        $list->field('captured_at')
            ->column('capturedAt')
            ->validation('Captured At', 'nullable|optional|type[string]')
            ->label('Captured At');

        $list->field('voided_at')
            ->column('voidedAt')
            ->validation('Voided At', 'nullable|optional|type[string]')
            ->label('Voided At');

        $list->field('refunded_at')
            ->column('refundedAt')
            ->validation('Refunded At', 'nullable|optional|type[string]')
            ->label('Refunded At');

        $list->field('settled_at')
            ->column('settledAt')
            ->validation('Settled At', 'nullable|optional|type[string]')
            ->label('Settled At');

        $list->field('void_ref')
            ->column('voidReference')
            ->validation('Void Reference', 'nullable|optional|type[string]|max_length[16]')
            ->label('Void Reference');

        $list->field('refund_ref')
            ->column('refundReference')
            ->validation('Refund Reference', 'nullable|optional|type[string]|max_length[16]')
            ->label('Refund Reference');

        // === On Demand fields ===
        $list->field('merchant_mid')
            ->onDemand()
            ->label('Merchant ID')
            ->query(function ($query) {
                return $query->leftJoin('propelrMerchants as pm', 'pm.propelrMerchantID', '=', 'propelrTransactions.propelrMerchantID');
            })
            ->rawColumn('pm.mid', 'merchant_mid');

        $list->field('amount')
            ->onDemand()
            ->label('Amount')
            ->value(function (PropelrTransaction $transaction) {
                return $transaction->totalAmount;
            });

        $list->field('payment_info')
            ->onDemand()
            ->label('Payment Info')
            ->value(function (PropelrTransaction $transaction) {
                if ($transaction->type == 2) { // ACH transaction
                    $account_type = '';
                    if ($transaction->achAccountType == 1) {
                        $account_type = 'Checking';
                    } elseif ($transaction->achAccountType == 2) {
                        $account_type = 'Savings';
                    }
                    return $account_type . ($transaction->last4 ? ' ****' . $transaction->last4 : '');
                } else { // Credit card transaction
                    return ($transaction->cardBrand ? ucfirst($transaction->cardBrand) : '') . 
                           ($transaction->last4 ? ' ****' . $transaction->last4 : '');
                }
            });

        $this->timestampFields($list, false, false, true);
        $list->modify([
            'id',
            'retref',
            'total_amount',
            'type',
            'status',
            'batch_id',
            'funding_id',
            'card_brand',
            'captured_at',
            'settled_at',
            'funded_at',
            'voided_at',
            'refunded_at',
            'created_at',
            'updated_at',
        ], function (Field $field) {
            return $field->onAction(Resource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false)->mutable();
            });
        });

        $list->modify([
            'status',
            'type',
            'total_amount',
            'batch_id',
            'funding_id',
            'created_at',
            'updated_at',
            'captured_at',
            'settled_at',
            'funded_at',
            'voided_at',
            'refunded_at'
        ], function (Field $field) {
            return $field->onAction(Resource::ACTION_FILTER, function (Field $field) {
                return $field->onDemand(false)->mutable();
            });
        });

        return $list;
    }

    /**
     * Apply global query scope for transactions
     *
     * @param mixed $query
     * @param Resource $resource
     * @return mixed
     */
    public function queryScopeGlobal($query, Resource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }

        $company = $user->company()->first(['companyUUID']);
        return $query->ofCompany($company->companyUUID);
    }

    /**
     * Search scope for transactions
     *
     * @param mixed $query
     * @param string $term
     * @return mixed
     */
    public function queryScopeSearch($query, string $term)
    {
        return $query->searchWithRank($term);
    }

    /**
     * Determine allowed actions based on ACL
     *
     * @param int $action
     * @param Resource $resource
     * @return bool
     */
    public function actionAllowed(int $action, Resource $resource): bool
    {
        // Allow read-only operations
        if (in_array($action, [Resource::ACTION_GET_COLLECTION, Resource::ACTION_GET_ENTITY])) {
            return true;
        }

        $user = $resource->acl()->user();
        if ($user === null) {
            return false;
        }

        if ($user->primary || $user->projectManagement) {
            return true;
        }

        return false;
    }



    public function scopeBuildBefore(Scope $scope): void
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'retref', 'total_amount', 'currency', 'status', 'status_name',
                    'auth_code', 'card_brand', 'last4', 'captured_at', 'created_at', 'updated_at'
                ]);
                break;
            case 'payments-dashboard-transactions-v1':
                $scope->fields([
                    'id', 'retref', 'type', 'ach_account_type', 'amount', 'total_amount', 'currency', 
                    'status', 'status_name', 'card_brand', 'last4', 'batch_id', 'payment_info',
                    'customer_first_name', 'customer_last_name', 'customer_email', 'customer_phone',
                    'credit_card_processing_fee', 'settled_amount', 'funding_id', 'settlement_date',
                    'funded_at', 'host_batch', 'ach_return_code',
                    'captured_at', 'settled_at', 'voided_at', 'refunded_at', 'created_at', 'updated_at'
                ]);
                $scope->with([
                    'merchant' => [
                        'fields' => ['id', 'mid']
                    ]
                ]);
                $scope->sort('created_at', 'desc');
                break;
            // todo - Filipe - Check with @Amanda what fields do we need to export.
            case 'export-v1':
                $scope->fields([
                    'retref',
                    'order_id',
                    'invoice_number',
                    'type',
                    'status_name',
                    'ach_account_type',
                    'base_amount',
                    'credit_card_processing_fee',
                    'total_amount',
                    'settled_amount',
                    'currency',
                    'customer_first_name',
                    'customer_last_name',
                    'customer_email',
                    'customer_phone',
                    'card_brand',
                    'last4',
                    'exp_month',
                    'exp_year',
                    'routing_number',
                    'entry_mode',
                    'auth_code',
                    'gateway_response_code',
                    'gateway_response_message',
                    'card_response_code',
                    'card_response_message',
                    'resp_stat',
                    'processor_code',
                    'avs_resp',
                    'cvv_resp',
                    'setl_stat',
                    'settlement_date',
                    'batch_id',
                    'host_batch',
                    'funding_id',
                    'ach_return_code',
                    'comm_card',
                    'bin_type',
                    'authorized_at',
                    'captured_at',
                    'settled_at',
                    'funded_at',
                    'voided_at',
                    'refunded_at',
                    'created_at',
                    'updated_at'
                ], true);
                break;
        }
    }
}
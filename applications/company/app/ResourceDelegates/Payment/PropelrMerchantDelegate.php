<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Payment;

use App\Classes\Acl;
use App\Resources\Payment\PropelrMerchantResource;
use App\Services\Payment\CoPilot\Api\CoPilotApiService;
use App\Services\Payment\PaymentLogger;
use App\Services\Payment\ContextualPaymentLogger;
use App\Services\Email\Types\User\PaymentsApplicationPendingSignatureType;
use App\Services\Email\Types\User\PaymentsApplicationSubmittedType;
use App\Services\Email\Types\User\PaymentsApplicationUnderwritingType;
use App\Services\Email\Types\User\PaymentsApplicationApprovedType;
use App\Services\Email\Types\User\PaymentsApplicationDeclinedType;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\PropelrMerchant;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Carbon\Carbon;
use Monolog\Logger;
use Ramsey\Uuid\Uuid;

/**
 * PropelrMerchant Resource Delegate
 * 
 *
 * Integrates CoPilotStatusManager functionality for centralized status management.
 * 
 * @package App\ResourceDelegates\Payment
 */
class PropelrMerchantDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    private ?ContextualPaymentLogger $logger = null;

    /**
     * Get logger instance
     */
    protected function getLog(): ContextualPaymentLogger
    {
        if ($this->logger === null) {
            $this->logger = PaymentLogger::getInstance()->withContext('MERCHANT_DELEGATE');
        }
        return $this->logger;
    }

    /**
     * Build relations for PropelrMerchant
     */
    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('transactions')->resource('App\Resources\Payment\PropelrTransactionResource');
        return $list;
    }


    /**
     * Build field definitions for PropelrMerchant
     */
    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id', true)
            ->primary()
            ->column('propelrMerchantID')
            ->typeUuid()
            ->label('Merchant ID');

        $list->field('company_uuid')
            ->column('companyUUID', true)
            ->validation('Company UUID', 'required|type[string]')
            ->label('Company UUID');

        $list->field('copilot_merchant_id')
            ->column('copilotMerchantID')
            ->validation('CoPilot Merchant ID', 'optional|type[string]|max_length[50]')
            ->label('CoPilot Merchant ID');

        $list->field('template_id')
            ->column('templateID')
            ->validation('Template ID', 'optional|type[string]|max_length[50]')
            ->label('Template ID');

        $list->field('copilot_client_id')
            ->column('copilotClientID')
            ->validation('CoPilot Client ID', 'optional|type[string]|max_length[50]')
            ->label('CoPilot Client ID');

        $list->field('cc_mid')
            ->column('ccMID')
            ->validation('Credit Card MID', 'optional|type[string]|max_length[50]')
            ->label('Credit Card MID');

        $list->field('ach_mid')
            ->column('achMID')
            ->validation('ACH MID', 'optional|type[string]|max_length[50]')
            ->label('ACH MID');

        $list->field('status')
            ->column('status', true)
            ->validation('Status', 'required|type[int]')
            ->label('Application Status');

        $list->field('gateway_status')
            ->column('gatewayStatus')
            ->validation('Gateway Status', 'required|type[int]')
            ->label('Gateway Status');

        $list->field('signature_url')
            ->column('signatureUrl')
            ->validation('Signature URL', 'optional|type[string]|max_length[512]')
            ->label('Signature URL');

        $list->field('signature_status')
            ->column('signatureStatus')
            ->validation('Signature Status', 'optional|type[int]')
            ->label('Signature Status');

        $list->field('signed_at')
            ->column('signedAt')
            ->typeDateTime()
            ->label('Signed At');

        $list->field('submitted_at')
            ->column('submittedAt')
            ->typeDateTime()
            ->label('Submitted At');

        $list->field('last_signature_error_code')
            ->column('lastSignatureErrorCode')
            ->validation('Last Signature Error Code', 'optional|type[string]|max_length[50]')
            ->label('Last Signature Error Code');

        $list->field('last_signature_error_field')
            ->column('lastSignatureErrorField')
            ->validation('Last Signature Error Field', 'optional|type[string]|max_length[100]')
            ->label('Last Signature Error Field');

        $list->field('last_signature_error_message')
            ->column('lastSignatureErrorMessage')
            ->validation('Last Signature Error Message', 'optional|type[string]')
            ->label('Last Signature Error Message');

        $list->field('is_ready_to_process')
            ->column('isReadyToProcess')
            ->validation('Is Ready To Process', 'optional|type[int]')
            ->label('Is Ready To Process');

        $list->field('approved_at')
            ->column('approvedAt')
            ->typeDateTime()
            ->label('Approved At');

        $list->field('declined_at')
            ->column('declinedAt')
            ->typeDateTime()
            ->label('Declined At');

        $list->field('boarded_at')
            ->column('boardedAt')
            ->typeDateTime()
            ->label('Boarded At');

        $list->field('last_synced_at')
            ->column('lastSyncedAt')
            ->typeDateTime()
            ->label('Last Synced At');

        $list->field('gateway_username')
            ->column('gatewayUsername')
            ->validation('Gateway Username', 'optional|type[string]|max_length[100]')
            ->label('Gateway Username');

        // Add timestamp fields
        $this->timestampFields($list, true, false, true);

        return $list;
    }

    /**
     * Apply global query scope for merchants (company-based filtering)
     */
    public function queryScopeGlobal($query, Resource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }

        $company = $user->company()->first(['companyUUID']);
        return $query->where('companyUUID', $company->companyUUID);
    }

    /**
     * Determine allowed actions based on ACL
     */
    public function actionAllowed(int $action, Resource $resource): bool
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return true;
        }

        if ($user->primary || $user->projectManagement) {
            return true;
        }

        if (in_array($action, [Resource::ACTION_GET_COLLECTION, Resource::ACTION_GET_ENTITY])) {
            return true;
        }

        return false;
    }

    /**
     * Handle post-creation logic including CoPilot merchant creation and signature generation
     */
    public function anyCreateSaveAfter(CreateRequest $request): void
    {
        /** @var PropelrMerchantResource $resource */
        $resource = $request->resource();
        
        /** @var PropelrMerchant $model */
        $model = $request->getModel();
        
        $user = $resource->acl()->user();
        
        $this->getLog()->info('Merchant created successfully', [
            'merchant_id' => $model->propelrMerchantID,
            'company_uuid' => $model->companyUUID,
            'user_id' => $user?->userID
        ]);

        // If this was created with CoPilot merchant data, attempt signature generation
        if ($model->copilotMerchantID && $model->status === PropelrMerchant::APPLICATION_STATUS_CREATED) {
            $this->generateSignatureForMerchant($model, $resource);
        }
    }


    /**
     * Handle post-update logic
     */
    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        /** @var PropelrMerchantResource $resource */
        $resource = $request->resource();

        /** @var PropelrMerchant $model */
        $model = $request->getModel();

        $user = $resource->acl()->user();

        $this->getLog()->info('Merchant updated successfully', [
            'merchant_id' => UUID::fromBytes($model->propelrMerchantID)->toString(),
            'company_uuid' => $model->companyUUID,
            'user_id' => $user?->userID
        ]);
    }

    /**
     * Generate signature URL for a merchant
     */
    protected function generateSignatureForMerchant(PropelrMerchant $merchant, PropelrMerchantResource $resource): void
    {
        try {
            $copilot_service = new CoPilotApiService();
            $signature_result = $copilot_service->generateSignature($merchant->copilotMerchantID);
            $system_resource = PropelrMerchantResource::make(Acl::make());

            if ($signature_result['success']) {
                $this->transitionToPendingSignature($merchant, $signature_result['signatureUrl'], $system_resource);
            } else {
                $errors = $signature_result['errors'] ?? [];
                $error = is_array($errors) && !empty($errors) ? $errors[0] : [];

                // Log detailed error information without truncation
                $logData = [
                    'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
                    'copilot_merchant_id' => $merchant->copilotMerchantID,
                    'errors' => $errors
                ];

                // Include full error details if available
                if (isset($signature_result['full_error_data'])) {
                    $logData['status_code'] = $signature_result['full_error_data']['status_code'] ?? null;
                    $logData['response_body'] = $signature_result['full_error_data']['response_body'] ?? null;
                    if (isset($signature_result['full_error_data']['copilot_validation_errors'])) {
                        $logData['validation_errors'] = $signature_result['full_error_data']['copilot_validation_errors'];
                    }
                } elseif (isset($signature_result['response_body'])) {
                    $logData['response_body'] = $signature_result['response_body'];
                }

                $this->getLog()->error('Failed to generate signature for merchant', $logData);
                $this->recordSignatureError($merchant, $error, $system_resource);
            }

        } catch(\Exception $e) {
            $this->getLog()->error('Exception during signature generation', [
                'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
                'copilot_merchant_id' => $merchant->copilotMerchantID,
                'exception_message' => $e->getMessage()
            ]);
            throw $e;
        }
    }


    /**
     * Transition merchant to PENDING_SIGNATURE status after signature URL generation
     */
    public function transitionToPendingSignature(PropelrMerchant $merchant, string $signature_url, PropelrMerchantResource $resource): void
    {
        $resource->partialUpdate(Entity::make([
            'id' => $merchant->propelrMerchantID,
            'signature_url' => $signature_url,
            'signature_status' => PropelrMerchant::SIGNATURE_STATUS_PENDING,
            'status' => PropelrMerchant::APPLICATION_STATUS_PENDING_SIGNATURE,
            'submitted_at' => Carbon::now(),
            'last_signature_error_code' => null,
            'last_signature_error_field' => null,
            'last_signature_error_message' => null,
        ]))->run();
        
        $this->getLog()->info('Merchant transitioned to PENDING_SIGNATURE', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
            'signature_url' => $signature_url,
        ]);

        PaymentsApplicationPendingSignatureType::send(['merchant_id' => $merchant->propelrMerchantID]);
    }

    /**
     * Transition merchant to QUALIFY status after signature completion
     */
    public function transitionToQualify(PropelrMerchant $merchant, string $signed_datetime, PropelrMerchantResource $resource): void
    {
        $resource->partialUpdate(Entity::make([
            'id' => $merchant->propelrMerchantID,
            'signature_status' => PropelrMerchant::SIGNATURE_STATUS_SIGNED,
            'signed_at' => $signed_datetime,
            'status' => PropelrMerchant::APPLICATION_STATUS_QUALIFY,
        ]))->run();
        
        $this->getLog()->info('Merchant transitioned to QUALIFY', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
            'signed_at' => $signed_datetime,
        ]);

        PaymentsApplicationSubmittedType::send(['merchant_id' => $merchant->propelrMerchantID]);
    }

    /**
     * Transition merchant to UNDER status (underwriting)
     */
    public function transitionToUnder(PropelrMerchant $merchant, PropelrMerchantResource $resource): void
    {
        $resource->partialUpdate(Entity::make([
            'id' => $merchant->propelrMerchantID,
            'status' => PropelrMerchant::APPLICATION_STATUS_UNDER,
        ]))->run();
        
        $this->getLog()->info('Merchant transitioned to UNDER', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
        ]);

        PaymentsApplicationUnderwritingType::send(['merchant_id' => $merchant->propelrMerchantID]);
    }

    /**
     * Transition merchant to BOARDING status (approved, gateway provisioning)
     */
    public function transitionToBoarding(PropelrMerchant $merchant, PropelrMerchantResource $resource): void
    {
        $resource->partialUpdate(Entity::make([
            'id' => $merchant->propelrMerchantID,
            'status' => PropelrMerchant::APPLICATION_STATUS_BOARDING,
            'approved_at' => Carbon::now(),
        ]))->run();
        
        $this->getLog()->info('Merchant transitioned to BOARDING', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
            'approved_at' => Carbon::now(),
        ]);

        PaymentsApplicationApprovedType::send(['merchant_id' => $merchant->propelrMerchantID]);
    }

    /**
     * Transition merchant to BOARDED status (gateway provisioned)
     */
    public function transitionToBoarded(PropelrMerchant $merchant, PropelrMerchantResource $resource, ?string $cc_mid = null): void
    {
        $updates = [
            'id' => $merchant->propelrMerchantID,
            'status' => PropelrMerchant::APPLICATION_STATUS_BOARDED,
            'boarded_at' => Carbon::now(),
        ];
        
        if (!empty($cc_mid)) {
            $updates['cc_mid'] = $cc_mid;
        }
        
        $resource->partialUpdate(Entity::make($updates))->run();
        
        $this->getLog()->info('Merchant transitioned to BOARDED', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
            'boarded_at' => Carbon::now(),
            'cc_mid' => $cc_mid,
        ]);
    }

    /**
     * Transition merchant to LIVE status (merchant is live and processing)
     */
    public function transitionToLive(PropelrMerchant $merchant, PropelrMerchantResource $resource): void
    {
        $updates = [
            'id' => $merchant->propelrMerchantID,
            'status' => PropelrMerchant::APPLICATION_STATUS_LIVE,
            'live_at' => Carbon::now(),
        ];
        
        $resource->partialUpdate(Entity::make($updates))->run();
        
        $this->getLog()->info('Merchant transitioned to LIVE', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
            'live_at' => Carbon::now(),
        ]);
    }

    /**
     * Transition merchant to DECLINED status
     */
    public function transitionToDeclined(PropelrMerchant $merchant, PropelrMerchantResource $resource): void
    {
        $resource->partialUpdate(Entity::make([
            'id' => $merchant->propelrMerchantID,
            'status' => PropelrMerchant::APPLICATION_STATUS_DECLINED,
            'declined_at' => Carbon::now(),
        ]))->run();
        
        $this->getLog()->info('Merchant transitioned to DECLINED', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
            'declined_at' => Carbon::now(),
        ]);

        PaymentsApplicationDeclinedType::send(['merchant_id' => $merchant->propelrMerchantID]);
    }

    /**
     * Record signature or merchant operation errors
     */
    public function recordSignatureError(PropelrMerchant $merchant, array $error, PropelrMerchantResource $resource): void
    {
        $resource->partialUpdate(Entity::make([
            'id' => $merchant->propelrMerchantID,
            'last_signature_error_code' => $error['code'] ?? null,
            'last_signature_error_field' => $error['errorField'] ?? null,
            'last_signature_error_message' => $error['message'] ?? null,
        ]))->run();
        
        $this->getLog()->warning('Signature error recorded', [
            'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
            'copilot_merchant_id' => $merchant->copilotMerchantID,
            'error_code' => $error['code'] ?? null,
            'error_field' => $error['errorField'] ?? null,
            'error_message' => $error['message'] ?? null,
        ]);
    }

    /**
     * Update merchant status based on CoPilot API response
     */
    public function updateFromCoPilotStatus(PropelrMerchant $merchant, string $application_boarding_status, PropelrMerchantResource $resource, ?string $gateway_boarding_status = null): void
    {
        $status_map = [
            'PENDING' => PropelrMerchant::APPLICATION_STATUS_PENDING_SIGNATURE,
            'QUALIFY' => PropelrMerchant::APPLICATION_STATUS_QUALIFY,
            'UNDER' => PropelrMerchant::APPLICATION_STATUS_UNDER,
            'BOARDING' => PropelrMerchant::APPLICATION_STATUS_BOARDING,
            'BOARDED' => PropelrMerchant::APPLICATION_STATUS_BOARDED,
            'LIVE' => PropelrMerchant::APPLICATION_STATUS_LIVE,
            'DECLINED' => PropelrMerchant::APPLICATION_STATUS_DECLINED,
        ];
        
        $new_status = $status_map[$application_boarding_status] ?? null;
        
        if ($new_status && $merchant->status !== $new_status) {
            switch ($new_status) {
                case PropelrMerchant::APPLICATION_STATUS_QUALIFY:
                    $this->transitionToQualify($merchant, Carbon::now()->toDateTimeString(), $resource);
                    break;
                case PropelrMerchant::APPLICATION_STATUS_UNDER:
                    $this->transitionToUnder($merchant, $resource);
                    break;
                case PropelrMerchant::APPLICATION_STATUS_BOARDING:
                    $this->transitionToBoarding($merchant, $resource);
                    break;
                case PropelrMerchant::APPLICATION_STATUS_BOARDED:
                    $this->transitionToBoarded($merchant, $resource);
                    break;
                case PropelrMerchant::APPLICATION_STATUS_LIVE:
                    $this->transitionToLive($merchant, $resource);
                    break;
                case PropelrMerchant::APPLICATION_STATUS_DECLINED:
                    $this->transitionToDeclined($merchant, $resource);
                    break;
                default:
                    // For other statuses, use resource pattern
                    throw new \Exception('Unhandled status transition: ' . $new_status);
            }
        }
        
        // Map and update gateway status if provided
        if ($gateway_boarding_status !== null) {
            $gateway_status_map = [
                'BOARDED' => PropelrMerchant::GATEWAY_STATUS_BOARDED,
                // Any other status means not boarded (1)
            ];
            
            $new_gateway_status = $gateway_status_map[$gateway_boarding_status] ?? PropelrMerchant::GATEWAY_STATUS_NOT_BOARDED;
            
            if ($merchant->gatewayStatus !== $new_gateway_status) {
                $resource->partialUpdate(Entity::make([
                    'id' => $merchant->propelrMerchantID,
                    'gateway_status' => $new_gateway_status
                ]))->run();
                
                $this->getLog()->info('Gateway status updated', [
                    'merchant_id' => Uuid::fromBytes($merchant->propelrMerchantID)->toString(),
                    'copilot_merchant_id' => $merchant->copilotMerchantID,
                    'old_gateway_status' => $merchant->gatewayStatus,
                    'new_gateway_status' => $new_gateway_status,
                    'gateway_boarding_status' => $gateway_boarding_status,
                ]);
            }
        }
    }
}
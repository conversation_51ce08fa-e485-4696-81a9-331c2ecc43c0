<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Payment;

use App\Resources\BidItemResource;
use App\Resources\Payment\PaymentLinkResource;
use App\Resources\Payment\PropelrMerchantResource;
use App\Services\Payment\PaymentLinkTokenService;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;

/**
 * Payment Link Resource Delegate
 * 
 *
 * @package App\ResourceDelegates\Payment
 */
class PaymentLinkDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('merchant')->resource(PropelrMerchantResource::class);
        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id', true)
            ->primary()
            ->column('paymentLinkID')
            ->typeUuid()
            ->label('Payment Link ID');

        $list->field('merchant_id')
            ->typeUuid()
            ->column('propelrMerchantID', true)
            ->label('Merchant ID');

        $list->field('short_token')
            ->column('shortToken', true)
            ->label('Short Token')
            ->validation('Short Token', 'trim|required|max_length[48]');

        // On demand fields
        $list->field('checkout_url')
            ->onDemand()
            ->label('Checkout URL');

        $this->timestampFields($list, true, true, true);

        return $list;
    }
}
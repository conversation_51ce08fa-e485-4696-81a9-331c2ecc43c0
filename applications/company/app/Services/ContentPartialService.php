<?php

namespace App\Services;

use App\Resources\ContentPartialResource;
use Core\Classes\Directory;
use Core\Components\Resource\Classes\Scope;
use Core\Exceptions\AppException;
use Core\StaticAccessors\App;
use Core\StaticAccessors\Path;
use LightnCandy\LightnCandy;

abstract class ContentPartialService
{
    protected static $resource;

    public static function clearCache()
    {
        Directory::clear(Path::get('contentPartialCache'));
    }

    // @todo maybe centralize to content template service and add configuration to disable certain helpers
    // @todo need to make sure cyclic calls are not allowed (this is disabled by not allowing partial helper, but it could be needed in the future)
    public static function compileTemplate($data)
    {
        $helper_class = ContentTemplate\Helpers::class;
        return LightnCandy::compile($data, [
            'flags' => LightnCandy::FLAG_HANDLEBARS | LightnCandy::FLAG_RUNTIMEPARTIAL | LightnCandy::FLAG_EXTHELPER | LightnCandy::FLAG_ERROR_EXCEPTION,
            'helpers' => [
                'format-date' => "{$helper_class}::formatDate",
                'format-text' => "{$helper_class}::formatText"
            ]
        ]);
    }

    public static function getCachePath($id)
    {
        return Path::contentPartialCache($id);
    }

    public static function renderByAlias(ContentPartialResource $resource, $alias, $context = null)
    {
        static::$resource = $resource;

        // @todo eventually do lookup from memory cache to prevent duplicate db calls
        $partial = $resource->entity()->lookup(['alias' => $alias])->scope(Scope::make()->field('id'))->run();

        $cache_path = self::getCachePath($partial['id']);
        $cache_hit = !App::debugEnabled() && file_exists($cache_path);

        if (!$cache_hit) {
            // get content from database to save
            $partial = $resource->entity($partial['id'])->scope(Scope::make()->field('content'))->run();
            $php = self::compileTemplate($partial['content']);
            if (file_put_contents($cache_path, '<?php ' . $php . ' ?>') === false) {
                throw new AppException('Unable to save partial template');
            }
        }

        $renderer = include($cache_path);

        return $renderer($context === null ? [] : $context);
    }

    public static function clearCacheByID($id)
    {
        $path = self::getCachePath($id);
        if (!file_exists($path)) {
            return;
        }
        if (!unlink($path)) {
            throw new AppException('Unable to remove content partial cache file: %s', $path);
        }
    }
}

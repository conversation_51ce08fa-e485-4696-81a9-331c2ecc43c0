<?php

namespace App\Services\Email\Types\User;

use App\Classes\Template;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\UserType;
use Common\Models\Company;
use Common\Models\PropelrMerchant;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

/**
 * Class PaymentsApplicationApprovedType
 *
 * Notify all primary users of Payments Application Approval
 *
 * @package App\Services\Email\Types\User
 */
class PaymentsLiveType extends UserType
{
    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws AppException
     * @throws TypeException
     */
    public function build(array $payload)
    {
        if (!isset($payload['merchant_id'])) {
            throw new TypeException('Merchant ID not defined in payload');
        }
        if (($merchant = PropelrMerchant::find($payload['merchant_id'])) === null) {
            throw new TypeException('Unable to find merchant: %s', $payload['merchant_id']);
        }

        if (($company = Company::where('companyUUID', $merchant['companyUUID'])->first()) === null) {
            throw new TypeException('Unable to find company: %s',$merchant['companyUUID']);
        }

        $this->setup($company, $this->getPrimaryUsers($company));

        $domain = $this->getDomain();
        $brand_name = $domain['brand']['name'];

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($company->companyUUID));
        $message->subject('You Can Now Use Payments in ' . $brand_name);

        $template_vars = [
            'brand_name' => $brand_name,
            'company_profile_link' => $this->newUrlBuilder()->path('company/profile/integrations/payments/details')->build(),
            'help_center_send_invoices_collect_payments' => 'https://cxlratr.to/hc-send-invoices-collect-payments',
            'help_center_applied_merchant_now_what' => 'https://cxlratr.to/hc-applied-merchant-now-what',
        ];
        $this->template->content = Template::fetch('emails.html.user.payments-live', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.payments-live', $template_vars));

        return $message;
    }
}

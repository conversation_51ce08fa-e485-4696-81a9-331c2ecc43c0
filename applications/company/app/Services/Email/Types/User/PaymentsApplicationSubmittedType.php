<?php

namespace App\Services\Email\Types\User;

use App\Classes\Template;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Types\UserType;
use Common\Models\Company;
use Common\Models\PropelrMerchant;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

/**
 * Class PaymentsApplicationSubmittedType
 *
 * Notify all primary users of Payments Application Approval
 *
 * @package App\Services\Email\Types\User
 */
class PaymentsApplicationSubmittedType extends UserType
{
    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws AppException
     * @throws TypeException
     */
    public function build(array $payload)
    {
        if (!isset($payload['merchant_id'])) {
            throw new TypeException('Merchant ID not defined in payload');
        }
        if (($merchant = PropelrMerchant::find($payload['merchant_id'])) === null) {
            throw new TypeException('Unable to find merchant: %s', $payload['merchant_id']);
        }

        if (($company = Company::where('companyUUID', $merchant['companyUUID'])->first()) === null) {
            throw new TypeException('Unable to find company: %s',$merchant['companyUUID']);
        }

        $this->setup($company, $this->getPrimaryUsers($company));

        $domain = $this->getDomain();
        $brand_name = $domain['brand']['name'];

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($company->companyUUID));
        $message->subject($brand_name . ' Payments Application Submitted');

        $template_vars = [
            'brand_name' => $brand_name,
        ];
        $this->template->content = Template::fetch('emails.html.user.payments-application-submitted', $template_vars);

        $message->html($this->template->render(), Template::fetch('emails.text.user.payments-application-submitted', $template_vars));

        return $message;
    }
}

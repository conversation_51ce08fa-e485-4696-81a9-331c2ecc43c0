<?php

declare(strict_types=1);

namespace App\Services\Lead;

use App\Classes\Acl;
use App\ResourceMediaHandlers\Lead\FileHandler;
use App\Resources\Lead\FileResource;
use App\Resources\LeadForm\FieldResource;
use App\Resources\LeadFormResource;
use App\Resources\LeadResource;
use Common\Models\LeadForm;
use Common\Models\MarketingType;
use Common\Models\ProjectType;
use Common\Models\User;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Resource\Classes\BatchRequest;
use Core\Components\Resource\Classes\Entity;
use Exception;
use Ramsey\Uuid\Uuid;

class LeadFormService
{

    public static function make()
    {
        return new static();
    }

    public function __construct()
    {
    }

    /**
     * Get marketing and project types for the company.
     *
     * @return array|JSONResponse
     */
    public function getCompanyMarketingInfo($companyID)
    {
        $project_types = ProjectType::where('companyID', $companyID)
            ->where('status', ProjectType::STATUS_ACTIVE)
            ->orderBy('name', 'asc')
            ->get();

        $marketing_types = MarketingType::where('companyID', $companyID)
            ->whereNull('isDeleted')
            ->orderBy('parentMarketingTypeID', 'asc')
            ->get();

        $marketing = [];
        foreach ($marketing_types as $type) {
            if ($type->parentMarketingTypeID === null) {
                $marketing[$type->marketingTypeID] = [
                    'label' => $type->marketingTypeName,
                    'options' => []
                ];
            } else {
                if (isset($marketing[$type->parentMarketingTypeID])) {
                    $marketing[$type->parentMarketingTypeID]['options'][$type->marketingTypeID] = $type->marketingTypeName;
                }
            }
        }

        $project = [];
        foreach ($project_types as $type) {
            $id = strtoupper(bin2hex($type->projectTypeID));
            $project[$id] = $type->name;
        }

        $marketing = array_values($marketing);
        $project = array_values([['label' => 'Project Types', 'options' => $project]]);

        return [
            'marketing_types' => $marketing,
            'project_types' => $project
        ];
    }


    /**
     * Activate a lead form for the company.
     * If a lead form already doesn't exist, create a new one.
     * If a lead form already exists, activate it.
     *
     * @throws Exception
     */
    public function activate()
    {
        $user = Auth::user();
        $acl = Auth::acl();
        if (!$user) {
            throw new Exception('User not found.');
        }

        $existing_lead_form = LeadForm::where('companyID', $user->companyID)
            ->first();

        // Activate existing lead form if it exists
        if ($existing_lead_form && !$existing_lead_form->isActive) {
            $form = Entity::make([
                'id' => Uuid::fromBytes($existing_lead_form->leadFormID)->toString(),
                'is_active' => true,
                'title' => $existing_lead_form->title,
                'save_button_label' => $existing_lead_form->saveButtonLabel,
                'default_assigned_to_user_id' => $existing_lead_form->defaultAssignedToUserID,
                'additional_email_recipients' => $existing_lead_form->additionalEmailRecipients,
                'google_tag_id' => $existing_lead_form->googleTagID,
            ]);

            return LeadFormResource::make($acl)->partialUpdate($form)->run();
        }

        $uuid = Uuid::uuid4();
        $form = Entity::make([
            'id' => $uuid->toString(),
            'token' => Uuid::uuid4()->toString(),
            'company_id' => $user->companyID,
            'is_active' => true,
            'title' => LeadFormResource::LABEL_DEFAULT_FORM_TITLE,
            'save_button_label' => LeadFormResource::LABEL_DEFAULT_SAVE_BUTTON,
        ]);

        $batch = BatchRequest::make()->sequential();
        $lead_form = LeadFormResource::make($acl)->create($form);
        $batch->add($lead_form);

        $field_data = $this->prepareLeadFormFields($uuid->toString());

        foreach ($field_data as $data) {
            $field = Entity::make($data);
            $batch->add(FieldResource::make($acl)->create($field));
        }

        $batch->run();
        return $lead_form->response();
    }

    /**
     * Create a new lead from the input data.
     *
     * @param LeadForm $lead_form
     * @param $input
     * @return mixed
     */
    public function createLead($lead_form, $input)
    {
        $payload = [
            'company_id' => $lead_form->companyID,
            'first_name' => $input['first_name'] ?? null,
            'last_name' => $input['last_name'] ?? null,
            'email' => $input['email'] ?? null,
            'phone_number' => $input['phone'] ?? null,
            'address' => $input['address'] ?? null,
            'city' => $input['city'] ?? null,
            'state' => $input['state'] ?? null,
            'zip' => $input['postal_code'] ?? null,
            'origin' => $input['origin'] ?? LeadResource::ORIGIN_STANDARD,
            'status' => LeadResource::STATUS_NEW
        ];

        if ($input['origin'] === LeadResource::ORIGIN_API_INTEGRATION) {
            $payload['is_unsubscribed'] = isset($input['is_unsubscribed']) && $input['is_unsubscribed'];
            if (isset($input['is_unsubscribed']) && !$input['is_unsubscribed']) {
                $payload['is_sent_notifications'] = $lead_form->isSendNotifications && isset($input['email']) && $input['email'] !== '';
            } else {
                $payload['is_sent_notifications'] = false;
            }
        } else {
            $payload['is_unsubscribed'] = $input['email_checkbox'] === 'no';
            $payload['is_sent_notifications'] = ($input['email_checkbox'] === 'on' || $input['email_checkbox'] === 'null') && $lead_form->isSendNotifications && $input['email'] !== '';
        }

        $array = $this->parseMarketingFields($lead_form, $input);
        $payload = array_merge($payload, $array);

        $payload['notes'] = $this->parseCustomerNotes($lead_form, $input);
        $payload['assigned_to_user_id'] = $this->getValidDefaultAssignedUserId($lead_form);

        $lead = Entity::make($payload);
        $acl = Acl::make()->setCompanyID($lead_form->companyID);
        return LeadResource::make($acl)->create($lead)->run();
    }

    /**
     * Create a new lead file from the input data.
     *
     * @param LeadForm $lead_form
     * @param $input
     * @param RequestInterface $request
     * @return mixed
     */
    public function createLeadFile($lead_form, $input, RequestInterface $request)
    {
        $payload = [
            'lead_id' => $input['lead_id'],
            'name' => $input['name']
        ];

        $acl = Acl::make()->setCompanyID($lead_form->companyID);

        // get lead file media handler
        $resource = FileResource::make($acl);
        /** @var FileHandler $handler */
        $handler = $resource->getMediaHandler('file');

        // create lead file type with lead file media handler and add to lead file payload
        $payload['file_id'] = $handler->createFileOnly(Entity::make($input['file']));

        // save new lead file
        return FileResource::make($acl)->create(Entity::make($payload))->run();
    }

    /**
     * Handles the processing of specific fields (marketing_source and project_type).
     *
     * @param LeadForm $lead_form
     * @param array $input
     * @return array
     */
    private function parseMarketingFields(LeadForm $lead_form, array $input): array
    {
        $data = [];
        $field_keys = [
            FieldResource::REFERENCE_MARKETING_SOURCE => 'marketingTypeID',
            FieldResource::REFERENCE_PROJECT_TYPE => 'projectTypeID',
        ];

        $fields = $lead_form->fields()
            ->whereIn('reference', array_keys($field_keys))
            ->where('isEnabled', true)
            ->get();

        foreach ($fields as $field) {
            $reference = $field->reference;
            $value = trim($input[$reference] ?? '');

            if (!empty($value)) {
                if ($field->fieldType === FieldResource::TYPE_DROPDOWN) {
                    if ($reference === FieldResource::REFERENCE_MARKETING_SOURCE) {
                        $data['marketing_type_id'] = (int) $value;
                    } elseif ($reference === FieldResource::REFERENCE_PROJECT_TYPE) {
                        $data['project_type_id'] = $value;
                    }
                }
            }
        }

        return $data;
    }

    /**
     * Parses customer notes from the lead form input.
     *
     * @param LeadForm $leadForm The lead form instance.
     * @param array $input The input data from the form submission.
     * @return string|null The parsed customer notes.
     */
    private function parseCustomerNotes(LeadForm $leadForm, array $input): ?string
    {
        $notes = [];
        $enabled = $leadForm->fields()
            ->whereIn('reference', [
                FieldResource::REFERENCE_MARKETING_SOURCE,
                FieldResource::REFERENCE_PROJECT_TYPE,
                FieldResource::REFERENCE_CUSTOMER_NOTES,
                FieldResource::REFERENCE_EMAIL_CHECKBOX,
                FieldResource::REFERENCE_APPOINTMENT_REQUEST,
            ])
            ->where('isEnabled', true)
            ->get()
            ->keyBy('reference');

        foreach ([
             FieldResource::REFERENCE_MARKETING_SOURCE,
             FieldResource::REFERENCE_PROJECT_TYPE,
             FieldResource::REFERENCE_CUSTOMER_NOTES,
         ] as $ref) {
            if (!isset($enabled[$ref])) {
                continue;
            }
            if (!isset($input[$ref])) {
                continue;
            }
            $value = $input[$ref];
            if (
                ($enabled[$ref]->fieldType === FieldResource::TYPE_FREEFORM || $enabled[$ref]->fieldType === FieldResource::TYPE_TEXTAREA) && !empty($value)
            ) {
                $value = trim($input[$ref] ?? '');
                $notes[] = "<strong>{$enabled[$ref]->label}</strong>: {$value}";
            }
        }


        $email_ref = FieldResource::REFERENCE_EMAIL_CHECKBOX;
        if (isset($enabled[$email_ref])) {
            $value = $input[$email_ref] === 'no' ? 'No' : 'Yes';
            $notes[] = "<strong>{$enabled[$email_ref]->label}:</strong> {$value}";
        }

        $appt_ref = FieldResource::REFERENCE_APPOINTMENT_REQUEST;
        if (isset($enabled[$appt_ref])) {
            $options = [];
            for ($i = 1; $i <= 3; $i++) {
                $date_key = "appointment_request_date_{$i}";
                $time_key = "appointment_request_time_{$i}";
                $date_val = $input[$date_key] ?? '';
                $time_val = $input[$time_key] ?? '';

                if ($date_val !== '' && $time_val !== '') {
                    $formatted_date   = date('m/d/Y', strtotime($date_val));
                    $options[] = "Option {$i}: {$formatted_date} – {$time_val}";
                }
            }

            if ($options) {
                $body  = implode('<br>', $options);
                $label = $enabled[$appt_ref]->label;
                $notes[] = "<strong>{$label}</strong>:<br>{$body}";
            }
        }

        $notes = implode("<br>", $notes);
        return $notes === '' ? null : $notes;
    }

    /**
     * Retrieves the valid default assigned user ID for a given lead form.
     *
     * @param LeadForm $leadForm The lead form instance.
     *
     * @return int|null The user ID if a valid user is found; otherwise, null.
     */
    private function getValidDefaultAssignedUserId(LeadForm $leadForm): ?int
    {
        $default_assigned_to = $leadForm->defaultAssignedToUserID;
        $company_id = $leadForm->companyID;

        if (empty($default_assigned_to)) {
            return null;
        }

        $user = User::where('userID', $default_assigned_to)
                ->ofCompany($company_id)
                ->active()
                ->ofRole(User::ROLE_SALES)
                ->notInvited()
                ->first();

        return $user ? $user->userID : null;
    }

    /**
     * Prepare lead form fields for bulk insertion.
     *
     * @param string $lead_form_id The UUID of the lead form.
     * @return array The prepared data for insertion.
     */
    private function prepareLeadFormFields($lead_form_id): array
    {
        $fields = [
            FieldResource::REFERENCE_FIRST_NAME => FieldResource::LABEL_DEFAULT_FIRST_NAME,
            FieldResource::REFERENCE_LAST_NAME => FieldResource::LABEL_DEFAULT_LAST_NAME,
            FieldResource::REFERENCE_EMAIL => FieldResource::LABEL_DEFAULT_EMAIL,
            FieldResource::REFERENCE_PHONE => FieldResource::LABEL_DEFAULT_PHONE,
            FieldResource::REFERENCE_ADDRESS => FieldResource::LABEL_DEFAULT_ADDRESS,
            FieldResource::REFERENCE_MARKETING_SOURCE => FieldResource::LABEL_DEFAULT_MARKETING_SOURCE,
            FieldResource::REFERENCE_PROJECT_TYPE => FieldResource::LABEL_DEFAULT_PROJECT_TYPE,
            FieldResource::REFERENCE_CUSTOMER_NOTES => FieldResource::LABEL_DEFAULT_CUSTOMER_NOTES,
            FieldResource::REFERENCE_EMAIL_CHECKBOX => FieldResource::LABEL_DEFAULT_EMAIL_CHECKBOX,
            FieldResource::REFERENCE_UPLOAD_FILE => FieldResource::LABEL_DEFAULT_UPLOAD_FILE,
            FieldResource::REFERENCE_APPOINTMENT_REQUEST => FieldResource::LABEL_DEFAULT_APPOINTMENT_REQUEST,
        ];

        // reference -> explicit type (defaults to FREEFORM)
        $typeMap = [
            FieldResource::REFERENCE_CUSTOMER_NOTES      => FieldResource::TYPE_TEXTAREA,
            FieldResource::REFERENCE_APPOINTMENT_REQUEST => FieldResource::TYPE_TEXTAREA,
            FieldResource::REFERENCE_UPLOAD_FILE         => FieldResource::TYPE_FILE,
            FieldResource::REFERENCE_EMAIL_CHECKBOX      => FieldResource::TYPE_BOOLEAN,
        ];

        return array_map(function ($reference, $label) use ($lead_form_id, $typeMap) {
            $type = $typeMap[$reference] ?? FieldResource::TYPE_FREEFORM;

            return [
                'created_at' => date('Y-m-d H:i:s'),
                'lead_form_id' => $lead_form_id,
                'field_type' => $type,
                'reference' => $reference,
                'label' => $label,
                'is_enabled' => true,
                'is_required' => false,
            ];
        }, array_keys($fields), $fields);
    }

}

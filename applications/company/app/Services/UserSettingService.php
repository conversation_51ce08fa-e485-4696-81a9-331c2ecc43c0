<?php

namespace App\Services;

use App\Classes\Acl;
use App\Resources\User\SettingResource;
use Core\Classes\Directory;
use Core\Components\Resource\Classes\BatchRequest;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\BatchHandleException;
use Core\Components\Resource\Exceptions\BatchPrepareException;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;

/**
 * Class UserSettingService
 *
 * @package App\Services
 */
class UserSettingService
{
    /**
     * @var int
     */
    protected $user_id;

    /**
     * @var null|SettingResource
     */
    protected $resource = null;

    /**
     * @var null|array
     */
    protected $settings = null;

    /**
     * @var array
     */
    protected $changed = [];

    /**
     * @var array
     */
    protected $removed = [];

    /**
     * @var bool
     */
    protected $auto_save = false;

    /**
     * Clear all cache files
     *
     * @throws AppException
     */
    public static function clearEntireCache()
    {
        Directory::clear(Path::get('userSettingCache'));
    }

    /**
     * UserSettingService constructor
     *
     * @param int $user_id
     */
    public function __construct($user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * Get cache file path
     *
     * @return string
     */
    protected function getCacheFile()
    {
        return Path::userSettingCache("{$this->user_id}.php");
    }

    /**
     * Set if auto save is used
     *
     * This controls whether saves happen immediately after set() is called, or if save() needs to be called separately
     * to group them in a batch
     *
     * @param $bool
     * @return $this
     */
    public function setAutoSave($bool)
    {
        $this->auto_save = $bool;
        return $this;
    }

    /**
     * Enable auto save
     *
     * @return UserSettingService
     */
    public function enableAutoSave()
    {
        return $this->setAutoSave(true);
    }

    /**
     * Determine state of auto saving flag
     *
     * @return bool
     */
    public function isAutoSaving()
    {
        return $this->auto_save;
    }

    /**
     * Get SettingResource instance
     *
     * @return SettingResource
     */
    protected function getResource()
    {
        if ($this->resource === null) {
            $this->resource = SettingResource::make(Acl::make());
        }
        return $this->resource;
    }

    /**
     * Get all settings for user
     *
     * If cache file exists with proper setting data, we use it. Otherwise we pull all settings for the user and
     * cache them in a file for the next request.
     *
     * @return array
     * @throws AppException
     */
    public function &getSettings()
    {
        if ($this->settings === null) {
            // @todo implement proper caching layer in framework and move the data storage to a memory based cache
            $cache_file = $this->getCacheFile();
            if (file_exists($cache_file)) {
                $settings = include $cache_file;
                if (!is_array($settings)) {
                    throw new AppException('User settings cache file corrupted: %s', $cache_file);
                }
                $this->settings = $settings;
            } else {
                $this->settings = [];
                $scope = Scope::make()->fields(['id', 'name', 'value'])
                    ->filter('user_id', 'eq', $this->user_id);
                $settings = $this->getResource()
                    ->collection()
                    ->scope($scope)
                    ->run();
                foreach ($settings as $setting) {
                    $this->settings[$setting->name] = $setting->toArray();
                }
                $cache_data = '<' . '?php return ' . var_export($this->settings, true) . ';' . PHP_EOL;
                if (file_put_contents($cache_file, $cache_data) === false) {
                    throw new AppException('Unable to save cache file: %s', $cache_file);
                }
            }
        }
        return $this->settings;
    }

    /**
     * Set setting value by name
     *
     * @param string $name
     * @param mixed $value
     * @throws AppException
     */
    public function set($name, $value)
    {
        $settings =& $this->getSettings();
        // if setting doesn't exist create it
        if (!isset($settings[$name])) {
            $settings[$name] = [
                'name' => $name,
                'value' => $value
            ];
        } else {
            $settings[$name]['value'] = $value;
        }
        $this->changed[] = $name;
        if ($this->isAutoSaving()) {
            $this->save();
        }
    }

    /**
     * Determine if setting exists
     *
     * @param $name
     * @return bool
     * @throws AppException
     */
    public function has($name)
    {
        $settings = $this->getSettings();
        return isset($settings[$name]);
    }

    /**
     * Get setting data if exists, otherwise return default value
     *
     * If name is null, then the entire setting array is returned
     *
     * @param null|string $name
     * @param mixed $default
     * @return array|null
     * @throws AppException
     */
    public function get($name = null, $default = null)
    {
        $settings = $this->getSettings();
        if ($name === null) {
            return $settings;
        }
        if (!isset($settings[$name]) || in_array($name, $this->removed)) {
            return $default;
        }
        return $settings[$name]['value'];
    }

    /**
     * Get name => value array
     */
    public function getList()
    {
        $settings = $this->getSettings();
        return array_map(function ($setting) {
            return $setting['value'];
        }, $settings);
    }

    /**
     * Remove setting by name
     *
     * @param $name
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\BatchHandleException
     */
    public function remove($name)
    {
        $this->removed[] = $name;
        if ($this->isAutoSaving()) {
            $this->save();
        }
    }

    /**
     * Save any changes
     *
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\BatchHandleException
     */
    public function save()
    {
        $resource = $this->getResource();
        $settings =& $this->getSettings();
        $batch_request = BatchRequest::make()->atomic();
        if (count($this->changed) > 0) {
            foreach ($this->changed as $name) {
                $entity = Entity::make($settings[$name]);
                $entity->set('user_id', $this->user_id);
                // if no id exists then it needs to be created
                if (!isset($settings[$name]['id'])) {
                    $request = $resource->create($entity)
                        ->attach('save.after', function (CreateRequest $request) use (&$settings, $name) {
                            $settings[$name]['id'] = $request->getModel()->getKey();
                        });
                    $batch_request->add($request, $name);
                    continue;
                }
                $batch_request->add($resource->update($entity), $name);
            }
            $this->changed = [];
        }
        if (count($this->removed) > 0) {
            foreach ($this->removed as $name) {
                if (!isset($settings[$name]['id'])) {
                    continue;
                }
                $request = $resource->delete(Entity::make(['id' => $settings[$name]['id']]))
                    ->attach('save.after', function () use (&$settings, $name) {
                        unset($settings[$name]);
                    });
                $batch_request->add($request, $name);
            }
            $this->removed = [];
        }
        try {
            $batch_request->run();
        } catch (BatchPrepareException $e) {
            throw (new ValidationException('Unable to validate settings'))->setErrors($e->getErrors());
        }
        $this->clearCache();
    }

    /**
     * Clear cache
     *
     * Deletes cache file from system, so it can be refreshed on next request
     *
     * @throws AppException
     */
    public function clearCache()
    {
        $cache_file = $this->getCacheFile();
        if (!file_exists($cache_file)) {
            return;
        }
        if (!unlink($cache_file)) {
            throw new AppException('Unable to delete cache file: %s', $cache_file);
        }
    }
}

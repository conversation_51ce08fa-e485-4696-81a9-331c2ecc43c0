<?php

declare(strict_types=1);

namespace App\Services\Payment;

use Common\Models\PaymentLink;
use Exception;

class PaymentLinkTokenService
{
    /**
     * Base62 character set for token encoding
     */
    private const BASE62_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    
    /**
     * Minimum token length to ensure security
     */
    private const MIN_TOKEN_LENGTH = 22;
    
    /**
     * Number of random bytes to generate (128-160 bits as per spec)
     */
    private const RANDOM_BYTES = 20; // 160 bits

    /**
     * Generate a cryptographically secure payment link token
     * 
     * Uses CSPRNG to generate 160 bits of randomness, then encodes with Base62
     * to produce a URL-safe token of minimum 22 characters
     *
     * @return string The generated token
     * @throws Exception If random_bytes fails
     */
    public function generateToken(): string
    {
        // Generate cryptographically secure random bytes
        $randomBytes = random_bytes(self::RANDOM_BYTES);
        
        // Convert to Base62 for URL-safe encoding
        $token = $this->encodeBase62($randomBytes);
        
        // Ensure minimum length requirement
        if (strlen($token) < self::MIN_TOKEN_LENGTH) {
            // Generate additional bytes if needed (edge case)
            $additionalBytes = random_bytes(4);
            $token .= $this->encodeBase62($additionalBytes);
        }
        
        return $token;
    }

    /**
     * Encode binary data to Base62 (simplified version without GMP)
     * 
     * @param string $data Binary data to encode
     * @return string Base62 encoded string
     */
    private function encodeBase62(string $data): string
    {
        // Simple approach: convert each byte to base62 characters
        $result = '';
        $dataLength = strlen($data);
        
        for ($i = 0; $i < $dataLength; $i++) {
            $byte = ord($data[$i]);
            
            // Convert byte (0-255) to base62 representation
            // Use two base62 chars per byte for consistency
            $first = intval($byte / 62);
            $second = $byte % 62;
            
            $result .= self::BASE62_CHARS[$first] . self::BASE62_CHARS[$second];
        }
        
        return $result;
    }

    /**
     * Validate that a token meets security requirements
     * 
     * @param string $token Token to validate
     * @return bool True if token is valid
     */
    public function validateToken(string $token): bool
    {
        if (strlen($token) < self::MIN_TOKEN_LENGTH) {
            return false;
        }
        
        // Check that token only contains Base62 characters
        if (!preg_match('/^[' . preg_quote(self::BASE62_CHARS, '/') . ']+$/', $token)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if a token is unique in the payment_links table
     * 
     * @param string $token Token to check
     * @return bool True if token is unique
     */
    public function isTokenUnique(string $token): bool
    {
        $existingLink = PaymentLink::where('shortToken', $token)->first();
        return !$existingLink;
    }

    /**
     * Generate a unique token that doesn't exist in the database
     * Will retry up to 10 times to avoid infinite loops
     * 
     * @return string Unique token
     * @throws Exception If unable to generate unique token after retries
     */
    public function generateUniqueToken(): string
    {
        $maxRetries = 10;
        $retries = 0;
        
        do {
            $token = $this->generateToken();
            
            if ($this->isTokenUnique($token)) {
                return $token;
            }
            
            $retries++;
        } while ($retries < $maxRetries);
        
        throw new Exception('Unable to generate unique token after ' . $maxRetries . ' attempts');
    }
}
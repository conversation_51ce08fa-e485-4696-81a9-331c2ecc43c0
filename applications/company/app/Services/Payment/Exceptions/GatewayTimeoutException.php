<?php

declare(strict_types=1);

namespace App\Services\Payment\Exceptions;

use Exception;

class GatewayTimeoutException extends Exception
{
    public function __construct(
        string $message = 'Payment gateway timeout.',
        int $code = 504,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Get response data for API responses
     *
     * @return array
     */
    public function getResponseData(): array
    {
        return [
            'error' => $this->getMessage(),
        ];
    }
}
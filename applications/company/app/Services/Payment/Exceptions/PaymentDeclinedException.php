<?php

declare(strict_types=1);

namespace App\Services\Payment\Exceptions;

use Common\Models\PropelrTransaction;
use Exception;

class PaymentDeclinedException extends Exception
{
    private PropelrTransaction $transaction;

    public function __construct(
        string $message,
        PropelrTransaction $transaction,
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->transaction = $transaction;
    }

    public function getTransaction(): PropelrTransaction
    {
        return $this->transaction;
    }

    public function getGatewayResponseCode(): ?string
    {
        return $this->transaction->gatewayResponseCode;
    }

    public function getGatewayResponseMessage(): ?string
    {
        return $this->transaction->gatewayResponseMessage;
    }

    public function getCardResponseCode(): ?string
    {
        return $this->transaction->cardResponseCode;
    }

    public function getCardResponseMessage(): ?string
    {
        return $this->transaction->cardResponseMessage;
    }

    /**
     * Get array of gateway response details for API responses
     *
     * @return array
     */
    public function getGatewayDetails(): array
    {
        $details = [];

        if ($this->transaction->gatewayResponseCode !== null) {
            $details['gateway_response_code'] = $this->transaction->gatewayResponseCode;
        }

        if ($this->transaction->gatewayResponseMessage !== null) {
            $details['gateway_response_message'] = $this->transaction->gatewayResponseMessage;
        }

        if ($this->transaction->cardResponseCode !== null) {
            $details['card_response_code'] = $this->transaction->cardResponseCode;
        }

        if ($this->transaction->cardResponseMessage !== null) {
            $details['card_response_message'] = $this->transaction->cardResponseMessage;
        }

        return $details;
    }

    /**
     * Get complete response data for API responses including error message
     *
     * @return array
     */
    public function getResponseData(): array
    {
        $response_data = [
            'error' => $this->getMessage(),
        ];

        // Merge with gateway details
        $gateway_details = $this->getGatewayDetails();
        if (!empty($gateway_details)) {
            $response_data = array_merge($response_data, $gateway_details);
        }

        return $response_data;
    }
}
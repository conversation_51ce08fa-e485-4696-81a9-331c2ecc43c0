<?php

declare(strict_types=1);

namespace App\Services\Payment\Exceptions;

use Common\Models\PropelrTransaction;
use Exception;

class DuplicatePaymentException extends Exception
{
    private ?PropelrTransaction $recent_transaction;
    private int $wait_minutes;

    public function __construct(
        string $message,
        ?PropelrTransaction $recent_transaction = null,
        int $wait_seconds = 300, // 5 minutes default
        int $code = 409,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->recent_transaction = $recent_transaction;
        $this->wait_minutes = intval(floor($wait_seconds / 60));
    }

    public function getRecentTransaction(): ?string
    {
        return $this->recent_transaction ? bin2hex($this->recent_transaction->propelrTransactionID) : null;

    }

    public function getWaitMinutes(): int
    {
        return $this->wait_minutes;
    }

    /**
     * Get response data for API responses
     *
     * @return array
     */
    public function getResponseData(): array
    {
        $response_data = [
            'error' => $this->getMessage(),
            'wait_minutes' => $this->wait_minutes,
        ];

        if ($this->recent_transaction) {
            $response_data['recent_transaction'] = [
                'transaction_id' => bin2hex($this->recent_transaction->propelrTransactionID),
                'amount' => $this->recent_transaction->totalAmount,
                'created_at' => $this->recent_transaction->createdAt,
            ];
        }

        return $response_data;
    }
}
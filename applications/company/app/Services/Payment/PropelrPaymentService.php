<?php

declare(strict_types=1);

namespace App\Services\Payment;

use App\Classes\Acl;
use App\Classes\Log;
use App\Resources\CompanyResource;
use App\Services\CompanyFeatureService;
use App\Services\CompanySettingService;
use App\Services\GoogleRecaptchaService;
use App\Services\Payment\Exceptions\DuplicatePaymentException;
use App\Services\Payment\Exceptions\PaymentDeclinedException;
use App\Services\Payment\PaymentLogger;
use App\Services\Payment\ContextualPaymentLogger;
use App\Services\Payment\Propelr\Api\CardConnectApiService;
use App\Services\Payment\Propelr\Api\CardSecureTokenizationService;
use App\Services\Payment\Propelr\DTOs\TransactionDTO;
use Carbon\Carbon;
use Core\Components\Http\Classes\URLBuilder;
use Common\Models\Company;
use Common\Models\Interfaces\CompanyInterface;
use Common\Models\PaymentLink;
use Common\Models\PropelrMerchant;
use Common\Models\PropelrTransaction;
use Common\Models\User;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\StaticAccessors\URI;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Validation;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Config;
use Exception;
use Monolog\Logger;
use Ramsey\Uuid\Uuid;

class PropelrPaymentService
{
    /**
     * The user making the request.
     * @var User|null
     */
    private ?User $user;

    /**
     * The CardConnect API service.
     * @var CardConnectApiService
     */
    private CardConnectApiService $cardConnectApiService;

    /**
     * Logger instance
     * @var ContextualPaymentLogger|null
     */
    private ?ContextualPaymentLogger $logger = null;

    /**
     * PropelrPaymentService constructor.
     */
    public function __construct(?User $user = null)
    {
        $this->user = $user ?: Auth::user();
        $this->cardConnectApiService = new CardConnectApiService();
    }

    /**
     * Process a payment transaction
     *
     * @param PropelrMerchant $merchant
     * @param array $validated_data
     * @return PropelrTransaction
     * @throws PaymentDeclinedException|DuplicatePaymentException
     */
    public function processPayment(PropelrMerchant $merchant, array $validated_data): PropelrTransaction
    {
        $order_id = $this->generateOrderId($merchant);
        $this->preventDuplicatePayment($merchant, $validated_data);

        $transaction_dto = $this->createTransactionDTO($merchant, $validated_data, $order_id);
        $context = $transaction_dto->getLoggingContext();

        $transaction = DB::transaction(function () use ($transaction_dto, $context) {
            $this->getLog()->info('Processing payment', $context);
            return $this->cardConnectApiService->charge($transaction_dto);
        });

        // Check if transaction was declined or retryable after it's saved
        $this->validateTransactionStatus($transaction);
        return $transaction;
    }

    /**
     * Validate transaction status and throw appropriate exception if declined or retryable
     *
     * @param PropelrTransaction $transaction
     * @return void
     * @throws PaymentDeclinedException
     */
    private function validateTransactionStatus(PropelrTransaction $transaction): void
    {
        if ($transaction->status === PropelrTransaction::STATUS_AUTH_DECLINED) {
            throw new PaymentDeclinedException("Payment was declined by the issuer", $transaction, 402);
        }
        
        if ($transaction->status === PropelrTransaction::STATUS_RETRYABLE) {
            throw new PaymentDeclinedException("Payment processing issue", $transaction, 503);
        }
    }

    /**
     * Calculate convenience fee for a payment amount
     *
     * @param float $payment_amount
     * @param CompanyInterface $company
     * @param bool $is_credit_card
     * @return float
     */
    public function calculateCreditCardConvenienceFee(float $payment_amount, CompanyInterface $company): float
    {
        // Load convenience fee percentage from company settings
        $setting_service = new CompanySettingService((int) $company->companyID);
        $credit_card_processing_fee = $setting_service->get('credit_card_processing_fee', 0.0);

        // Calculate convenience fee as percentage of payment amount
        return round($payment_amount * ($credit_card_processing_fee / 100), 2);
    }

    /**
     * Calculate payment totals including convenience fee
     *
     * @param float $payment_amount
     * @param CompanyInterface $company
     * @param bool $is_credit_card
     * @return array ['payment_amount' => float, 'credit_card_processing_fee' => float, 'total_amount' => float]
     */
    public function calculatePaymentTotals(float $payment_amount, CompanyInterface $company, bool $is_credit_card = false): array
    {
        // ACH payments have no convenience fee
        $credit_card_processing_fee = 0;

        if ($is_credit_card) {
            $credit_card_processing_fee = $this->calculateCreditCardConvenienceFee($payment_amount, $company);
        }

        $total_amount = $payment_amount + $credit_card_processing_fee;

        return [
            'payment_amount' => $payment_amount,
            'credit_card_processing_fee_amount' => $credit_card_processing_fee,
            'total_amount' => $total_amount
        ];
    }

    /**
     * Validate payment form input with conditional requirements and server-side fee validation
     *
     * @param array $input
     * @param CompanyInterface $company
     * @return array
     * @throws ValidationException
     */
    public function validatePaymentInput(array $input, CompanyInterface $company): array
    {
        // Verify reCAPTCHA first
        if (!GoogleRecaptchaService::verifyToken($input['recaptcha_token'] ?? '', GoogleRecaptchaService::ACTION_PAYMENT_SUBMIT)) {
            throw new ValidationException('reCAPTCHA verification failed.');
        }

        // Determine payment method based on input
        $is_credit_card = !empty($input['card_number']);
        $is_ach = !empty($input['ach_routing_number']) || !empty($input['ach_account_number']);

        $config = FieldConfig::fromArray([
            'payment_amount' => [
                'label' => 'Payment Amount',
                'rules' => 'trim|required|numeric|greater_than[0]'
            ],
            'credit_card_processing_fee' => [
                'label' => 'Credit Card Processing Fee',
                'rules' => 'trim|required|numeric|greater_than_equal[0]'
            ],
            'total_amount' => [
                'label' => 'Total Amount',
                'rules' => 'trim|required|numeric|greater_than[0]'
            ],
            'payer_first_name' => [
                'label' => 'First Name',
                'rules' => 'trim|required|max_length[100]'
            ],
            'payer_last_name' => [
                'label' => 'Last Name',
                'rules' => 'trim|required|max_length[100]'
            ],
            'payer_email' => [
                'label' => 'Email',
                'rules' => 'trim|required|email|max_length[100]'
            ],
            'payer_phone' => [
                'label' => 'Phone',
                'rules' => 'trim|required|max_length[20]'
            ],
            'invoice_number' => [
                'label' => 'Invoice Number',
                'rules' => 'trim|optional|max_length[50]'
            ],

            'cardholder_name' => [
                'label' => 'Cardholder Name', 
                'rules' => 'trim|' . ($is_credit_card ? 'required|' : '') . 'max_length[100]'
            ],
            'card_number' => [
                'label' => 'Card Number',
                'rules' => 'trim|' . ($is_credit_card ? 'required|' : '') . 'max_length[20]'
            ],
            'card_expiry' => [
                'label' => 'Card Expiry',
                'rules' => 'trim|' . ($is_credit_card ? 'required|' : '') . 'max_length[5]'
            ],
            'card_cvv' => [
                'label' => 'CVV',
                'rules' => 'trim|' . ($is_credit_card ? 'required|' : '') . 'max_length[4]'
            ],
            'card_brand' => [
                'label' => 'Card Brand',
                'rules' => 'trim|optional|max_length[50]'
            ],
            'billing_address' => [
                'label' => 'Billing Address',
                'rules' => 'trim|' . ($is_credit_card ? 'required|' : '') . 'max_length[200]'
            ],
            'billing_city' => [
                'label' => 'Billing City',
                'rules' => 'trim|' . ($is_credit_card ? 'required|' : '') . 'max_length[100]'
            ],
            'billing_zip' => [
                'label' => 'Billing ZIP',
                'rules' => 'trim|' . ($is_credit_card ? 'required|' : '') . 'max_length[10]'
            ],

            'ach_account_name' => [
                'label' => 'Account Name',
                'rules' => 'trim|' . ($is_ach ? 'required|' : '') . 'max_length[100]'
            ],
            'ach_account_type' => [
                'label' => 'Account Type',
                'rules' => 'trim' . ($is_ach ? '|required|in_array[account_types]' : '')
            ],
            'ach_account_number' => [
                'label' => 'Account Number',
                'rules' => 'trim|' . ($is_ach ? 'required|' : '') . 'max_length[17]'
            ],
            'ach_routing_number' => [
                'label' => 'Routing Number',
                'rules' => 'trim|' . ($is_ach ? 'required|' : '') . 'max_length[9]'
            ],
            'ach_billing_address' => [
                'label' => 'ACH Billing Address',
                'rules' => 'trim|' . ($is_ach ? 'required|' : '') . 'max_length[200]'
            ],
            'ach_billing_city' => [
                'label' => 'ACH Billing City',
                'rules' => 'trim|' . ($is_ach ? 'required|' : '') . 'max_length[100]'
            ],
            'ach_billing_state' => [
                'label' => 'ACH Billing State',
                'rules' => 'trim|' . ($is_ach ? 'required|' : '') . 'max_length[50]'
            ],
            'ach_billing_zip' => [
                'label' => 'ACH Billing ZIP',
                'rules' => 'trim|' . ($is_ach ? 'required|' : '') . 'max_length[10]'
            ]
        ]);

        $config->store('account_types', ['savings', 'checking']);
        
        $validation = Validation::make()->config($config);
        $validator = $validation->run($input);

        if (!$validator->valid()) {
            throw new ValidationException('Validation failed', $validator->errors()->get());
        }

        $validated_data = $validator->data();
        $this->validatePaymentFees($validated_data, $company);

        return $validated_data;
    }

    /**
     * Validate payment fees to ensure client calculations match server-side calculations
     *
     * @param array $validated_data
     * @param CompanyInterface $company
     * @return void
     * @throws ValidationException
     */
    private function validatePaymentFees(array $validated_data, CompanyInterface $company): void
    {
        // Server-side fee validation - ensure client fees match server calculation
        $payment_amount = (float)$validated_data['payment_amount'];
        $client_credit_card_processing_fee = (float)$validated_data['credit_card_processing_fee'];
        $client_total_amount = (float)$validated_data['total_amount'];

        // Determine payment method based on validated data
        $is_credit_card = !empty($validated_data['card_number']);

        // Calculate server-side fees
        $server_totals = $this->calculatePaymentTotals($payment_amount, $company, $is_credit_card);
        $server_credit_card_processing_fee_amount = $server_totals['credit_card_processing_fee_amount'];
        $server_total_amount = $server_totals['total_amount'];

        // Validate credit card processing fee matches server calculation -- 0.01 is used for precision difference
        if (abs($client_credit_card_processing_fee - $server_credit_card_processing_fee_amount) > 0.01) {
            $this->getLog()->warning('Credit card processing fee mismatch detected', [
                'payment_amount' => $payment_amount,
                'client_credit_card_processing_fee_amount' => $client_credit_card_processing_fee,
                'server_credit_card_processing_fee_amount' => $server_credit_card_processing_fee_amount,
                'is_credit_card' => $is_credit_card,
                'company_id' => $company->companyID ?? 'unknown'
            ]);
            throw new ValidationException('Invalid credit card processing fee calculation.');
        }

        // Validate total amount matches server calculation -- 0.01 is used for precision difference
        if (abs($client_total_amount - $server_total_amount) > 0.01) {
            $this->getLog()->warning('Total amount mismatch detected', [
                'payment_amount' => $payment_amount,
                'client_total_amount' => $client_total_amount,
                'server_total_amount' => $server_total_amount,
                'is_credit_card' => $is_credit_card,
                'company_id' => $company->companyID ?? 'unknown'
            ]);
            throw new ValidationException('Invalid total amount calculation.');
        }

        $expected_client_total = $payment_amount + $client_credit_card_processing_fee;
        if (abs($client_total_amount - $expected_client_total) > 0.01) {
            $this->getLog()->warning('Client fee arithmetic validation failed', [
                'payment_amount' => $payment_amount,
                'client_credit_card_processing_fee' => $client_credit_card_processing_fee,
                'client_total_amount' => $client_total_amount,
                'expected_client_total' => $expected_client_total,
                'is_credit_card' => $is_credit_card,
                'company_id' => $company->companyID ?? 'unknown'
            ]);
            throw new ValidationException('Invalid fee calculation submitted.');
        }

        // Log successful validation for audit trail
        $this->getLog()->info('Payment fee validation successful', [
            'payment_amount' => $payment_amount,
            'credit_card_processing_fee' => $client_credit_card_processing_fee,
            'total_amount' => $client_total_amount,
            'is_credit_card' => $is_credit_card,
            'company_id' => $company->companyID ?? 'unknown'
        ]);
    }

    /**
     * Create TransactionDTO from validated form data
     *
     * @param PropelrMerchant $merchant
     * @param array $data
     * @param string|null $order_id
     * @return TransactionDTO
     * @throws Exception
     */
    public function createTransactionDTO(PropelrMerchant $merchant, array $data, ?string $order_id = null): TransactionDTO
    {
        $amount = number_format((float)($data['total_amount'] ?? $data['payment_amount']), 2, '.', '');
        $is_ach = !empty($data['ach_account_number']);

        $payment_data = $this->buildPaymentData($data, $merchant, $is_ach);
        $transaction_type = $is_ach ? PropelrTransaction::TRANSACTION_TYPE_ACH : PropelrTransaction::TRANSACTION_TYPE_CREDIT_CARD;
        $ach_account_type = $this->determineAchAccountType($data, $is_ach);
        $accountholder_name = $is_ach ? ($data['ach_account_name'] ?? '') : ($data['cardholder_name'] ?? '');

        return new TransactionDTO(
            merchant: $merchant,
            amount: $amount,
            payment_data: $payment_data,
            currency: 'USD',
            order_id: $order_id ?: $this->generateOrderId($merchant),
            customer_name: $accountholder_name,
            email: $data['payer_email'],
            phone: $data['payer_phone'] ?? null,
            base_amount: (float)$data['payment_amount'],
            credit_card_processing_fee: (float)$data['credit_card_processing_fee'],
            transaction_type: $transaction_type,
            ach_account_type: $ach_account_type,
            payment_link_id: $data['payment_link_id'] ?? null,
            invoice_number: $data['invoice_number'] ?? null,
            customer_first_name: $data['payer_first_name'] ?? null,
            customer_last_name: $data['payer_last_name'] ?? null,
            metadata: $data['metadata'] ?? null,
            card_brand: $data['card_brand'] ?? null
        );
    }


    /**
     * Build payment data array based on payment method
     * @param array $data
     * @param PropelrMerchant $merchant
     * @param bool $is_ach
     * @throws Exception
     */
    private function buildPaymentData(array $data, PropelrMerchant $merchant, bool $is_ach): array
    {
        if (!$is_ach && !empty($data['card_number'])) {
            return $this->buildCreditCardPaymentData($data, $merchant);
        }

        if ($is_ach) {
            return $this->buildAchPaymentData($data, $merchant);
        }

        return [];
    }

    /**
     * Build credit card payment data with tokenization
     *
     * @param array $data
     * @param PropelrMerchant $merchant
     * @return array
     * @throws Exception
     */
    private function buildCreditCardPaymentData(array $data, PropelrMerchant $merchant): array
    {
        $tokenization_service = new CardSecureTokenizationService();
        $tokenized_account = $tokenization_service->tokenizeCreditCard(
            $data['card_number'],
            $merchant
        );

        return [
            'name' => $data['cardholder_name'] ?? '',
            'account' => $tokenized_account,
            'expiry' => $this->formatCardExpiry($data['card_expiry'] ?? ''),
            'cvv2' => $data['card_cvv'] ?? '',
            'address' => $data['billing_address'] ?? '',
            'city' => $data['billing_city'] ?? '',
            'region' => $data['billing_state'] ?? '',
            'postal' => $data['billing_zip'] ?? ''
        ];
    }

    /**
    * Build ACH payment data with tokenization
    *
    * @param array $data
    * @param PropelrMerchant $merchant
    * @return array
    * @throws Exception
    */
    private function buildAchPaymentData(array $data, PropelrMerchant $merchant): array
    {
        $tokenization_service = new CardSecureTokenizationService();
        $tokenized_account = $tokenization_service->tokenizeAchAccount(
            $data['ach_routing_number'] ?? '',
            $data['ach_account_number'],
            $merchant
        );

        return [
            'name' => $data['ach_account_name'] ?? '',
            'account' => $tokenized_account,
            'bankaba' => $data['ach_routing_number'] ?? '',
            'address' => $data['ach_billing_address'] ?? '',
            'city' => $data['ach_billing_city'] ?? '',
            'region' => $data['ach_billing_state'] ?? '',
            'postal' => $data['ach_billing_zip'] ?? ''
        ];
    }

    /**
     * Determine ACH account type from form data
     *
     * @param array $data
     * @param bool $is_ach
     * @return int
     */
    private function determineAchAccountType(array $data, bool $is_ach): ?int
    {
        if (!$is_ach || empty($data['ach_account_type'])) {
            return null;
        }

        return $data['ach_account_type'] === 'savings'
            ? PropelrTransaction::ACH_ACCOUNT_TYPE_SAVINGS
            : PropelrTransaction::ACH_ACCOUNT_TYPE_CHECKING;
    }

    /**
     * Format card expiry from MM/YY to MMYY
     *
     * @param string $expiry
     * @return string
     */
    private function formatCardExpiry(string $expiry): string
    {
        // Remove any non-digit characters -- MMYY format
        $clean = preg_replace('/\D/', '', $expiry);
        
        if (strlen($clean) === 4) {
            return $clean;
        }
        
        return $clean;
    }

    /**
     * Authorize company feature access
     *
     * @param Company $company
     * @param bool $internal
     * @return void
     * @throws HttpResponseException
     * @throws AppException
     */
    public function authorizeFeatureAccess(Company $company, $internal = false): void
    {
        $allowed_statuses = [CompanyInterface::STATUS_TRIAL, CompanyInterface::STATUS_ACTIVE];

        if (!in_array($company->status, $allowed_statuses, true)) {
            throw new HttpResponseException(403, 'Company is not active.');
        }

        if (!$this->canProcessPayments($company, !$internal)) {
            throw new HttpResponseException(403, 'Feature not enabled and active.');
        }
    }

    /**
     * Check if a company can process payments
     *
     * @param Company $company
     * @param bool $require_active Whether to require is_payments_active setting to be true
     * @return bool
     */
    public function canProcessPayments(Company $company, bool $require_active = true): bool
    {
        // Check if feature is enabled
        $company_feature_service = CompanyFeatureService::getByCompanyID($company->companyID);
        $is_feature_enabled = $company_feature_service->has(\Common\Models\Feature::PAYMENTS, false);
        if (!$is_feature_enabled) {
            return false;
        }

        // Check if payments are active
        if ($require_active) {
            $setting_service = new CompanySettingService($company->companyID);
            $is_payments_active = $setting_service->get('is_payments_active', false);
            if (!$is_payments_active) {
                return false;
            }
        }

        // Check if merchant exists and has proper status
        $merchant = PropelrMerchant::where('companyUUID', $company->companyUUID)
            ->whereNull('deletedAt')
            ->first();

        if (!$merchant) {
            return false;
        }

        // Check merchant status is boarded or live
        $allowed_statuses = [
            PropelrMerchant::APPLICATION_STATUS_BOARDED,
            PropelrMerchant::APPLICATION_STATUS_LIVE
        ];
        if (!in_array($merchant->status, $allowed_statuses, true)) {
            return false;
        }

        // Check if ready to process
        if ($merchant->isReadyToProcess !== PropelrMerchant::IS_READY_TO_PROCESS_READY) {
            return false;
        }

        return true;
    }

    /**
     * Get logger instance
     *
     * @return ContextualPaymentLogger
     */
    private function getLog(): ContextualPaymentLogger
    {
        if ($this->logger === null) {
            $this->logger = PaymentLogger::getInstance()->withContext('SERVICE');
        }
        return $this->logger;
    }

    /**
     * Generate unique order ID for transaction
     *
     * @param PropelrMerchant $merchant
     * @return string
     */
    private function generateOrderId(PropelrMerchant $merchant): string
    {
        return 'ORDER-' . time() . '-' . substr(bin2hex($merchant->propelrMerchantID), 0, 8);
    }

    /**
     * Prevent duplicate payment processing within time window
     * Default time window is 5 minutes.
     *
     * @param PropelrMerchant $merchant
     * @param array $validated_data
     * @throws DuplicatePaymentException
     */
    private function preventDuplicatePayment(PropelrMerchant $merchant, array $validated_data): void
    {
        // Look for recent transactions with same amount and merchant within 5 minutes
        $cutoff_time = Carbon::now()->subMinutes(5)->toDateTimeString();
        $amount = number_format((float)$validated_data['payment_amount'], 2, '.', '');
        
        $recent_transaction = PropelrTransaction::where('propelrMerchantID', $merchant->propelrMerchantID)
            ->where('customerEmail', $validated_data['payer_email'])
            ->where('totalAmount', $amount)
            ->where('createdAt', '>=', $cutoff_time)
            ->first();

        if ($recent_transaction) {
            $wait_seconds = 300 - Carbon::now()->diffInSeconds($recent_transaction->createdAt);
            $wait_seconds = max(0, $wait_seconds);
            
            throw new DuplicatePaymentException(
                'Duplicate payment detected. Please wait before submitting again.',
                $recent_transaction,
                $wait_seconds,
                409
            );
        }
    }

    /**
     * Void a previously authorized transaction
     *
     * @param string $transaction_id
     * @return array
     * @throws Exception
     */
    public function voidTransaction(string $transaction_id): array
    {
        try {
            $transaction_bytes = UUID::fromString($transaction_id)->getBytes();
            
            $transaction = PropelrTransaction::where('propelrTransactionID', $transaction_bytes)
                ->with('merchant')
                ->first();

            if (!$transaction) {
                throw new Exception('Transaction not found');
            }

            // Validate transaction can be voided
            if (!$transaction->canVoid()) {
                if ($transaction->isVoided()) {
                    throw new Exception('Transaction has already been voided');
                }
                if ($transaction->status !== PropelrTransaction::STATUS_CAPTURED) {
                    throw new Exception('Only captured transactions can be voided');
                }
                throw new Exception('Transaction cannot be voided at this time');
            }

            // Company-based access control
            if ($this->user) {
                $user_company = $this->user->company()->first(['companyUUID']);
                if ($user_company && $transaction->merchant->companyUUID !== $user_company->companyUUID) {
                    throw new Exception('Access denied to this transaction');
                }
            }

            $mid = $transaction->merchant->getMidForTransactionType($transaction->type);
            $context = [
                'transaction_id' => $transaction_id,
                'retref' => $transaction->retref,
                'merchant_mid' => $mid,
                'transaction_type' => $transaction->type,
                'amount' => $transaction->totalAmount
            ];

            $this->getLog()->info('Attempting to void transaction', $context);

            // Call CardConnect void API
            $void_response = $this->cardConnectApiService->void(
                $mid,
                $transaction->retref,
                (float) $transaction->totalAmount
            );

            // Update transaction based on void response
            return DB::transaction(function () use ($transaction, $void_response, $context) {
                if (($void_response['respstat'] ?? '') === 'A') {
                    // Successful void
                    $transaction->update([
                        'status' => PropelrTransaction::STATUS_VOIDED,
                        'voidedAt' => Carbon::now(),
                        'voidReference' => $void_response['retref'] ?? null
                    ]);

                    $this->getLog()->info('Transaction voided successfully', array_merge($context, [
                        'void_response' => $void_response
                    ]));

                    return [
                        'success' => true,
                        'message' => 'Transaction voided successfully',
                        'void_reference' => $void_response['retref'] ?? null
                    ];
                } else {
                    // Failed void - save error details
                    $error_message = $this->mapVoidErrorMessage($void_response);
                    
                    $this->getLog()->warning('Transaction void failed', array_merge($context, [
                        'void_response' => $void_response,
                        'error_message' => $error_message
                    ]));

                    throw new Exception($error_message);
                }
            });

        } catch (Exception $e) {
            $this->getLog()->error('Void transaction error', [
                'transaction_id' => $transaction_id,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);
            
            throw $e;
        }
    }

    /**
     * Map CardConnect void error response to user-friendly message
     *
     * @param array $response
     * @return string
     */
    private function mapVoidErrorMessage(array $response): string
    {
        $respstat = $response['respstat'] ?? '';
        $respcode = $response['respcode'] ?? '';
        $resptext = $response['resptext'] ?? '';

        // Map common error scenarios
        if ($respstat === 'C') {
            switch ($respcode) {
                case '29':
                    return 'Transaction cannot be voided - it has already been settled';
                case '12':
                    return 'Invalid transaction reference';
                case '96':
                    return 'System error occurred while processing void';
                default:
                    return $resptext ?: 'Transaction cannot be voided at this time';
            }
        }

        return $resptext ?: 'Void operation failed';
    }

    /**
     * Refund a previously settled transaction
     *
     * @param string $transaction_id
     * @return array
     * @throws Exception
     */
    public function refundTransaction(string $transaction_id): array
    {
        try {
            // Convert UUID string to bytes for database lookup
            $transaction_bytes = UUID::fromString($transaction_id)->getBytes();
            
            $transaction = PropelrTransaction::where('propelrTransactionID', $transaction_bytes)
                ->with('merchant')
                ->first();

            if (!$transaction) {
                throw new Exception('Transaction not found');
            }

            // Validate transaction can be refunded
            if (!$transaction->canRefund()) {
                if ($transaction->isRefunded()) {
                    throw new Exception('Transaction has already been refunded');
                }
                if ($transaction->isVoided()) {
                    throw new Exception('Cannot refund a voided transaction');
                }
                if ($transaction->status !== PropelrTransaction::STATUS_CAPTURED) {
                    throw new Exception('Only captured transactions can be refunded');
                }
                throw new Exception('Transaction cannot be refunded at this time');
            }

            if ($this->user) {
                $user_company = $this->user->company()->first(['companyUUID']);
                if ($user_company && $transaction->merchant->companyUUID !== $user_company->companyUUID) {
                    throw new Exception('Access denied to this transaction');
                }
            }

            $mid = $transaction->merchant->getMidForTransactionType($transaction->type);
            $context = [
                'transaction_id' => $transaction_id,
                'retref' => $transaction->retref,
                'merchant_mid' => $mid,
                'transaction_type' => $transaction->type,
                'amount' => $transaction->totalAmount
            ];

            $this->getLog()->info('Attempting to refund transaction', $context);

            // Call CardConnect refund API
            $refund_response = $this->cardConnectApiService->refund(
                $mid,
                $transaction->retref,
                (float) $transaction->totalAmount
            );

            // todo - filipe - move this to api service or another layer.
            // Update transaction based on refund response
            return DB::transaction(function () use ($transaction, $refund_response, $context) {
                if (($refund_response['respstat'] ?? '') === 'A') {
                    // Successful refund
                    $transaction->update([
                        'status' => PropelrTransaction::STATUS_REFUNDED,
                        'refundedAt' => Carbon::now(),
                        'refundReference' => $refund_response['retref'] ?? null
                    ]);

                    $this->getLog()->info('Transaction refunded successfully', array_merge($context, [
                        'refund_response' => $refund_response
                    ]));

                    return [
                        'success' => true,
                        'message' => 'Transaction refunded successfully',
                        'refund_reference' => $refund_response['retref'] ?? null
                    ];
                } else {
                    // Failed refund - save error details
                    $error_message = $this->mapRefundErrorMessage($refund_response);
                    
                    // Note: Error details logged but not stored in database

                    $this->getLog()->warning('Transaction refund failed', array_merge($context, [
                        'refund_response' => $refund_response,
                        'error_message' => $error_message
                    ]));

                    throw new Exception($error_message);
                }
            });

        } catch (Exception $e) {
            $this->getLog()->error('Refund transaction error', [
                'transaction_id' => $transaction_id,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);
            
            throw $e;
        }
    }

    /**
     * Map CardConnect refund error response to user-friendly message
     *
     * @param array $response
     * @return string
     */
    private function mapRefundErrorMessage(array $response): string
    {
        $respstat = $response['respstat'] ?? '';
        $respcode = $response['respcode'] ?? '';
        $resptext = $response['resptext'] ?? '';

        // Map common refund error scenarios
        if ($respstat === 'C') {
            switch ($respcode) {
                case '12':
                    return 'Invalid transaction reference for refund';
                case '29':
                    return 'Transaction cannot be refunded - may not be settled yet';
                case '54':
                    return 'Refund amount exceeds original transaction amount';
                case '96':
                    return 'System error occurred while processing refund';
                default:
                    return $resptext ?: 'Transaction cannot be refunded at this time';
            }
        }

        return $resptext ?: 'Refund operation failed';
    }

    /**
     * Get base payment data shared between API and checkout contexts
     *
     * @param Company $company
     * @param PropelrMerchant|null $merchant
     * @return array
     */
    public function getBasePaymentData(Company $company, ?PropelrMerchant $merchant = null): array
    {
        $setting_service = new CompanySettingService($company->companyID);
        
        // Get company settings
        $company_settings = [
            'is_payments_active' => $setting_service->get('is_payments_active', false),
            'ach_acceptance_enabled' => $setting_service->get('payment_ach_acceptance_enabled', false),
            'credit_card_acceptance_enabled' => $setting_service->get('payment_credit_card_acceptance_enabled', false),
            'credit_card_processing_fee' => $setting_service->get('credit_card_processing_fee', 0.0)
        ];

        // Get company logo only if it exists and generate public URL
        $company_logo_data = null;
        
        if (!empty($company->logoFileID)) {
            $company_scope = Scope::make()
                ->fields(['companyID'])
                ->with([
                    'logo_media_urls' => [
                        'fields' => ['original']
                    ],
                    'logo' => [
                        'fields' => ['name']
                    ]
                ]);

            $acl = Acl::make()->setCompanyID($company->companyID);
            $company_logo = CompanyResource::make($acl)
                ->entity($company->companyID)
                ->scope($company_scope)
                ->run();

            // Generate public URL with checksum for authentication bypass
            if ($company_logo && !empty($company_logo['logo_media_urls']['original'])) {
                $public_url = URLBuilder::fromString($company_logo['logo_media_urls']['document_thumbnail'])
                    ->csm()
                    ->build();

                $company_logo_data = [
                    'url' => $public_url,
                    'name' => $company_logo['logo']['name'] ?? null
                ];
            }
        }

        if (!$merchant['isACHAvailable']) {
            $company_settings['ach_acceptance_enabled'] = false;
        }

        return [
            'company_settings' => $company_settings,
            'company_logo' => $company_logo_data,
            'company_colors' => $company->getButtonColors(),
            'environment' => SERVER_ROLE,
        ];
    }

    /**
     * Get payment data for authenticated API responses (includes merchant data)
     *
     * @param Company $company
     * @param PropelrMerchant $merchant
     * @return array
     * @throws HttpResponseException
     */
    public function getApiPaymentData(Company $company, PropelrMerchant $merchant): array
    {
        $base_data = $this->getBasePaymentData($company, $merchant);

        $payment_link = PaymentLink::where('propelrMerchantID', $merchant->propelrMerchantID)
            ->orderBy('createdAt', 'desc')
            ->first();

        // Get payment data for checkout page with payment link action URL
        $short_token = $payment_link ? $payment_link->shortToken : null;
        $action_url = $payment_link ? URI::route('payments.link.page', ['shortToken' => $short_token])->build() : null;

        // Add merchant data for authenticated API responses
        $merchant_data = [
            'merchant_uuid' => strtoupper(bin2hex($merchant->propelrMerchantID)),
            'status' => $merchant->status,
            'created_at' => $merchant->createdAt,
            'gateway_status' => $merchant->gatewayStatus ?? null,
            'signature_url' => $merchant->signatureUrl ?? null,
            'signature_status' => $merchant->signatureStatus ?? null,
            'submitted_at' => $merchant->submittedAt,
            'is_ready_to_process' => $merchant->isReadyToProcess,
            'is_ach_available' => $merchant->isACHAvailable
        ];

        return array_merge($base_data, [
            'merchant_data' => $merchant_data,
            'action_url' => $action_url,
        ]);
    }

    /**
     * Get payment data for public checkout pages (includes captcha, no merchant UUID)
     *
     * @param Company $company
     * @param PropelrMerchant|null $merchant
     * @param string|null $action_url
     * @return array
     */
    public function getCheckoutPaymentData(Company $company, ?PropelrMerchant $merchant = null, ?string $action_url = null): array
    {
        $base_data = $this->getBasePaymentData($company, $merchant);
        
        $checkout_data = [
            'captcha_key' => Config::get('payments.recaptcha_key'),
        ];

        // Add action URL if provided
        if ($action_url) {
            $checkout_data['action_url'] = $action_url;
        }

        return array_merge($base_data, $checkout_data);
    }
}
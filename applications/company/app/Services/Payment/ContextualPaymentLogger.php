<?php

namespace App\Services\Payment;

use Monolog\Logger;

/**
 * Contextual logger that automatically prefixes messages with a context
 */
class ContextualPaymentLogger
{
    private Logger $logger;
    private string $context;
    private PaymentLogger $paymentLogger;

    public function __construct(Logger $logger, string $context)
    {
        $this->logger = $logger;
        $this->context = $context;
        $this->paymentLogger = PaymentLogger::getInstance();
    }

    /**
     * Log debug message
     */
    public function debug(string $message, array $data = []): void
    {
        $this->logger->debug("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Log info message
     */
    public function info(string $message, array $data = []): void
    {
        $this->logger->info("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Log notice message
     */
    public function notice(string $message, array $data = []): void
    {
        $this->logger->notice("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Log warning message
     */
    public function warning(string $message, array $data = []): void
    {
        $this->logger->warning("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Log error message
     */
    public function error(string $message, array $data = []): void
    {
        $this->logger->error("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Log critical message
     */
    public function critical(string $message, array $data = []): void
    {
        $this->logger->critical("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Log alert message
     */
    public function alert(string $message, array $data = []): void
    {
        $this->logger->alert("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Log emergency message
     */
    public function emergency(string $message, array $data = []): void
    {
        $this->logger->emergency("[{$this->context}] $message", $this->paymentLogger->sanitizeData($data));
    }

    /**
     * Get the underlying Monolog logger
     */
    public function getLogger(): Logger
    {
        return $this->logger;
    }
}
<?php

declare(strict_types=1);

namespace App\Services\Payment;

use Common\Models\PaymentLink;

class PaymentMetadataService
{
    protected PaymentLinkService $paymentLinkService;

    public function __construct()
    {
        $this->paymentLinkService = new PaymentLinkService();
    }

    /**
     * Extract and enrich metadata from payment form input
     *
     * @param array $input Raw form input data
     * @param PaymentLink $paymentLink Payment link context
     * @return array Enriched metadata array
     */
    public function extractAndEnrichMetadata(array $input, PaymentLink $paymentLink): array
    {
        $metadata = $this->extractMetadataFromInput($input);
        
        if ($this->shouldEnrichWithBusinessContext($metadata)) {
            $business_context = $this->getBusinessContext($metadata, $paymentLink);
            if ($business_context) {
                $metadata = array_merge($metadata, $business_context);
            }
        }
        
        return $metadata;
    }

    /**
     * Extract metadata fields with 'metadata_' prefix from input
     *
     * @param array $input Raw form input
     * @return array Extracted metadata
     */
    private function extractMetadataFromInput(array $input): array
    {
        $metadata = [];
        
        foreach ($input as $key => $value) {
            if (strpos($key, 'metadata_') === 0) {
                $metadata_key = substr($key, 9);
                $metadata[$metadata_key] = $value;
            }
        }
        
        return $metadata;
    }

    /**
     * Check if metadata should be enriched with business context
     *
     * @param array $metadata Extracted metadata
     * @return bool True if enrichment should occur
     */
    private function shouldEnrichWithBusinessContext(array $metadata): bool
    {
        return isset($metadata['bid_id']) && isset($metadata['invoice_type']);
    }

    /**
     * Get business context metadata from evaluation data
     *
     * @param array $metadata Base metadata
     * @param PaymentLink $paymentLink Payment link context
     * @return array|null Business context metadata
     */
    private function getBusinessContext(array $metadata, PaymentLink $paymentLink): ?array
    {
        return $this->paymentLinkService->getBusinessContextMetadata(
            $paymentLink,
            (int)$metadata['invoice_type'],
            (int)$metadata['bid_id'],
            isset($metadata['invoice_order']) ? (int)$metadata['invoice_order'] : null
        );
    }
}
<?php

declare(strict_types=1);

namespace App\Services\Payment;

use Carbon\Carbon;
use Common\Models\Company;
use Common\Models\CustomBid;
use Common\Models\EvaluationInvoice;
use Common\Models\PaymentLink;
use Common\Models\PropelrMerchant;
use Exception;
use Ramsey\Uuid\Uuid;


class PaymentLinkService
{
    protected PaymentLinkTokenService $tokenService;

    public function __construct()
    {
        $this->tokenService = new PaymentLinkTokenService();
    }

    public function findActiveByToken(string $token): ?PaymentLink
    {
        return PaymentLink::where('shortToken', $token)
            ->whereNull('deletedAt')
            ->first();
    }

    public function findByCompanyID(int $companyID): ?PaymentLink
    {
        $company = Company::find($companyID);
        if (!$company) {
            return null;
        }

        $merchant = PropelrMerchant::where('companyUUID', $company->companyUUID)->first();

        if (!$merchant) {
            return null;
        }

        return PaymentLink::where('propelrMerchantID', $merchant->propelrMerchantID)
            ->whereNull('deletedAt')
            ->first();
    }


    /**
     * Create a payment link for a merchant
     *
     * @param string $merchantID UUID of the merchant
     * @param int $createdByUserID User creating the link
     * @return PaymentLink|null
     * @throws Exception
     */
    public function createLinkForMerchant(string $merchantID, int $createdByUserID): ?PaymentLink
    {
        try {
            $merchant_bytes = Uuid::fromString($merchantID)->getBytes();
            $merchant = PropelrMerchant::where('propelrMerchantID', $merchant_bytes)->first();
            
            if (!$merchant) {
                throw new Exception('Merchant not found');
            }

            // Create payment link (simplified structure)
            $payment_link = PaymentLink::create([
                'paymentLinkID' => Uuid::uuid4()->getBytes(),
                'propelrMerchantID' => $merchant->propelrMerchantID,
                'shortToken' => $this->tokenService->generateUniqueToken(),
                'createdByUserID' => $createdByUserID,
            ]);
            
            return $payment_link;
            
        } catch (Exception $e) {
            error_log("Failed to create payment link for merchant {$merchantID}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if a payment link is valid and usable for checkout
     * 
     * @param PaymentLink $link
     * @return array Validation result with 'valid' boolean and 'reason' if invalid
     */
    public function validateLinkForCheckout(PaymentLink $link): array
    {
        if ($link->deletedAt) {
            return ['valid' => false, 'reason' => 'deleted'];
        }

        if (!$link->merchant) {
            return ['valid' => false, 'reason' => 'merchant_not_found'];
        }
        $company = Company::where('companyUUID', $link->merchant->companyUUID)->first();
        if (!$company) {
            return ['valid' => false, 'reason' => 'company_not_found'];
        }

        $payment_service = new PropelrPaymentService();
        if (!$payment_service->canProcessPayments($company)) {
            return ['valid' => false, 'reason' => 'payment_processing_not_available'];
        }

        return ['valid' => true];
    }

    /**
     * Get prefill data for checkout based on bid/evaluation information
     *
     * @param PaymentLink $paymentLink
     * @param int $invoiceType Type of invoice (1-4)
     * @param int $bidId The evaluationID from customBid table
     * @param int|null $invoiceOrder Required for type 3 (middle invoice)
     * @return array|null Prefill data structure or null if not found
     */
    public function getCheckoutPrefillData(
        PaymentLink $paymentLink,
        int $invoiceType,
        int $bidId,
        ?int $invoiceOrder = null
    ): ?array {
        try {
            $company = Company::where('companyUUID', $paymentLink->merchant->companyUUID)->first();
            if (!$company) {
                return null;
            }
            
            $customBid = CustomBid::where('evaluationID', $bidId)
                ->whereHas('evaluation.project.customer', function($q) use ($company) {
                    // Ensure bid belongs to same company as payment link
                    $q->where('companyID', $company->companyID);
                })
                ->with(['evaluation.project.customer.primaryPhone', 'evaluation.project'])
                ->first();
            
            if (!$customBid) {
                return null; // Graceful degradation - continue without prefill
            }
            
            $evaluation = $customBid->evaluation;
            $property = $evaluation->project?->property;
            $customer = $property?->customer;
            
            // Determine amount and invoice number based on type
            $amount = null;
            $invoice_number = null;
            
            switch ($invoiceType) {
                case 1: // Accept (Project Start)

                    $amount = $customBid->bidAcceptanceAmount;
                    $invoice_number = $customBid->bidAcceptanceNumber;
                    break;
                    
                case 2: // Complete (Project Completion)
                    $amount = $customBid->projectCompleteAmount;
                    $invoice_number = $customBid->projectCompleteNumber;
                    break;
                    
                case 3: // Middle Invoice (Progress Payment)
                    if ($invoiceOrder) {
                        $evaluationInvoice = EvaluationInvoice::where('evaluationID', $bidId)
                            ->where('invoiceSort', $invoiceOrder)
                            ->first();
                        
                        if ($evaluationInvoice) {
                            $amount = $evaluationInvoice->invoiceAmount;
                            $invoice_number = $evaluationInvoice->invoiceNumber;
                        }
                    }
                    break;
                    
                case 4: // Scope Change
                    $amount = $customBid->bidScopeChangeTotal;
                    $invoice_number = $customBid->bidScopeChangeNumber;
                    break;
            }
            
            // Get primary phone from customer (using relationship)
            $primary_phone = $customer?->primaryPhone?->phoneNumber;
            
            return [
                'customer' => [
                    'first_name' => $customer->firstName ?? null,
                    'last_name' => $customer->lastName ?? null,
                    'email' => $customer->email ?? null,
                    'phone' => $primary_phone,
                ],
                'address' => [
                    'billing_address' => $property->address ?? null,
                    'billing_city' => $property->city ?? null,
                    'billing_state' => $property->state ?? null,
                    'billing_zip' => $property->zip ?? null,
                ],
                'amount' => $amount,
                'invoice_number' => $invoice_number,
            ];
            
        } catch (Exception $e) {
            error_log("Error getting checkout prefill data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract business context metadata from evaluation for payment tracking
     *
     * @param PaymentLink $paymentLink
     * @param int $invoiceType The invoice type (1-4)
     * @param int $bidId The evaluationID from customBid table
     * @param int|null $invoiceOrder The invoice order for middle invoices
     * @return array|null Business context metadata or null if not found
     */
    public function getBusinessContextMetadata(PaymentLink $paymentLink, int $invoiceType, int $bidId, ?int $invoiceOrder = null): ?array
    {
        try {
            $company = Company::where('companyUUID', $paymentLink->merchant->companyUUID)->first();
            if (!$company) {
                return null;
            }
            
            $customBid = CustomBid::where('evaluationID', $bidId)
                ->whereHas('evaluation.project.customer', function($q) use ($company) {
                    // Ensure bid belongs to same company as payment link
                    $q->where('companyID', $company->companyID);
                })
                ->with(['evaluation.project'])
                ->first();
            
            if (!$customBid) {
                return null; // Continue without metadata
            }
            
            $evaluation = $customBid->evaluation;
            $project = $evaluation->project;
            
            // Convert project UUID from bytes to string format
            $projectUUID = null;
            if ($project?->projectUUID) {
                try {
                    $projectUUID = strtoupper(str_replace('-', '', Uuid::fromBytes($project->projectUUID)->toString()));
                } catch (Exception $e) {
                    // If UUID conversion fails, leave as null
                    $projectUUID = null;
                }
            }

            $bidItemID = null;
            if ($evaluation?->bidItemID) {
                try {
                    $bidItemID = strtoupper(str_replace('-', '', Uuid::fromBytes($evaluation->bidItemID)->toString()));
                } catch(Exception $e) {
                    $bidItemID = null;
                }
            }

            $metadata = [
                'evaluation_id' => $evaluation->evaluationID,
                'project_id' => $project->projectID ?? null,
                'project_uuid' => $projectUUID,
                'invoice_type' => $invoiceType,
                'bid_item_id' => $bidItemID,
            ];
            
            // Add invoice order for middle invoices
            if ($invoiceType === 3 && $invoiceOrder !== null) {
                $metadata['invoice_order'] = $invoiceOrder;
            }
            
            return $metadata;
            
        } catch (Exception $e) {
            error_log("Error getting business context metadata: " . $e->getMessage());
            return null;
        }
    }

}
<?php

declare(strict_types=1);

namespace App\Services\Payment\CoPilot\Api;

use App\Classes\Log;
use App\Services\Payment\CoPilot\CoPilotCredentials;
use Core\StaticAccessors\Config;
use Core\Components\Cache\StaticAccessors\Cache;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Monolog\Logger;

/**
 * CoPilot API Helper - Handles HTTP client management and OAuth token operations
 */
class CoPilotApiHelper
{
    private static array $clients = [];
    private Logger $logger;

    private string $TOKEN_ENDPOINT;
    private string $MERCHANT_ENDPOINT;
    private string $SIGNATURE_ENDPOINT;
    private string $STATUS_ENDPOINT;

    public function __construct()
    {
        $this->logger = Log::create('copilot_api', [
            'file' => 'copilot_api.log'
        ]);
        
        // API endpoints
        $this->TOKEN_ENDPOINT = '/auth/realms/cardconnect/protocol/openid-connect/token';
        $this->MERCHANT_ENDPOINT = '/merchant';
        $this->SIGNATURE_ENDPOINT = '/merchant/{merchantId}/signature';
        $this->STATUS_ENDPOINT = '/merchant/{merchantId}/status';
    }

    /**
     * Get HTTP client for CoPilot API with OAuth token
     */
    public function getHttpClient(CoPilotCredentials $credentials): Client
    {
        $token = $this->getValidToken($credentials);
        $credentials_key = $this->getCacheKey($credentials);

        // Check if we have a client for these credentials and if the token is still valid
        if (isset(self::$clients[$credentials_key])) {
            $current_auth = self::$clients[$credentials_key]->getConfig('headers')['Authorization'] ?? '';
            $expected_auth = 'Bearer ' . $token;
            
            // If token matches, reuse the client
            if ($current_auth === $expected_auth) {
                $this->logger->info('Reusing cached CoPilot HTTP client', ['credentials_key' => $credentials_key]);
                return self::$clients[$credentials_key];
            }
            
            // Token changed, need to create new client
            $this->logger->info('Token changed, creating new CoPilot HTTP client', ['credentials_key' => $credentials_key]);
            unset(self::$clients[$credentials_key]);
        }
        
        // Create new client with current token
        self::$clients[$credentials_key] = new Client([
            'base_uri' => Config::get('payments.copilot.api_url'),
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'X-CopilotAPI-Version' => Config::get('payments.copilot.api_version'),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'timeout' => 30,
        ]);
        
        return self::$clients[$credentials_key];
    }

    /**
     * Get OAuth client for token operations
     */
    private function getOAuthClient(): Client
    {
        return new Client([
            'base_uri' => Config::get('payments.copilot.token_url'),
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Accept' => 'application/json'
            ],
            'timeout' => 10,
        ]);
    }

    /**
     * Get valid OAuth token (cached or fetch new one)
     * @throws \Exception
     */
    private function getValidToken(CoPilotCredentials $credentials): string
    {
        $cache_key = $this->getCacheKey($credentials);
        $cached_token_data = Cache::get($cache_key);
        
        if ($cached_token_data && 
            is_array($cached_token_data) && 
            isset($cached_token_data['token'], $cached_token_data['expires_at']) &&
            $cached_token_data['expires_at'] > time() + 15) { // 15 seconds buffer
            
            $this->logger->info('Using cached CoPilot OAuth token', [
                'username' => $credentials->getUsername(),
                'expires_at' => $cached_token_data['expires_at'],
                'time_remaining' => $cached_token_data['expires_at'] - time()
            ]);
            
            return $cached_token_data['token'];
        }

        // Fetch new token
        return $this->fetchNewToken($credentials, $cache_key);
    }

    private function getCacheKey(CoPilotCredentials $credentials): string
    {
        return 'copilot_oauth_token_' . md5($credentials->getUsername() . $credentials->getClientId());
    }

    /**
     * Fetch new OAuth token from CoPilot
     * @throws \Exception
     */
    private function fetchNewToken(CoPilotCredentials $credentials, string $cache_key): string
    {
        try {
            $client = $this->getOAuthClient();
            $this->logger->info('Fetching new CoPilot OAuth token');
            
            $response = $client->request('POST', '', [ 'form_params' => [
                    'username' => $credentials->getUsername(),
                    'password' => $credentials->getPassword(),
                    'grant_type' => 'password',
                    'client_id' => $credentials->getClientId(),
                    'client_secret' => $credentials->getClientSecret(),
            ]]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            if (!isset($data['access_token'])) {
                throw new \Exception('No access token in response');
            }

            $expires_in = $data['expires_in'] ?? 300; // Default 5 minutes
            $expires_at = time() + $expires_in;
            
            // Cache the token in Redis with TTL
            $token_data = [
                'token' => $data['access_token'],
                'expires_at' => $expires_at
            ];
            
            // Store in Redis with TTL slightly longer than the actual expiration to handle clock skew
            Cache::put($cache_key, $token_data, $expires_in + 10);

            $this->logger->info('CoPilot OAuth token refreshed and cached successfully', [ 'cache_key' => $cache_key ]);
            return $data['access_token'];

        } catch (\Exception $e) {
            throw new \Exception('Failed to authenticate with CoPilot:', 0, $e);
        }
    }

    /**
     * Handle API exceptions and provide structured error information
     */
    public function handleApiException(\Exception $exception): array
    {
        $statusCode = 0;
        $responseBody = '';
        $responseHeaders = [];
        
        if ($exception instanceof RequestException) {
            $statusCode = $exception->hasResponse() ? $exception->getResponse()->getStatusCode() : 0;
            $responseBody = $exception->hasResponse() ? $exception->getResponse()->getBody()->getContents() : '';
            $responseHeaders = $exception->hasResponse() ? $exception->getResponse()->getHeaders() : [];
        }

        $error_data = [
            'status_code' => $statusCode,
            'message' => $exception->getMessage(),
            'response_body' => $responseBody,
            'response_headers' => $responseHeaders
        ];

        // Try to parse JSON error response
        if (!empty($responseBody)) {
            try {
                $jsonError = json_decode($responseBody, true);
                if (isset($jsonError['errors'])) {
                    $errorData['copilot_errors'] = $jsonError['errors'];
                }
            } catch (\Exception $e) {
                $this->logger->warning('CoPilot API response not valid JSON', [
                    'json_parse_error' => $e->getMessage(),
                    'raw_response' => $responseBody
                ]);
            }
        }

        $this->logger->error('CoPilot API request failed', $error_data);
        return $error_data;
    }

    /**
     * Get logger instance
     */
    public function getLog(): Logger
    {
        return $this->logger;
    }

}
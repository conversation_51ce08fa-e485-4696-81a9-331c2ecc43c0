<?php

declare(strict_types=1);

namespace App\Services\Payment\CoPilot;

use Common\Models\PropelrMerchant;
use Core\StaticAccessors\Config;

/**
 * DTO for CoPilot API credentials
 */
class CoPilotCredentials
{
    private string $username;
    private string $password;
    private string $clientId;
    private string $clientSecret;

    public function __construct(string $username, string $password, string $clientId, string $clientSecret)
    {
        $this->username = $username;
        $this->password = $password;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
    }

    /**
     * Create credentials from environment variables via Config
     */
    public static function fromEnv(): self
    {
        $username = Config::get('payments.copilot.username');
        $password = Config::get('payments.copilot.password');
        $clientId = Config::get('payments.copilot.client_id');
        $clientSecret = Config::get('payments.copilot.client_secret');
        
        if (!$username || !$password || !$clientId || !$clientSecret) {
            throw new \InvalidArgumentException('CoPilot credentials not configured in environment');
        }
        
        return new self($username, $password, $clientId, $clientSecret);
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getApplicationTemplateId(): string
    {
        return Config::get('payments.copilot.application_template_id');
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function getClientId(): string
    {
        return $this->clientId;
    }

    public function getClientSecret(): string
    {
        return $this->clientSecret;
    }
}
<?php

namespace App\Services\Payment;

use App\Classes\Log;
use Monolog\Logger;

/**
 * Centralized payment logging service
 * 
 * Provides unified logging for all payment operations with contextual prefixes
 * to maintain a chronological audit trail across all payment components.
 */
class PaymentLogger
{
    private static ?PaymentLogger $instance = null;
    private ?Logger $logger = null;

    private function __construct()
    {}

    /**
     * Get singleton instance
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get the underlying Monolog logger instance
     */
    public function getLogger(): Logger
    {
        if ($this->logger === null) {
            $this->logger = Log::create('payments', [
                'file' => 'payments.log',
                'app_processor' => function (\App\Classes\Log\AppProcessor $processor) {
                    return $processor->withInput(false);
                }
            ]);
            
            // Add custom processor to clean up AppProcessor data
            $this->logger->pushProcessor([$this, 'filterAppProcessorData']);
        }
        return $this->logger;
    }

    /**
     * Create a contextual logger that prefixes all messages with the given context
     */
    public function withContext(string $context): ContextualPaymentLogger
    {
        return new ContextualPaymentLogger($this->getLogger(), $context);
    }

    /**
     * Log debug message with context
     */
    public function debug(string $context, string $message, array $data = []): void
    {
        $this->getLogger()->debug("[$context] $message", $this->sanitizeData($data));
    }

    /**
     * Log info message with context
     */
    public function info(string $context, string $message, array $data = []): void
    {
        $this->getLogger()->info("[$context] $message", $this->sanitizeData($data));
    }

    /**
     * Log warning message with context
     */
    public function warning(string $context, string $message, array $data = []): void
    {
        $this->getLogger()->warning("[$context] $message", $this->sanitizeData($data));
    }

    /**
     * Log error message with context
     */
    public function error(string $context, string $message, array $data = []): void
    {
        $this->getLogger()->error("[$context] $message", $this->sanitizeData($data));
    }

    /**
     * Filter AppProcessor data to keep only url and method
     */
    public function filterAppProcessorData(array $record): array
    {
        if (isset($record['extra']['request'])) {
            // Keep only url and method from request data
            $filteredRequest = [];
            if (isset($record['extra']['request']['url'])) {
                $filteredRequest['url'] = $record['extra']['request']['url'];
            }
            if (isset($record['extra']['request']['method'])) {
                $filteredRequest['method'] = $record['extra']['request']['method'];
            }
            $record['extra']['request'] = $filteredRequest;
        }
        
        // Remove auth data entirely
        if (isset($record['extra']['auth'])) {
            unset($record['extra']['auth']);
        }
        
        return $record;
    }

    /**
     * Sanitize sensitive data from log entries
     */
    public function sanitizeData(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeData($value);
                continue;
            }
            
            if (!is_string($value) && !is_numeric($value)) {
                $sanitized[$key] = $value;
                continue;
            }
            
            $value = (string) $value;
            
            // Remove sensitive fields entirely
            if (in_array(strtolower($key), [
                'cvv', 'cvv2', 'cvc', 'cid',
                'recaptcha_token', 'g-recaptcha-response',
                'password', 'token', 'secret',
                'user_agent', 'user-agent',
                'headers', 'request', 'response'
            ])) {
                continue; // Skip logging these fields
            }
            
            // Mask card numbers (keep only last 4)
            if (in_array(strtolower($key), ['account', 'cardnumber', 'card_number', 'pan'])) {
                if (strlen($value) > 4) {
                    $sanitized[$key] = str_repeat('*', strlen($value) - 4) . substr($value, -4);
                } else {
                    $sanitized[$key] = $value;
                }
                continue;
            }
            
            // Mask emails
            if (in_array(strtolower($key), ['email', 'email_address'])) {
                $sanitized[$key] = $this->maskEmail($value);
                continue;
            }
            
            // Mask phones
            if (in_array(strtolower($key), ['phone', 'phone_number'])) {
                $sanitized[$key] = $this->maskPhone($value);
                continue;
            }
            
            $sanitized[$key] = $value;
        }
        
        return $sanitized;
    }

    /**
     * Mask email address
     */
    private function maskEmail(string $email): string
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $email; // Return as-is if not a valid email
        }
        
        [$user, $domain] = explode('@', $email);
        
        if (strlen($user) <= 1) {
            return $user . '***@' . $domain;
        }
        
        return substr($user, 0, 1) . '***@' . $domain;
    }

    /**
     * Mask phone number
     */
    private function maskPhone(string $phone): string
    {
        // Extract just the digits
        $digits = preg_replace('/\D/', '', $phone);
        
        if (strlen($digits) < 4) {
            return '***'; // Too short to safely mask
        }
        
        // Keep last 4 digits, mask the rest
        $lastFour = substr($digits, -4);
        
        // Try to preserve original formatting if possible
        if (preg_match('/^\((\d{3})\)\s*(\d{3})-(\d{4})$/', $phone)) {
            return "(***) ***-$lastFour";
        } elseif (preg_match('/^(\d{3})-(\d{3})-(\d{4})$/', $phone)) {
            return "***-***-$lastFour";
        } elseif (preg_match('/^(\d{3})\.(\d{3})\.(\d{4})$/', $phone)) {
            return "***.***.$lastFour";
        } else {
            // Default format
            return "***-***-$lastFour";
        }
    }
}
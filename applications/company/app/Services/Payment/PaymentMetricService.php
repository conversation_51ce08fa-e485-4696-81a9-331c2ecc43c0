<?php

declare(strict_types=1);

namespace App\Services\Payment;

use Common\Models\PropelrMerchant;
use Common\Models\PropelrTransaction;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\DB\StaticAccessors\DB;
use DateTime;
use DateTimeZone;
use Exception;

class PaymentMetricService {

    /**
     * Get the payment transactions' metrics.
     *
     * @param string $start_date
     * @param string $end_date
     * @return array
     * @throws Exception
     */
    public function getPaymentTransactionsMetrics(string $start_date, string $end_date): array
    {
        $start_date = new DateTime($start_date, new DateTimeZone('UTC'));
        $end_date = new DateTime($end_date, new DateTimeZone('UTC'));

        $user = Auth::user();

        $merchant = PropelrMerchant::where('companyUUID', $user->company->companyUUID)
            ->whereNull('deletedAt')
            ->first();

        if (!$merchant) {
            return [
                'total_captured_amount' => 0,
                'total_settled_amount' => 0,
                'total_funded_amount' => 0,
                'total_credit_card_amount' => 0,
                'total_ach_amount' => 0
            ];
        }

        return $this->getTransactionsMetrics($start_date, $end_date);
    }

    /**
     * Get the transactions' metrics.
     *
     * @param DateTime $start_date
     * @param DateTime $end_date
     * @return array
     */
    private function getTransactionsMetrics(DateTime $start_date, DateTime $end_date): array
    {
        $user = Auth::user();
        $query = PropelrTransaction::query()
            ->join(
                'propelrMerchants',
                'propelrMerchants.propelrMerchantID',
                '=',
                'propelrTransactions.propelrMerchantID'
            )
            ->where('propelrMerchants.companyUUID', $user->company->companyUUID)
            ->whereNull('propelrTransactions.deletedAt');

        $all_time_metrics = $this->calculateAllTimeMetrics($query);
        $transactions = $this->getFilteredTransactions($query, [$start_date, $end_date]);

        return $this->calculateTransactionMetrics($transactions, $all_time_metrics);
    }

    /**
     * Calculate all-time metrics.
     */
    private function calculateAllTimeMetrics($query)
    {
        return $query->clone()
            ->select(
                DB::raw("
                     SUM(
                            CASE 
                                WHEN (
                                    propelrTransactions.status = " . PropelrTransaction::STATUS_CAPTURED . "
                                    AND propelrTransactions.voidedAt IS NULL
                                    AND propelrTransactions.refundedAt IS NULL
                                )
                                THEN totalAmount 
                                ELSE 0 
                            END
                     ) AS total_captured_amount
                    "),
                DB::raw("
                        SUM(
                            CASE 
                                WHEN (
                                    propelrTransactions.status = " . PropelrTransaction::STATUS_SETTLED . "
                                    AND propelrTransactions.voidedAt IS NULL
                                    AND propelrTransactions.refundedAt IS NULL
                                )
                                THEN totalAmount 
                                ELSE 0 
                            END
                        ) AS total_settled_amount
                    ")
            )
            ->first();
    }

    /**
     * Get filtered transactions within the date range.
     */
    private function getFilteredTransactions($query, array $createdAtRange)
    {
        return $query->clone()
            ->select('propelrTransactions.*', 'propelrMerchants.status as merchant_status')
            ->whereBetween('propelrTransactions.createdAt', $createdAtRange)
            ->get();
    }

    /**
     * Calculate transaction metrics.
     */
    private function calculateTransactionMetrics($transactions, $all_time_metrics): array
    {
        $total_funded_amount = 0;
        $total_credit_card_amount = 0;
        $total_ach_amount = 0;

        foreach ($transactions as $transaction) {
            // Skip voided or refunded transactions
            if ($transaction->voidedAt !== null || $transaction->refundedAt !== null) {
                continue;
            }

            $is_funded = $transaction->status === PropelrTransaction::STATUS_FUNDED &&
                        $transaction->fundedAt !== null;

            if ($is_funded) {
                $total_funded_amount += $transaction->totalAmount;
            }

            if ($is_funded) {
                if ($transaction->type === PropelrTransaction::TRANSACTION_TYPE_CREDIT_CARD) {
                    $total_credit_card_amount += $transaction->totalAmount;
                } elseif ($transaction->type === PropelrTransaction::TRANSACTION_TYPE_ACH) {
                    $total_ach_amount += $transaction->totalAmount;
                }
            }
        }

        return [
            'total_captured_amount' => $all_time_metrics->total_captured_amount ?? 0,
            'total_settled_amount' => $all_time_metrics->total_settled_amount ?? 0,
            'total_funded_amount' => $total_funded_amount,
            'total_credit_card_amount' => $total_credit_card_amount,
            'total_ach_amount' => $total_ach_amount
        ];
    }
}
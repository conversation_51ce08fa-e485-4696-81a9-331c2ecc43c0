<?php

declare(strict_types=1);

namespace App\Services\Payment\Propelr\Api;

use App\Services\Payment\Exceptions\GatewayTimeoutException;
use App\Services\Payment\Propelr\DTOs\TransactionDTO;
use Common\Models\PropelrMerchant;
use Common\Models\PropelrTransaction;
use Exception;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use Ramsey\Uuid\Uuid;
use Carbon\Carbon;

class CardConnectApiService
{
    /**
     * Auth + capture in one call, then persist.
     */
    public function charge(TransactionDTO $dto): PropelrTransaction
    {
        $payload = $dto->toGatewayPayload();
        $merchant = $dto->merchant;

        try {
            $client = CardConnectApiHelper::getHttpClient(
                $merchant->gatewayUsername,
                $merchant->gatewayPasswordEnc
            );

            $response = $client->request('PUT', CardConnectApiHelper::$AUTH_ENDPOINT, ['json' => $payload]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code: {$response->getStatusCode()}");
            }

            $transaction = $this->persist($dto, $body);
            return $transaction;

        } catch (ConnectException $e) {
            throw new GatewayTimeoutException();
        } catch (Exception|GuzzleException $e) {
            CardConnectApiHelper::handleApiException($e, $payload, [
                'cc_mid' => $merchant->ccMID, 
                'ach_mid' => $merchant->achMID
            ]);
        }
    }

    /** Map gateway response → proplrTransactions row */
    protected function persist(TransactionDTO $dto, array $body): PropelrTransaction
    {
        // Build metadata from DTO properties
        $metadata = [];
        if ($dto->payment_link_id) {
            $metadata['payment_link_id'] = $dto->payment_link_id;
        }
        if ($dto->invoice_number) {
            $metadata['invoice_number'] = $dto->invoice_number;
        }
        
        // Merge any additional metadata from the DTO
        if ($dto->metadata) {
            $metadata = array_merge($metadata, $dto->metadata);
        }

        $payload = [
            'propelrTransactionID'   => Uuid::uuid4()->getBytes(),
            'propelrMerchantID'      => $dto->merchant->propelrMerchantID,

            'retref'          => $body['retref'],
            'totalAmount'     => $body['amount'],
            'authCode'        => $body['authcode'] ?? null,
            'gatewayResponseCode'    => $body['respcode'] ?? null,
            'gatewayResponseMessage' => $body['resptext'] ?? null,
            'cardResponseCode'       => $body['assocRespCode'] ?? null,
            'cardResponseMessage'    => $body['assocRespText'] ?? null,
            'last4'           => substr($body['token'] ?? $body['account'] ?? '', -4),
            'expMonth'        => substr($body['expiry'] ?? '', 0, 2),
            'expYear'         => substr($body['expiry'] ?? '', 2, 2) ? ('20'.substr($body['expiry'],2,2)) : null,
            'routingNumber'   => $dto->transaction_type === PropelrTransaction::TRANSACTION_TYPE_ACH 
                                ? ($dto->payment_data['bankaba'] ?? null) 
                                : null,
            'paymentToken'    => $body['token'] ?? null,
            'respStat'        => $body['respstat'] ?? null,
            'avsResp'         => $body['avsresp']  ?? null,
            'cvvResp'         => $body['cvvresp']  ?? null,
            'entryMode'       => $body['entrymode'] ?? null,
            'setlStat'        => $body['setlstat']  ?? null,
            'batchID'         => $body['batchid']   ?? null,
            'processorCode'   => $body['respproc']  ?? null,
            'commCard'        => $body['commcard']  ?? null,
            'binType'         => $body['bintype']   ?? null,
            'capturedAt'      => Carbon::now(),

            'orderID'         => $dto->order_id,
            'baseAmount'      => $dto->base_amount,
            'creditCardProcessingFee'  => $dto->credit_card_processing_fee,
            'type'            => $dto->transaction_type,
            'achAccountType'  => $dto->ach_account_type,
            'cardBrand'       => $dto->transaction_type === PropelrTransaction::TRANSACTION_TYPE_CREDIT_CARD 
                                ? $dto->card_brand 
                                : null,
            'rawGatewayJSON'  => json_encode($body),
            'metadata'        => !empty($metadata) ? json_encode($metadata) : null,
            
            // Customer information fields
            'customerFirstName' => $dto->customer_first_name,
            'customerLastName'  => $dto->customer_last_name,
            'customerEmail'     => $dto->email,
            'customerPhone'     => $dto->phone,
        ];


        $transaction = PropelrTransaction::create($payload);
        return $transaction;
    }

    /**
     * Void a previously authorized transaction
     */
    public function void(string $merchid, string $retref, ?float $amount = null): array
    {
        $payload = [
            'merchid' => $merchid,
            'retref' => $retref
        ];

        if ($amount !== null) {
            $payload['amount'] = number_format($amount, 2, '.', '');
        }

        try {
            $merchant = PropelrMerchant::where('ccMID', $merchid)
                ->orWhere('achMID', $merchid)
                ->first();
            if (!$merchant) {
                throw new Exception("Merchant not found for MID: {$merchid}");
            }

            $client = CardConnectApiHelper::getHttpClient(
                $merchant->gatewayUsername,
                $merchant->gatewayPasswordEnc
            );

            $response = $client->request('PUT', CardConnectApiHelper::$VOID_ENDPOINT, ['json' => $payload]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code: {$response->getStatusCode()}");
            }

            CardConnectApiHelper::getLog()->info('Void request completed', [
                'retref' => $payload['retref'],
                'status_code' => $response->getStatusCode(),
                'response_status' => $body['respstat'] ?? 'unknown'
            ]);

            return $body;

        } catch (Exception|GuzzleException $e) {
            CardConnectApiHelper::handleApiException($e, $payload, ['mid' => $merchid]);
            throw $e;
        }
    }

    /**
     * Refund a previously settled transaction
     */
    public function refund(string $merchid, string $retref, float $amount): array
    {
        $payload = [
            'merchid' => $merchid,
            'retref' => $retref,
            'amount' => number_format($amount, 2, '.', '')
        ];

        try {
            $merchant = PropelrMerchant::where('ccMID', $merchid)
                ->orWhere('achMID', $merchid)
                ->first();
            if (!$merchant) {
                throw new Exception("Merchant not found for MID: {$merchid}");
            }

            $client = CardConnectApiHelper::getHttpClient(
                $merchant->gatewayUsername,
                $merchant->gatewayPasswordEnc
            );

            $response = $client->request('POST', CardConnectApiHelper::$REFUND_ENDPOINT, ['json' => $payload]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code: {$response->getStatusCode()}");
            }

            CardConnectApiHelper::getLog()->info('Refund request completed', [
                'retref' => $payload['retref'],
                'amount' => $payload['amount'],
                'status_code' => $response->getStatusCode(),
                'response_status' => $body['respstat'] ?? 'unknown'
            ]);

            return $body;

        } catch (Exception|GuzzleException $e) {
            CardConnectApiHelper::handleApiException($e, $payload, ['mid' => $merchid]);
            throw $e; // Re-throw to be handled by calling service
        }
    }

    /**
     * Inquire about transaction status
     */
    public function inquire(string $merchid, string $retref): array
    {
        try {
            $merchant = PropelrMerchant::where('ccMID', $merchid)
                ->orWhere('achMID', $merchid)
                ->first();
            if (!$merchant) {
                throw new Exception("Merchant not found for MID: {$merchid}");
            }

            $client = CardConnectApiHelper::getHttpClient(
                $merchant->gatewayUsername,
                $merchant->gatewayPasswordEnc
            );

            $url = CardConnectApiHelper::$INQUIRE_ENDPOINT . "/{$retref}/{$merchid}";
            $response = $client->request('GET', $url);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code: {$response->getStatusCode()}");
            }

            CardConnectApiHelper::getLog()->info('Inquire request completed', [
                'retref' => $retref,
                'status_code' => $response->getStatusCode(),
                'response_status' => $body['respstat'] ?? 'unknown'
            ]);

            return $body;

        } catch (Exception|GuzzleException $e) {
            CardConnectApiHelper::handleApiException($e, ['merchid' => $merchid, 'retref' => $retref], ['endpoint' => 'inquire']);
            throw $e;
        }
    }

    /**
     * Get settlement status for a specific date
     * date must be in MMDD format.
     */
    public function settlestat(string $merchid, string $date): array
    {
        try {
            $merchant = PropelrMerchant::where('ccMID', $merchid)
                ->orWhere('achMID', $merchid)
                ->first();
            if (!$merchant) {
                throw new Exception("Merchant not found for MID: {$merchid}");
            }

            $client = CardConnectApiHelper::getHttpClient(
                $merchant->gatewayUsername,
                $merchant->gatewayPasswordEnc
            );

            $params = [ 'merchid' => $merchid, 'date' => $date ];
            $response = $client->request('GET', CardConnectApiHelper::$SETTLESTAT_ENDPOINT, [
                'query' => $params
            ]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code: {$response->getStatusCode()}");
            }

            // Response can be an array of batches or non-array
            if (is_array($body)) {
                $batches = $body;
            } else {
                $batches = [];
            }

            CardConnectApiHelper::getLog()->info('Settlestat request completed', [
                'params' => $params,
                'batch_count' => count($batches),
                'status_code' => $response->getStatusCode()
            ]);

            return $batches;

        } catch (Exception|GuzzleException $e) {
            CardConnectApiHelper::handleApiException($e, ['merchid' => $merchid, 'date' => $date], ['endpoint' => 'settlestat']);
            throw $e;
        }
    }

    /**
     * Get funding information for a specific date with pagination support
     */
    public function funding(string $merchid, string $date, ?int $page = null): array
    {
        try {
            $merchant = PropelrMerchant::where('ccMID', $merchid)
                ->orWhere('achMID', $merchid)
                ->first();
            if (!$merchant) {
                throw new Exception("Merchant not found for MID: {$merchid}");
            }

            $client = CardConnectApiHelper::getHttpClient(
                $merchant->gatewayUsername,
                $merchant->gatewayPasswordEnc
            );

            // Date format should be YYYY-MM-DD
            $params = [
                'merchid' => $merchid,
                'date' => $date
            ];

            if ($page !== null) {
                $params['page'] = $page;
            }

            $response = $client->request('GET', CardConnectApiHelper::$FUNDING_ENDPOINT, [
                'query' => $params
            ]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code: {$response->getStatusCode()}");
            }

            CardConnectApiHelper::getLog()->info('Funding request completed', [
                'params' => $params,
                'has_more' => $body['hasMore'] ?? false,
                'funding_count' => isset($body['fundings']) ? count($body['fundings']) : 0,
                'status_code' => $response->getStatusCode()
            ]);

            return $body;

        } catch (Exception|GuzzleException $e) {
            CardConnectApiHelper::handleApiException($e, ['merchid' => $merchid, 'date' => $date, 'page' => $page], ['endpoint' => 'funding']);
            throw $e;
        }
    }
}
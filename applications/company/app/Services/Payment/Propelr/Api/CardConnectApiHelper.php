<?php

declare(strict_types=1);

namespace App\Services\Payment\Propelr\Api;

use App\Classes\Log;
use App\Services\Payment\PaymentLogger;
use App\Services\Payment\ContextualPaymentLogger;
use Core\StaticAccessors\Config;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\{ClientException,ServerException};
use Monolog\Logger;
use Exception;

class CardConnectApiHelper
{

    /** @var Client[] keyed by md5(username:password) */
    private static array $clients = [];
    protected static ?ContextualPaymentLogger $logger  = null;

    public static string $AUTH_ENDPOINT = '/cardconnect/rest/auth';
    public static string $VOID_ENDPOINT = '/cardconnect/rest/void';
    public static string $REFUND_ENDPOINT = '/cardconnect/rest/refund';
    public static string $INQUIRE_ENDPOINT = '/cardconnect/rest/inquire';
    public static string $SETTLESTAT_ENDPOINT = '/cardconnect/rest/settlestat';
    public static string $FUNDING_ENDPOINT = '/cardconnect/rest/funding';
    public static string $TOKENIZE_ENDPOINT = '/cardsecure/api/v1/ccn/tokenize';


    /** Build the Guzzle client for CardConnect merchant */
    public static function getHttpClient(string $username, string $password): Client
    {
        $key = md5("$username:$password");
        if (!isset(self::$clients[$key])) {
            self::$clients[$key] = new Client([
                'base_uri' => Config::get('payments.card_connect.gateway_url'),
                'headers'  => [
                    'Authorization' => "Basic " . base64_encode("$username:$password"),
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ],
                'timeout'  => 10,
            ]);
        }
        return self::$clients[$key];
    }

    /** Uniform error handling (Sentry + log)
     *
     * @throws Exception
     */
    public static function handleApiException(Exception $e, array $payload = [], array $context = []): void
    {
        $extra = [
            'payload' => $payload,
            'context' => $context,
            'msg' => $e->getMessage()
        ];

        // Sanitize payload for logging but keep original for Sentry
        $sanitizedExtra = PaymentLogger::getInstance()->sanitizeData($extra);

        \Sentry\withScope(function ($scope) use ($extra, $e) {
            $scope->setExtras($extra);
            \Sentry\captureException($e);
        });

        self::getLog()->error('CardConnect API error', $sanitizedExtra);
        throw $e;
    }

    public static function getLog(): ContextualPaymentLogger
    {
        return self::$logger ??= PaymentLogger::getInstance()->withContext('API');
    }
}
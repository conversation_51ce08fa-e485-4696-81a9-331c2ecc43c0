<?php

declare(strict_types=1);

namespace App\Services\Payment\Propelr\Api;

use GuzzleHttp\Exception\GuzzleException;
use Exception;

class CardSecureTokenizationService
{
    public function tokenizeCreditCard(string $pan, $merchant): string
    {
        $payload = [ 'account' => $pan ];

        return $this->tokenize($payload, $merchant);
    }

    public function tokenizeAchAccount(string $routing, string $account, $merchant): string
    {
        $payload = [ 'account' => "{$routing}/{$account}" ];

        return $this->tokenize($payload, $merchant);
    }

    private function tokenize(array $payload, $merchant): string
    {
        try {
            $client = CardConnectApiHelper::getHttpClient($merchant->gatewayUsername, $merchant->gatewayPasswordEnc);
            
            $response = $client->request('POST', CardConnectApiHelper::$TOKENIZE_ENDPOINT, [ 'json' => $payload ]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                throw new Exception("Unexpected status code from CardSecure: {$response->getStatusCode()}");
            }

            if (empty($body['token'])) {
                throw new Exception("No token returned from CardSecure: " . json_encode($body));
            }

            CardConnectApiHelper::getLog()->info('CardSecure tokenization successful', [
                'token_prefix' => substr($body['token'], 0, 8) . '...',
                'account_prefix' => substr($payload['account'], 0, 6) . '...'
            ]);

            return $body['token'];

        } catch (Exception|GuzzleException $e) {
            CardConnectApiHelper::handleApiException($e, $payload);
            throw $e;
        }
    }
}
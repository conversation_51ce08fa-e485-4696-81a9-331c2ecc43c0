<?php

declare(strict_types=1);

namespace App\Services\Payment\Propelr\DTOs;

use Common\Models\Company;
use Common\Models\PaymentLink;
use Common\Models\PropelrMerchant;

class PaymentRequestDTO
{
    public function __construct(
        public PaymentLink $paymentLink,
        public Company $company,
        public PropelrMerchant $merchant,
        public array $validatedData,
        public array $metadata
    ) {}

    /**
     * Get payment link ID as hex string for storage
     *
     * @return string
     */
    public function getPaymentLinkId(): string
    {
        return bin2hex($this->paymentLink->paymentLinkID);
    }

    /**
     * Prepare validated data with metadata for payment processing
     *
     * @return array
     */
    public function getPaymentData(): array
    {
        $data = $this->validatedData;
        $data['payment_link_id'] = $this->getPaymentLinkId();
        
        if (!empty($this->metadata)) {
            $data['metadata'] = $this->metadata;
        }
        
        return $data;
    }

    /**
     * Get transaction context data for logging
     *
     * @return array
     */
    public function getLoggingContext(): array
    {
        return [
            'payment_link_id' => $this->getPaymentLinkId(),
            'merchant_id' => bin2hex($this->merchant->propelrMerchantID),
            'payment_amount' => $this->validatedData['payment_amount'],
            'credit_card_processing_fee' => $this->validatedData['credit_card_processing_fee'],
            'total_amount' => $this->validatedData['total_amount'],
        ];
    }
}
<?php
namespace App\Services\Payment\Propelr\DTOs;

use Common\Models\PropelrMerchant;
use Common\Models\PropelrTransaction;
use Ramsey\Uuid\Uuid;

class TransactionDTO
{
    public function __construct(
        public PropelrMerchant $merchant,
        public string         $amount,
        public array          $payment_data,

        public ?string        $currency = "USD",
        public ?string        $order_id = null,
        public ?string        $customer_name = null,
        public ?string        $email = null,
        public ?string        $phone = null,
        public ?float         $base_amount = null,
        public ?float         $credit_card_processing_fee = null,
        public ?int           $transaction_type = null,
        public ?int           $ach_account_type = null,
        public ?string        $payment_link_id = null,
        public ?string        $invoice_number = null,
        public ?string        $customer_first_name = null,
        public ?string        $customer_last_name = null,
        public ?array         $metadata = null,
        public ?string        $card_brand = null,
    ) {}

    public function toGatewayPayload(): array
    {
        $is_ach = $this->transaction_type === \Common\Models\PropelrTransaction::TRANSACTION_TYPE_ACH;
        
        if ($is_ach) {
            $base = [
                'merchid'      => $this->merchant->achMID,
                'account'      => $this->payment_data['account'] ?? '',
                'bankaba'      => $this->payment_data['bankaba'] ?? '',
                'accttype'     => $this->getAchAccountTypeString(),
                'amount'       => $this->amount,
                'currency'     => 'USD',
                'name'         => $this->customer_name,
                'ecomind'      => 'E',
                'achEntryCode' => 'WEB',
                'address'      => $this->payment_data['address'] ?? '',
                'city'         => $this->payment_data['city'] ?? '',
                'region'       => $this->payment_data['region'] ?? '',
                'postal'       => $this->payment_data['postal'] ?? '',
                'country'      => 'US',
                'email'        => $this->email,
                'phone'        => $this->formatPhoneForAch($this->phone),
                'orderid'      => $this->order_id,
            ];
        } else {
            $base = [
                'ecomind'   => 'E',
                'capture'   => 'Y',
                'merchid'   => $this->merchant->ccMID,
                'amount'    => $this->amount,
                'currency'  => $this->currency,
                'orderid'   => $this->order_id,
                'name'      => $this->customer_name,
                'email'     => $this->email,
                'phone'     => $this->phone,
            ];
            
            // Merge payment data (account, expiry, cvv2, etc.)
            $base = array_merge($base, $this->payment_data);
        }

        return array_filter($base, fn($v) => $v !== null && $v !== '');
    }

    /**
     * Convert ACH account type integer to gateway string format
     */
    private function getAchAccountTypeString(): string
    {
        return $this->ach_account_type === PropelrTransaction::ACH_ACCOUNT_TYPE_SAVINGS
            ? 'ESAV' 
            : 'ECHK';
    }

    /**
     * Format phone number for ACH (digits only, no formatting)
     */
    private function formatPhoneForAch(?string $phone): ?string
    {
        if (!$phone) {
            return null;
        }
        
        // Strip all non-digit characters
        return preg_replace('/\D/', '', $phone);
    }

    /**
     * Generate logging context array with all relevant transaction information
     *
     * @return array
     */
    public function getLoggingContext(): array
    {
        $payment_method = $this->transaction_type === PropelrTransaction::TRANSACTION_TYPE_ACH ? 'ACH' : 'Credit Card';
        
        return array_filter([
            'merchant_id' => bin2hex($this->merchant->propelrMerchantID),
            'order_id' => $this->order_id,
            'payment_method' => $payment_method,
            'transaction_type' => $this->transaction_type,
            'payment_amount' => $this->base_amount,
            'credit_card_processing_fee' => $this->credit_card_processing_fee,
            'total_amount' => $this->amount,
            'customer_email' => $this->email,
            'invoice_number' => $this->invoice_number,
            'card_brand' => $this->card_brand,
        ], fn($value) => $value !== null);
    }
}
<?php

declare(strict_types=1);

namespace App\Services\Payment\Propelr\Handlers;

use App\Services\Payment\Exceptions\DuplicatePaymentException;
use App\Services\Payment\Exceptions\PaymentDeclinedException;
use App\Services\Payment\PaymentLinkService;
use App\Services\Payment\PaymentMetadataService;
use App\Services\Payment\Propelr\DTOs\PaymentRequestDTO;
use App\Services\Payment\PropelrPaymentService;
use Common\Models\Company;
use Common\Models\PaymentLink;
use Common\Models\PropelrTransaction;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Exceptions\AppException;

class PaymentProcessingHandler
{
    private PaymentLinkService $payment_link_service;
    private PropelrPaymentService $payment_service;
    private PaymentMetadataService $metadata_service;

    public function __construct()
    {
        $this->payment_link_service = new PaymentLinkService();
        $this->payment_service = new PropelrPaymentService();
        $this->metadata_service = new PaymentMetadataService();
    }

    /**
     * Prepare and validate payment request data
     *
     * @param array $input Form input data
     * @param string $shortToken Payment link token
     * @return PaymentRequestDTO
     * @throws HttpResponseException
     * @throws AppException
     */
    public function preparePaymentRequest(array $input, string $short_token): PaymentRequestDTO
    {
        // Validate and get payment link
        $payment_link = $this->validateAndGetPaymentLink($short_token);
        
        // Validate and get company
        $company = $this->validateAndGetCompany($payment_link->merchant);
        
        // Authorize feature access
        $this->payment_service->authorizeFeatureAccess($company);
        
        // Extract and enrich metadata
        $metadata = $this->metadata_service->extractAndEnrichMetadata($input, $payment_link);
        
        // Validate payment input
        $validated_data = $this->payment_service->validatePaymentInput($input, $company);
        
        return new PaymentRequestDTO(
            paymentLink: $payment_link,
            company: $company,
            merchant: $payment_link->merchant,
            validatedData: $validated_data,
            metadata: $metadata
        );
    }

    /**
     * Process payment and prepare response data
     *
     * @param PaymentRequestDTO $paymentRequest
     * @return array Response data
     * @throws PaymentDeclinedException| DuplicatePaymentException
     */
    public function processPaymentAndGetResponse(PaymentRequestDTO $payment_request): array
    {
        $payment_data = $payment_request->getPaymentData();
        $transaction = $this->payment_service->processPayment($payment_request->merchant, $payment_data);

        // Map transaction status to response status
        $response_status = match($transaction->status) {
            PropelrTransaction::STATUS_CAPTURED => 'captured',
            PropelrTransaction::STATUS_AUTH_DECLINED => 'declined',
            PropelrTransaction::STATUS_RETRYABLE => 'retryable',
            PropelrTransaction::STATUS_ZERO_AMOUNT => 'zero_amount',
            default => 'unknown'
        };

        return [
            'success' => true,
            'transaction_id' => bin2hex($transaction->propelrTransactionID),
            'payment_link_id' => $payment_request->getPaymentLinkId(),
            'payment_amount' => $payment_request->validatedData['payment_amount'],
            'credit_card_processing_fee' => $payment_request->validatedData['credit_card_processing_fee'],
            'total_amount' => $payment_request->validatedData['total_amount'],
            'status' => $response_status,
            'gateway_response_code' => $transaction->gatewayResponseCode,
            'gateway_response_message' => $transaction->gatewayResponseMessage,
            'card_response_code' => $transaction->cardResponseCode,
            'card_response_message' => $transaction->cardResponseMessage,
            'logging_context' => array_merge($payment_request->getLoggingContext(), [
                'transaction_id' => bin2hex($transaction->propelrTransactionID),
                'status' => $response_status,
            ])
        ];
    }

    /**
     * Validate payment link and return if valid
     *
     * @param string $shortToken
     * @return PaymentLink
     * @throws HttpResponseException
     */
    private function validateAndGetPaymentLink(string $short_token): PaymentLink
    {
        $payment_link = $this->payment_link_service->findActiveByToken($short_token);

        if (!$payment_link) {
            throw new HttpResponseException(404, 'Payment link not found or expired.');
        }

        $validation = $this->payment_link_service->validateLinkForCheckout($payment_link);
        if (!$validation['valid']) {
            $error_messages = [
                'expired' => 'This payment link has expired.',
                'inactive' => 'This payment link is no longer active.',
                'merchant_inactive' => 'Merchant processing is temporarily unavailable.',
                'payment_processing_not_available' => 'Payment processing is temporarily unavailable.'
            ];

            $message = $error_messages[$validation['reason']] ?? 'Payment link is not available.';
            throw new HttpResponseException(410, $message);
        }

        return $payment_link;
    }

    /**
     * Validate and get company from merchant
     *
     * @param \Common\Models\PropelrMerchant $merchant
     * @return Company
     * @throws HttpResponseException
     */
    private function validateAndGetCompany($merchant): Company
    {
        $company = Company::where('companyUUID', $merchant->companyUUID)->first();

        if (!$company) {
            throw new HttpResponseException(404, 'Company not found.');
        }

        return $company;
    }
}
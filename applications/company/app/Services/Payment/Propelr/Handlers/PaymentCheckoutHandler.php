<?php

declare(strict_types=1);

namespace App\Services\Payment\Propelr\Handlers;

use App\Services\Payment\PaymentLinkService;
use App\Services\Payment\PropelrPaymentService;
use Common\Models\Company;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\URI;
use Core\Exceptions\AppException;

class PaymentCheckoutHandler
{
    private PaymentLinkService $payment_link_service;
    private PropelrPaymentService $payment_service;

    public function __construct()
    {
        $this->payment_link_service = new PaymentLinkService();
        $this->payment_service = new PropelrPaymentService();
    }

    /**
     * Prepare checkout data for rendering checkout page
     *
     * @param RequestInterface $request
     * @param string $shortToken
     * @return array
     * @throws HttpResponseException
     * @throws AppException
     */
    public function prepareCheckoutData(RequestInterface $request, string $shortToken): array
    {
        $payment_link = $this->validateAndGetPaymentLink($shortToken);
        
        $company = $this->validateAndGetCompany($payment_link->merchant);
        
        $this->payment_service->authorizeFeatureAccess($company);
        
        $query_params = $this->extractAndValidateQueryParams($request);

        $prefill_data = $this->getPrefillData($payment_link, $query_params);
        
        // Prepare payment data
        $action_url = URI::route('payments.link.charge', ['shortToken' => $shortToken])->build();
        $payment_data = $this->payment_service->getCheckoutPaymentData($company, $payment_link->merchant, $action_url);
        
        return array_merge($payment_data, [
            'prefill_data' => $prefill_data,
        ]);
    }

    /**
     * Validate payment link and return if valid
     *
     * @param string $shortToken
     * @return \Common\Models\PaymentLink
     * @throws HttpResponseException
     */
    private function validateAndGetPaymentLink(string $short_token)
    {
        $payment_link = $this->payment_link_service->findActiveByToken($short_token);

        if (!$payment_link) {
            throw new HttpResponseException(404, 'Payment link not found or expired.');
        }

        $validation = $this->payment_link_service->validateLinkForCheckout($payment_link);
        if (!$validation['valid']) {
            $error_messages = [
                'expired' => 'This payment link has expired.',
                'inactive' => 'This payment link is no longer active.',
                'merchant_inactive' => 'Merchant processing is temporarily unavailable.',
                'payment_processing_not_available' => 'Payment processing is temporarily unavailable.'
            ];

            $message = $error_messages[$validation['reason']] ?? 'Payment link is not available.';
            throw new HttpResponseException(410, $message);
        }

        return $payment_link;
    }

    /**
     * Validate and get company from merchant
     *
     * @param \Common\Models\PropelrMerchant $merchant
     * @return Company
     * @throws HttpResponseException
     */
    private function validateAndGetCompany($merchant): Company
    {
        $company = Company::where('companyUUID', $merchant->companyUUID)->first();

        if (!$company) {
            throw new HttpResponseException(404, 'Company not found.');
        }

        return $company;
    }

    /**
     * Extract and validate query parameters
     *
     * @param RequestInterface $request
     * @return array|null
     * @throws HttpResponseException
     */
    private function extractAndValidateQueryParams(RequestInterface $request): ?array
    {
        $invoice_type = $request->get('invoice_type');
        $bid_id = $request->get('bid_id');
        $invoice_order = $request->get('invoice_order');
        
        // Return null if no parameters provided
        if (!$bid_id || !$invoice_type) {
            return null;
        }
        
        if (!in_array($invoice_type, ['1', '2', '3', '4'])) {
            throw new HttpResponseException(400, 'Invalid invoice type. Must be 1-4.');
        }

        // todo - filipe - double check with @amanda this
        // For type 3, ensure invoice_order is provided
        if ($invoice_type == '3' && !$invoice_order) {
            throw new HttpResponseException(400, 'Invoice order required for middle invoice type.');
        }
        
        return [
            'invoice_type' => $invoice_type,
            'bid_id' => $bid_id,
            'invoice_order' => $invoice_order
        ];
    }

    /**
     * Get prefill data if parameters provided
     *
     * @param \Common\Models\PaymentLink $paymentLink
     * @param array|null $queryParams
     * @return array|null
     */
    private function getPrefillData($payment_link, ?array $query_params): ?array
    {
        if (!$query_params) {
            return null;
        }
        
        return $this->payment_link_service->getCheckoutPrefillData(
            $payment_link,
            (int)$query_params['invoice_type'],
            (int)$query_params['bid_id'],
            $query_params['invoice_order'] ? (int)$query_params['invoice_order'] : null
        );
    }
}
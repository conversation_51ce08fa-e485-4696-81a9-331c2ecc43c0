<?php

namespace app\Services\MySalesman\Exceptions;

use Exception;

/**
 * Exception class for invalid amounts in financial transactions.
 */
class MySalesmanException extends Exception
{
    /**
     * Construct the exception.
     *
     * @param string $message The Exception message to throw.
     * @param int $code The Exception code.
     * @param Exception|null $previous The previous throwable used for the exception chaining.
     */
    public function __construct($message = "", $code = 400, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

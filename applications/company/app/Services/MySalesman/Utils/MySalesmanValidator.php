<?php

namespace App\Services\MySalesman\Utils;

use App\Resources\LeadResource;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Validation;
use Core\Components\Validation\Classes\Validator;

class MySalesmanValidator
{

    /**
     * Validate lead data
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     * @throws ValidationException
     */
    public function lead($data): Validator
    {
        $config = FieldConfig::fromArray([
            'status' => [
                'label' => 'status',
                'rules' => 'required|type[int]|in_array[statuses]'
            ],
            'first_name' => [
                'label' => 'first_name',
                'rules' => 'required|trim|max_length[50]'
            ],
            'last_name' => [
                'label' => 'last_name',
                'rules' => 'required|trim|max_length[50]'
            ],
            'street_address1' => [
                'label' => 'street_address1',
                'rules' => 'required|trim|max_length[100]'
            ],
            'street_address2' => [
                'label' => 'street_address2',
                'rules' => 'trim|max_length[100]'
            ],
            'city' => [
                'label' => 'city',
                'rules' => 'required|trim|max_length[50]'
            ],
            'state' => [
                'label' => 'state',
                'rules' => 'required|trim|max_length[15]'
            ],
            'postal_code' => [
                'label' => 'postal_code',
                'rules' => 'required|trim|max_length[12]'
            ],
            'email' => [
                'label' => 'email',
                'rules' => 'required|trim|email|max_length[100]'
            ],
            'phone' => [
                'label' => 'phone',
                'rules' => 'required|trim|max_length[15]'
            ],
            'estimate' => [
                'label' => 'estimate',
                'rules' => 'required|trim|max_length[25]'
            ],
            'budget_drawing' => [
                'label' => 'budget_drawing',
                'rules' => 'required|trim|max_length[100]'
            ],
            'date_submitted' => [
                'label' => 'date_submitted',
                'rules' => 'required|date'
            ]
        ]);
        $config->store('statuses', array_keys(LeadResource::getStatusNames()));

        $validator = Validation::make()
            ->config($config)
            ->run($data);

        if (!$validator->valid()) {
            throw (new ValidationException('Input is not valid'))->setValidator($validator);
        }

        return $validator;
    }

    /**
     * Validate settings data
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     * @throws ValidationException
     */
    public function settings($data): Validator
    {
        $config = FieldConfig::fromArray([
            'my_salesman_integration_enabled' => [
                'label' => 'my_salesman_integration_enabled',
                'rules' => 'required|type[bool]'
            ],
            'my_salesman_capture_dropoffs' => [
                'label' => 'my_salesman_capture_dropoffs',
                'rules' => 'type[bool]'
            ],
            'my_salesman_default_marketing_source_lead' => [
                'label' => 'my_salesman_default_marketing_source_lead',
                'rules' => 'required|type[int]|in_array[marketing_types]'
            ],
        ]);
        $helper = new MySalesmanHelper();
        $marketing_types = $helper->getMarketingTypes(Auth::user());
        $config->store('marketing_types', array_keys($marketing_types));

        $validator = Validation::make()
            ->config($config)
            ->run($data);

        if (!$validator->valid()) {
            throw (new ValidationException('Input is not valid'))->setValidator($validator);
        }

        return $validator;
    }

}
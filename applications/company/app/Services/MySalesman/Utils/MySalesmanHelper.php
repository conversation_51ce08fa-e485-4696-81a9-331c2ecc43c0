<?php
namespace App\Services\MySalesman\Utils;

use App\Classes\Log;
use App\Services\CompanyFeatureService;
use app\Services\MySalesman\Exceptions\MySalesmanException;
use App\Services\UserSettingService;
use Common\Models\Feature;
use Common\Models\MarketingType;
use Common\Models\User;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Exceptions\AppException;
use Monolog\Logger;
use Exception;

class MySalesmanHelper
{

    const STATUS_SETTING = 'my_salesman_integration_enabled';
    const CAPTURE_DROP_OFFS_SETTING ='my_salesman_capture_dropoffs';
    const DEFAULT_MARKETING_SOURCE_LEAD_SETTING = 'my_salesman_default_marketing_source_lead';


    /**
     * @var Logger|null
     */
    protected ?Logger $logger = null;

    /**
     * Get integration setting
     *
     * @param $user
     * @return array|null
     * @throws MySalesmanException
     */
    public function getUserSettings($user): ?array
    {
        try {
            $setting_service = new UserSettingService($user->getKey());

            return [
                self::STATUS_SETTING =>
                    $setting_service->get(self::STATUS_SETTING, false),
                self::CAPTURE_DROP_OFFS_SETTING =>
                    $setting_service->get(self::CAPTURE_DROP_OFFS_SETTING, false),
                self::DEFAULT_MARKETING_SOURCE_LEAD_SETTING =>
                    $setting_service->get(self::DEFAULT_MARKETING_SOURCE_LEAD_SETTING, null)
            ];
        } catch (Exception $e) {
            throw new MySalesmanException('Error getting user settings', 500);
        }
    }

    /**
     * Get marketing types
     *
     * @param $user
     * @return array
     */
    public function getMarketingTypes($user)
    {
        $marketing_types = MarketingType::where('companyID', $user->companyID)
            ->whereNull('isDeleted')
            ->whereNotNull('parentMarketingTypeID')
            ->orderBy('parentMarketingTypeID', 'asc')
            ->get();

        $marketing = [];
        foreach ($marketing_types as $type) {
            $marketing[$type->marketingTypeID] = $type->marketingTypeName;
        }
        return $marketing;
    }

    /**
     * @throws AppException
     * @throws MySalesmanException
     */
    public function checkIntegrationFeatureEnabled(User $user)
    {
        $company_feature = new CompanyFeatureService($user->companyID);
        $is_feature_enabled = $company_feature->has(Feature::MY_SALESMAN);

        if (!$is_feature_enabled) {
            throw new MySalesmanException('MySalesman integration is not enabled for this company', 400);
        }
    }


    /**
     * Handle exceptions
     * @param \Throwable $e
     * @param string $message
     * @param mixed ...$context
     * @return JSONResponse
     */
    public function handleException(\Throwable $e, $message, ...$context): JSONResponse
    {
        $log_data = ['exception' => $e];

        foreach ($context as $key => $value) {
            if (is_string($key)) {
                $log_data[$key] = $value;
            } else {
                $log_data['context' . $key] = $value;
            }
        }

        $this->getLog()->error($message, $log_data);

        if ($e instanceof ValidationException) {
            return Response::json(['error' => $e->getErrors()], 422);
        }

        $error_code = $e instanceof MySalesmanException ? $e->getCode() : 500;
        $error_message = $e instanceof MySalesmanException ? $e->getMessage() : $message;

        return Response::json(['error' => $error_message], $error_code);
    }

    /**
     * Get logger
     *
     * @return Logger
     */
    protected function getLog(): Logger
    {
        if ($this->logger === null) {
            $this->logger = Log::create('mysalesman', [
                'file' => 'mysalesman.log',
            ]);
        }
        return $this->logger;
    }

}
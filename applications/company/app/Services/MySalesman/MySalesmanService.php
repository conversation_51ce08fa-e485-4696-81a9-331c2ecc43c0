<?php

declare(strict_types=1);

namespace App\Services\MySalesman;

use app\Services\MySalesman\Exceptions\MySalesmanException;
use App\Services\MySalesman\Utils\MySalesmanHelper;
use App\Services\MySalesman\Utils\MySalesmanValidator;
use App\Services\UserSettingService;
use Common\Models\Lead;

use Common\Models\MySalesmanCredential;
use Common\Models\User;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Resource\Exceptions\BatchHandleException;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;
use Carbon\Carbon;

class MySalesmanService
{

    public function __construct()
    {
        $this->validator = new MySalesmanValidator();
        $this->helper = new MySalesmanHelper();
    }


    /**
     * Get integration data
     * @throws MySalesmanException
     */
    public function getIntegrationData($user): array
    {
        $api_key = $this->getCredentialKey($user);
        $settings = $this->helper->getUserSettings($user);
        $marketing_types = $this->helper->getMarketingTypes($user);

        return [
            'is_customer_enabled' => $settings[$this->helper::STATUS_SETTING],
            'capture_drop_offs' => $settings[$this->helper::CAPTURE_DROP_OFFS_SETTING],
            'default_marketing_source_lead' => $settings[$this->helper::DEFAULT_MARKETING_SOURCE_LEAD_SETTING],
            'api_key' => $api_key,
            'marketing_types' => $marketing_types
        ];
    }


    /**
     * Get credential tokens
     *
     * @param $user
     * @return string|null
     */
    public function getCredentialKey($user): ?string
    {
        $credential = MySalesmanCredential::where('userID', $user->userID)
            ->where('isActive', 1)
            ->first();

        return $credential ? $credential->getApiKey() : null;
    }


    /**
     * @param $input
     * @return array
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     * @throws ValidationException
     * @throws MySalesmanException
     * @throws AppException
     */
    public function createLead($input): array
    {
        // @todo - Deal with capture drop off logic here - later on
        $user = Auth::user();
        $validator = $this->validator->lead($input);

        $notes = "Estimate: "
            . $validator->data('estimate')
            . " | Budged Drawing: "
            . $validator->data('budget_drawing')
            . " | Date Submitted: "
            . Carbon::parse($validator->data('date_submitted'))->format('Y-m-d');

        $setting_service = new UserSettingService($user->getKey());
        $marketing_type_id = $setting_service->get($this->helper::DEFAULT_MARKETING_SOURCE_LEAD_SETTING);

        if (!$marketing_type_id) {
            throw new MySalesmanException('Default marketing source lead is not set', 400);
        }

        $lead = Lead::create([
            'leadUUID' => random_bytes(16),
            'marketingTypeID' => $marketing_type_id,
            'status' => $validator->data('status'),
            'companyID' => $user->companyID,
            'email' => $validator->data('email'),
            'firstName' => $validator->data('first_name'),
            'lastName' => $validator->data('last_name'),
            'address' => $validator->data('street_address1'),
            'address2' => $validator->data('street_address2'),
            'city' => $validator->data('city'),
            'state' => $validator->data('state'),
            'zip' => $validator->data('postal_code'),
            'phoneNumber' => $validator->data('phone'),
            'notes'=> $notes,
        ]);

        // Filter the returned data after lead creation.
        return [
            'lead_uuid' => Uuid::fromBytes($lead->leadUUID)->toString(),
            'email' => $lead->email,
            'first_name' => $lead->firstName,
            'last_name' => $lead->lastName,
            'street_address1' => $lead->address,
            'street_address2' => $lead->address2,
            'city' => $lead->city,
            'state' => $lead->state,
            'postal_code' => $lead->zip,
            'phone' => $lead->phoneNumber,
            'notes' => $lead->notes,
            'status' => $lead->status,
        ];
    }


    /**
     * Update integration
     * @param $data
     * @return string|null
     *
     * @throws BatchHandleException
     * @throws MySalesmanException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     * @throws AppException
     */
    public function updateIntegration($data): ?string
    {
        $user = Auth::user();
        $validator = $this->validator->settings($data);
        $target = $validator->data($this->helper::STATUS_SETTING);

        $credential = $this->updateCredentialStatus($user, $target);

        // Enable the integration in user settings
        $settings = new UserSettingService($user->getKey());
        $this->updateUserSettings($settings, $validator);

        return $credential ? $credential->getApiKey() : null;
    }

    /**
     * Update the credential status
     *
     * @param User $user
     * @param bool $target
     * @return MySalesmanCredential|null
     */
    private function updateCredentialStatus(User $user, bool $target): ?MySalesmanCredential
    {
        $credential = MySalesmanCredential::where('userID', $user->userID)->first();

        if ($target) {
            if (!$credential || !$credential->isActive) {
                $credential = MySalesmanCredential::create(['userID' => $user->userID]);
            }
        } else {
            if ($credential) {
                $credential->deactivate();
                $credential = null;
            }
        }
        return $credential;
    }

    /**
     * Update user settings
     *
     * @param UserSettingService $settings
     * @param $validator
     * @throws AppException
     */
    private function updateUserSettings(UserSettingService $settings, $validator)
    {
        $settings->set($this->helper::STATUS_SETTING, $validator->data($this->helper::STATUS_SETTING));

        $drop_off_value = $validator->data($this->helper::CAPTURE_DROP_OFFS_SETTING);
        if ($drop_off_value !== null) {
            $settings->set($this->helper::CAPTURE_DROP_OFFS_SETTING, $drop_off_value);
        }

        $source_lead_value = $validator->data($this->helper::DEFAULT_MARKETING_SOURCE_LEAD_SETTING);
        if ($source_lead_value !== null) {
            $settings->set($this->helper::DEFAULT_MARKETING_SOURCE_LEAD_SETTING, $source_lead_value);
        }

        $settings->save();
    }
}
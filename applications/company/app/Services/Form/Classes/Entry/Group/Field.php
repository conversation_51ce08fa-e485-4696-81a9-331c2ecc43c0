<?php

declare(strict_types=1);

namespace App\Services\Form\Classes\Entry\Group;

use App\Services\Form\Classes\Structure\Group\Field as StructureField;
use App\Services\Form\Components\{EntryFields, Fields};
use App\Services\Form\Exceptions\FormException;
use App\Services\Form\Traits\ArrayImportExportTrait;
use Core\Components\Resource\Classes\Entity;

/**
 * Class Field
 *
 * @package App\Services\Form\Classes\Entry\Group
 */
abstract class Field
{
    use ArrayImportExportTrait;

    /**
     * @var string[] Mapping of types to their respective classes
     */
    protected static array $type_map = [
        Fields\Option\CheckboxField::class => EntryFields\OptionEntryField::class,
        Fields\Option\RadioField::class => EntryFields\OptionEntryField::class,
        Fields\Option\SelectField::class => EntryFields\OptionEntryField::class,
        Fields\FileField::class => EntryFields\FileEntryField::class,
        Fields\ProductListField::class => EntryFields\ProductEntryField::class,
        Fields\TextareaField::class => EntryFields\ValueEntryField::class,
        Fields\TextField::class => EntryFields\ValueEntryField::class
    ];

    /**
     * @var Item Parent item
     */
    protected Item $item;

    /**
     * @var StructureField Related structure field instance
     */
    protected StructureField $field;

    /**
     * Create new instance and assign to item
     *
     * Will find structure field based on field id and pass to constructor.
     *
     * @param Item $item
     * @param string $field_id
     * @param array $data
     * @return static
     * @throws FormException
     */
    public static function make(Item $item, string $field_id, array $data = [])
    {
        if (($field = $item->getContainer()->getStructureGroup()->getField($field_id)) === null) {
            throw new FormException('Unable to find field: %s', $field_id);
        }
        $class = static::class;
        $type = get_class($field);
        if (!isset(static::$type_map[$type])) {
            throw new FormException('Unable to find class for type: %s', $type);
        }
        if ($class !== static::$type_map[$type]) {
            $class = static::$type_map[$type];
        }
        $entry_field = new $class($item, $field, $data);
        $item->addField($entry_field);
        return $entry_field;
    }

    /**
     * Field constructor
     *
     * @param Item $item
     * @param StructureField $field
     * @param array $data
     */
    public function __construct(Item $item, StructureField $field, array $data = [])
    {
        $this->setItem($item);
        $this->field = $field;
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Clone field
     *
     * @param Item $item
     * @return $this
     */
    public function clone(Item $item): self
    {
        $field = clone $this;
        $field->setItem($item);
        return $field;
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     */
    public function hydrate(array $data): void
    {
        //
    }

    /**
     * Set item
     *
     * @param Item $item
     */
    public function setItem(Item $item): void
    {
        $this->item = $item;
    }

    /**
     * Get parent item
     *
     * @return Item
     */
    public function getItem(): Item
    {
        return $this->item;
    }

    /**
     * Get structure field
     *
     * If override is allowed and related structure field has an override, we use that.
     *
     * @param bool $allow_override
     * @return StructureField
     */
    public function getField(bool $allow_override = true): StructureField
    {
        return $allow_override && ($field = $this->field->getOverride()) !== null ? $field : $this->field;
    }

    /**
     * Validate field before saving
     */
    public function validate(): void
    {
        //
    }

    /**
     * Get field entity to persist
     *
     * @return Entity
     */
    public function getEntity(): Entity
    {
        $field = $this->getField();
        return Entity::make([
            'group_id' => $this->item->getID(),
            'field_source' => $field->getParent()->getFieldSource(),
            'field_id' => $field->getID()
        ]);
    }

    /**
     * Setup and configure field for use after loading from external source
     */
    public function setup(): void
    {
        //
    }

    /**
     * Convert into format useful for client side form library to consume
     *
     * @return array
     */
    abstract public function toClientFormat(): array;

    /**
     * Export to user friendly format useful to transferring info between companies or environments
     *
     * @return array
     */
    public function export(): array
    {
        $field = $this->getField();
        return [
            'field_id' => $field->getAlias() ?? $field->getID()
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Fields\Option;

use App\Resources\Form\Item\Group\FieldResource;
use App\Services\Form\Components\Fields\OptionField;
use Core\Components\Http\StaticAccessors\View;

/**
 * Class SelectField
 *
 * @package App\Services\Form\Components\Fields\Option
 */
class SelectField extends OptionField
{
    /**
     * @var int Field type
     */
    protected int $type = FieldResource::TYPE_SELECT;

    /**
     * Build options array
     *
     * @return array
     */
    protected function buildOptions(): array
    {
        return array_map(fn($option) => [
            'type' => self::TYPE_OPTION,
            'option' => $option->toClientFormat()
        ], $this->options);
    }

    /**
     * Get variables needed to build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    protected function getTemplateVars(int $layout_type): array
    {
        $vars = parent::getTemplateVars($layout_type);
        array_unshift($vars['classes'], 't-select');
        $vars['value'] = View::fetch('services.form.structure.fields.value_with_default', [
            'default_value' => 'N/A' // @todo add config for this later
        ])->render();
        return $vars;
    }
}

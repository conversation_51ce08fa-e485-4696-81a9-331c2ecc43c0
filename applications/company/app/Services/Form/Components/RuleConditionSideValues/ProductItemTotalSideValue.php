<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleConditionSideValues;

use App\Services\Form\Classes\Structure\Group\Rule\Condition\SideValue;
use App\Services\Form\Interfaces\Structure\Group\Rule\Condition\ContextSideValueInterface;

/**
 * Class ProductItemTotalSideValue
 *
 * @package App\Services\Form\Components\RuleConditionSideValues
 */
class ProductItemTotalSideValue extends SideValue implements ContextSideValueInterface
{
    public const KEY_LEFT = 'product_item_total';
    public const KEY_RIGHT = 'product_item_total_2';

    /**
     * @var string|null Key for left side of conditional
     */
    protected ?string $left_key = self::KEY_LEFT;

    /**
     * @var string|null Key for right side of conditional
     */
    protected ?string $right_key = self::KEY_RIGHT;

    /**
     * Helper method to create new instance of side value
     *
     * @param string $product_item_id
     * @param string|int|float $quantity
     * @return static
     */
    public static function make(string $product_item_id, string|int|float $quantity): static
    {
        return new static($product_item_id, $quantity);
    }

    /**
     * Create instance from conditional value
     *
     * @param array $value
     * @return static
     */
    public static function fromValue(array $value): static
    {
        [$product_item_id, $quantity] = $value;
        return static::make($product_item_id, $quantity);
    }

    /**
     * ProductItemTotalSideValue constructor
     *
     * @param string $product_item_id
     * @param string|int|float $quantity
     */
    public function __construct(protected string $product_item_id, protected string|int|float $quantity)
    {}

    /**
     * Convert value into storage format for conditional
     *
     * @return array
     */
    public function toValue(): array
    {
        return [$this->product_item_id, (string) $this->quantity];
    }
}

<?php

declare(strict_types=1);

namespace App\Services\Form\Components\MetaValues;

use App\Resources\Form\Item\MetaResource;
use App\Services\Form\Classes\Structure\Meta;

/**
 * Class StringMetaValue
 *
 * @package App\Services\Form\Components\MetaValues
 */
class StringMetaValue extends Meta
{
    /**
     * @var int Value type
     */
    protected int $value_type = MetaResource::VALUE_TYPE_STRING;
}

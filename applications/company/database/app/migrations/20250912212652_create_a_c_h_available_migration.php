<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateACHAvailableMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('propelrMerchants')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isACHAvailable')->after('companyUUID')->default(0);
            })
            ->useHistory(false)
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('propelrMerchants')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('isACHAvailable');
            })
            ->useHistory(false)
            ->alter();
    }
}
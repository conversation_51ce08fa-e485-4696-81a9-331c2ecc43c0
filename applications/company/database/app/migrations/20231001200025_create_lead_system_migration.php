<?php

declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class CreateLeadSystemMigration extends Migration
{
    public function up(): void
    {
        $this->newTable('leads')
            ->primaryKey('leadID', false) // this will be used for task associations for now until the project and customer pages are updated to take in a UUID
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('status'); // new, working, converted, dead
                $table->unsignedTinyInteger('priority')->nullable(); // hot, warm, cold, dead
                $table->unsignedTinyInteger('marketingTypeID')->nullable();
                $table->unsignedInteger('assignedToUserID')->nullable();
                $table->string('email', 100)->nullable();
                $table->string('businessName', 200)->nullable();
                $table->string('firstName', 50)->nullable();
                $table->string('lastName', 50)->nullable();
                $table->string('address', 100)->nullable();
                $table->string('address2', 100)->nullable();
                $table->string('city', 50)->nullable();
                $table->string('state', 15)->nullable();
                $table->string('zip', 12)->nullable();
                $table->string('phoneNumber', 15)->nullable();
                $table->text('notes')->nullable();
                $table->text('workingNotes')->nullable();
                $table->text('deadNotes')->nullable();
                $table->dateTime('workingAt')->nullable();
                $table->unsignedInteger('workingByUserID')->nullable();
                $table->dateTime('convertedAt')->nullable();
                $table->unsignedInteger('convertedByUserID')->nullable();
                $table->dateTime('deadAt')->nullable();
                $table->unsignedInteger('deadByUserID')->nullable();
            })
            ->column('leadTypeID', Table::COLUMN_TYPE_UUID, [
                'after' => 'priority',
                'nullable' => true
            ])
            ->column('leadUUID', Table::COLUMN_TYPE_UUID, [
                'after' => 'leadID'
            ])
            ->index('search', Table::INDEX_TYPE_FULLTEXT, ['email', 'businessName', 'firstName', 'lastName',
                'address', 'address2', 'city', 'state', 'zip', 'phoneNumber', 'notes', 'workingNotes', 'deadNotes'])
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index('status');
                $table->index('marketingTypeID');
                $table->index('assignedToUserID');
                $table->index('workingByUserID');
                $table->index('deadByUserID');
                $table->index('convertedByUserID');
            })
            ->create();

        $this->newTable('leadTypes')
            ->primaryKey('leadTypeID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->string('name', 100);
                $table->unsignedTinyInteger('status'); // active, inactive, archived
                $table->dateTime('inactiveAt')->nullable();
                $table->unsignedInteger('inactiveByUserID')->nullable();
                $table->dateTime('archivedAt')->nullable();
                $table->unsignedInteger('archivedByUserID')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index('status');
            })
            ->create();

        $this->updateTable('customer')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('leadID')->after('customerUUID')->nullable();
            })
            ->alter();

        Schema::uuidColumn('project', 'projectUUID', [
            'after' => 'projectID'
        ]);
        Schema::uuidColumn('projectHistory', 'projectUUID', [
            'after' => 'projectID'
        ]);

        $this->updateTable('tasks')
            ->column('associationUUID', Table::COLUMN_TYPE_UUID, [
                'after' => 'associationID',
                'nullable' => true
            ])
            ->alter();
    }

    public function down(): void
    {
        $this->dropTables(['leads', 'leadsHistory', 'leadTypes', 'leadTypesHistory']);

        $this->updateTable('customer')
            ->columns(
                function (Blueprint $table) {
                    $table->dropColumn(['leadID']);
                }
            )
            ->alter();

        $this->updateTable('project')
            ->columns(
                function (Blueprint $table) {
                    $table->dropColumn(['projectUUID']);
                }
            )
            ->alter();

        $this->updateTable('tasks')
            ->columns(
                function (Blueprint $table) {
                    $table->dropColumn(['associationUUID']);
                }
            )
            ->alter();
    }
}

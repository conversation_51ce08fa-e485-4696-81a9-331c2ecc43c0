<?php

use App\Classes\DB\Migration;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

class AddQueueChannelsMigration extends Migration
{
    public function up()
    {
        $this->updateTable('queueJobs')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('channel')->after('queueJobID');
                $table->renameColumn('payload', 'job');
                $table->unsignedTinyInteger('maxTries')->nullable()->after('handleAt');
            })
            ->indexes(function (Blueprint $table) {
                $table->index('channel', 'channel_index');
                $table->index('handleAt', 'handle_at_index');
            })
            ->useHistory(false)
            ->alter();
        DB::statement('ALTER TABLE `queueJobs` MODIFY COLUMN `job` LONGBLOB NOT NULL');

        $this->updateTable('queueJobLog')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('message');
                $table->dateTime('handleAt', 6)->after('queueJobID');
                $table->unsignedTinyInteger('try')->after('handleAt');
                $table->unsignedTinyInteger('status')->after('try');
                $table->unsignedBigInteger('time')->after('status');
            })
            ->useHistory(false)
            ->alter();
    }

    public function down()
    {
        DB::statement('ALTER TABLE `queueJobs` MODIFY COLUMN `job` LONGTEXT NOT NULL');
        $this->updateTable('queueJobs')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['channel', 'maxTries']);
                $table->renameColumn('job', 'payload');
                $table->dropIndex('handle_at_index');
            })
            ->useHistory(false)
            ->alter();
        $this->updateTable('queueJobLog')
            ->columns(function (Blueprint $table) {
                $table->text('message')->after('queueJobID');
                $table->dropColumn(['handleAt', 'try', 'status', 'time']);
            })
            ->useHistory(false)
            ->alter();
    }
}

<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

/**
 * Update propelrMerchants table for CoPilot onboarding integration
 */
final class UpdatePropelrMerchantsForCopilot extends Migration
{
    public function up(): void
    {
        $this->updateTable('propelrMerchants')
            ->columns(function (Blueprint $table) {
                $table->renameColumn('copilotApplicationID', 'copilotMerchantID');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('propelrMerchants')
            ->columns(function (Blueprint $table) {
                $table->string('signatureUrl', 512)
                    ->nullable()
                    ->after('templateID')
                    ->comment('Latest signature URL returned by PUT/GET /merchant/{id}/signature');
                    
                $table->string('signatureStatus', 32)
                    ->nullable()
                    ->after('signatureUrl')
                    ->comment('Signature status code (e.g., SIGNED, PENDING)');
                    
                $table->dateTime('signedAt')
                    ->nullable()
                    ->after('signatureStatus')
                    ->comment('Date/time when application was signed');
                    
                $table->dateTime('submittedAt')
                    ->nullable()
                    ->after('signedAt')
                    ->comment('Timestamp when we first generated signature (submitted for underwriting)');

                $table->string('lastSignatureErrorCode', 16)
                    ->nullable()
                    ->after('submittedAt')
                    ->comment('Last signature error code (from CoPilot)');
                    
                $table->string('lastSignatureErrorField', 128)
                    ->nullable()
                    ->after('lastSignatureErrorCode')
                    ->comment('Last signature error field path');
                    
                $table->string('lastSignatureErrorMessage', 255)
                    ->nullable()
                    ->after('lastSignatureErrorField')
                    ->comment('Last signature error message (short)');

                $table->unsignedTinyInteger('isReadyToProcess')
                    ->default(1)
                    ->after('lastSignatureErrorMessage')
                    ->comment('Processing readiness status: 1=pending, 2=ready');

                $table->dateTime('isReadyToProcessAt')
                    ->nullable()
                    ->after('boardedAt')
                    ->comment('Timestamp when CA TEAM set the merchant ready to process');

            })
            ->useHistory(false)
            ->alter();

        // Update comments for existing status fields
        $this->execute("ALTER TABLE propelrMerchants MODIFY COLUMN copilotMerchantID VARCHAR(36) NULL COMMENT 'CoPilot merchant identifier returned by POST /merchant'");
        $this->execute("ALTER TABLE propelrMerchants MODIFY COLUMN status TINYINT(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Application Status: 1=CREATED, 2=PENDING_SIGNATURE, 3=QUALIFY, 4=UNDER, 5=BOARDING, 6=BOARDED, 7=DECLINED'");
        $this->execute("ALTER TABLE propelrMerchants MODIFY COLUMN gatewayStatus TINYINT(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT 'CardConnect Gateway Status: 1=not_boarded, 2=boarded'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->updateTable('propelrMerchants')
            ->columns(function (Blueprint $table) {
                $table->dropColumn([
                    'signatureUrl',
                    'signatureStatus', 
                    'signedAt',
                    'submittedAt',
                    'lastSignatureErrorCode',
                    'lastSignatureErrorField',
                    'lastSignatureErrorMessage',
                    'isReadyToProcess',
                    'isReadyToProcessAt'
                ]);
                
                $table->renameColumn('copilotMerchantID', 'copilotApplicationID');
            })
            ->useHistory(false)
            ->alter();

        // Restore original comments
        $this->execute("ALTER TABLE propelrMerchants MODIFY COLUMN copilotApplicationID VARCHAR(36) NULL");
        $this->execute("ALTER TABLE propelrMerchants MODIFY COLUMN status TINYINT(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT 'CoPilot Application Status used during boarding'");
        $this->execute("ALTER TABLE propelrMerchants MODIFY COLUMN gatewayStatus TINYINT(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT 'CardConnect Gateway Status. If merchant can effectively process payments'");
    }
}
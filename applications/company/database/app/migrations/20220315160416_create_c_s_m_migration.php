<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Core\Components\DB\StaticAccessors\DB;
use Illuminate\Database\Schema\Blueprint;

final class CreateCSMMigration extends Migration
{
    public function up(): void
    {
        $this->newTable('successManagers')
            ->primaryKey('successManagerID', false)
            ->columns(function (Blueprint $table) {
                $table->string('name', 100);
                $table->unsignedTinyInteger('isAutoAssign');
                $table->unsignedTinyInteger('isAvailable')->nullable();
                $table->string('zendeskGroupName', 100);
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        // insert General
        $query_insert = <<<SQL
INSERT INTO `successManagers` (`successManagerID`, `name`, `isAutoAssign`, `isAvailable`, `zendeskGroupName`, `createdAt`, `updatedAt`, `deletedAt`)
VALUES
(1, 'House', 0, NULL, '32370267', NOW(), NOW(), NULL);
SQL;
        DB::statement($query_insert);

        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('successManagerID')->after('resellerID');
                $table->string('zendeskOrganizationID', 50)->nullable()->after('hubspotDealID');
            })
            ->indexes(function (Blueprint $table) {
                $table->index('successManagerID', 'success_manager_id_index');
            })
            ->useHistory()
            ->alter();

        DB::statement('UPDATE companies SET successManagerID = 1');
        DB::statement('UPDATE companiesHistory SET successManagerID = 1');
    }

    public function down(): void
    {
        $this->dropTables(['successManagers']);
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['successManagerID', 'zendeskOrganizationID']);
            })
            ->useHistory()
            ->alter();
    }
}

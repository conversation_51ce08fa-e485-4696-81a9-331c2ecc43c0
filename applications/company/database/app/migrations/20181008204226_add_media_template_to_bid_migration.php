<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class AddMediaTemplateToBidMigration extends Migration
{
    public function up()
    {
        $this->updateTable('bidItems')
            ->column('mediaContentTemplateID', Table::COLUMN_TYPE_UUID, [
                'nullable' => true,
                'after' => 'imagesContentTemplateID'
            ])
            ->alter();
    }

    public function down()
    {
        $this->updateTable('bidItems')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('mediaContentTemplateID');
            })
            ->alter();
    }
}

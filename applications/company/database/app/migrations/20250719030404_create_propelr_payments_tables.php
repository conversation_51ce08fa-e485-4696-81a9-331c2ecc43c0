<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;
use App\Classes\DB\Schema;

final class CreatePropelrPaymentsTables extends Migration
{

    public function up(): void
    {
        $this->newTable('propelrMerchants')
            ->primaryKey('propelrMerchantID')
            ->columns(function (Blueprint $table) {
                $table->string('ccMID', 32)
                    ->nullable()
                    ->unique()
                    ->comment('CardConnect Credit Card Merchant ID');
                
                $table->string('achMID', 32)
                    ->nullable()
                    ->unique()
                    ->comment('CardConnect ACH Merchant ID');

                $table->string('copilotApplicationID', 36)->nullable();
                $table->string('templateID', 36)
                    ->nullable()
                    ->comment('CoPilot template used during boarding');

                $table->unsignedTinyInteger('status')
                    ->default(1)
                    ->comment('CoPilot Application Status used during boarding');

                $table->unsignedTinyInteger('gatewayStatus')
                    ->default(1)
                    ->comment('CardConnect Gateway Status. If merchant can effectively process payments');

                $table->string('gatewayUsername', 64)
                    ->nullable()
                    ->comment('Basic-auth username for CardConnect API');

                $table->string('gatewayPasswordEnc', 255)
                    ->nullable()
                    ->comment('Encrypted basic-auth password');

                $table->dateTime('lastSyncedAt')
                    ->nullable()
                    ->comment('Last reconciliation/poll against CardConnect');

                $table->dateTime('approvedAt')
                    ->nullable()
                    ->comment('Timestamp when CoPilot application was approved');

                $table->dateTime('declinedAt')
                    ->nullable()
                    ->comment('Timestamp when CoPilot application was declined');

                $table->dateTime('boardedAt')
                    ->nullable()
                    ->comment('Timestamp when merchant was boarded in CardConnect');
            })
            ->column('companyUUID', Table::COLUMN_TYPE_UUID, [
                'nullable' => false,
                'after' => 'propelrMerchantID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('status',    'idx_merchant_status');
            })
            ->timestamps(true, true)
            ->useHistory(false)
            ->create();

        // --- TRANSACTIONS ---
        $this->newTable('propelrTransactions')
            ->primaryKey('propelrTransactionID')
            ->columns(function (Blueprint $table) {
                // === Transaction Identity===
                $table->string('retref', 16)
                    ->unique()
                    ->comment('CardConnect reference (retref) returned by /auth');
                $table->string('orderID', 50)
                    ->nullable()
                    ->comment('Merchant order identifier');
                $table->string('invoiceNumber', 50)
                    ->nullable()
                    ->comment('Optional invoice reference');

                // === Transaction Classification ===
                $table->unsignedTinyInteger('type')
                    ->comment('Transaction type: 1=Credit Card, 2=ACH');
                $table->unsignedTinyInteger('status')
                    ->default(1)
                    ->comment('1=authorized, 2=queued_capture, 3=captured, 4=batch_sent, 5=settled, 6=declined, 7=failed, 8=voided, 9=refunded, 10=retry');
                $table->unsignedTinyInteger('achAccountType')
                    ->nullable()
                    ->comment('ACH account type: 1=Checking, 2=Savings');

                // === Financial Information ===
                $table->decimal('baseAmount', 12, 2)
                    ->comment('Original payment amount before credit card processing fee');
                $table->decimal('creditCardProcessingFee', 12, 2)
                    ->default(0)
                    ->comment('Credit card processing fee applied to the transaction');
                $table->decimal('totalAmount', 12, 2)
                    ->comment('Total amount charged (baseAmount + creditCardProcessingFee)');
                $table->decimal('settledAmount', 10, 2)
                    ->nullable()
                    ->comment('Amount that was actually settled by processor');
                $table->char('currency', 3)->default('USD');

                // === Customer Information ===
                $table->string('customerFirstName', 100)->nullable();
                $table->string('customerLastName', 100)->nullable();
                $table->string('customerEmail', 255)->nullable();
                $table->string('customerPhone', 20)->nullable();

                // === Payment Method Details ===
                $table->string('paymentToken', 36)
                    ->nullable()
                    ->comment('CardSecure token for future charges');
                $table->char('last4', 4)
                    ->nullable()
                    ->comment('Last 4 digits of card/account');
                $table->unsignedTinyInteger('expMonth')
                    ->nullable()
                    ->comment('Card expiration month');
                $table->unsignedSmallInteger('expYear')
                    ->nullable()
                    ->comment('Card expiration year');
                $table->string('cardBrand', 20)
                    ->nullable()
                    ->comment('Card brand: visa, mastercard, etc');
                $table->string('routingNumber', 9)
                    ->nullable()
                    ->comment('Bank routing number for ACH');

                // === Processing details ===
                $table->string('entryMode', 20)
                    ->nullable()
                    ->comment('Channel used: Ecommerce, Keyed, etc.');
                $table->string('setlStat', 32)
                    ->nullable()
                    ->comment('Settlement status reported by gateway');
                $table->string('batchID',32)
                    ->nullable()
                    ->comment('Processor batch ID for settlement reconciliation');
                $table->string('hostBatch', 32)
                    ->nullable()
                    ->comment('Host batch identifier from settlement response');
                $table->char('commCard', 1)
                    ->nullable()
                    ->comment('Indicates a commercial/corporate card: C');
                $table->string('binType', 10)
                    ->nullable()
                    ->comment('Debit, Credit, Prepaid');
                                
                // === Gateway Response Data ===
                $table->string('authCode', 10)
                    ->nullable()
                    ->comment('Bank approval code');
                $table->string('gatewayResponseCode', 10)
                    ->nullable()
                    ->comment('Gateway response code');
                $table->string('gatewayResponseMessage', 255)
                    ->nullable()
                    ->comment('Gateway response message');
                $table->string('cardResponseCode', 10)
                    ->nullable()
                    ->comment('Card issuer response code from assocRespCode');
                $table->string('cardResponseMessage', 255)
                    ->nullable()
                    ->comment('Card issuer response message from assocRespText');
                $table->char('respStat', 1)
                    ->nullable()
                    ->comment('Gateway response status: A=Approved, B=Retry, C=Declined');
                $table->char('processorCode', 4)
                    ->nullable()
                    ->comment('Processor routing code (e.g., FNOR, RPCT)');

                // === Verification results ===
                $table->char('avsResp', 1)
                    ->nullable()
                    ->comment('Address verification result: Y, N, Z, etc.');
                $table->char('cvvResp', 1)
                    ->nullable()
                    ->comment('CVV verification result: M, N, P, etc.');

                // === Transaction State Management ===
                $table->string('voidReference', 16)
                    ->nullable()
                    ->comment('CardConnect void reference');
                $table->string('refundReference', 16)
                    ->nullable()
                    ->comment('CardConnect refund reference');

                // === Raw Data & Metadata ===
                $table->json('rawGatewayJSON')
                    ->nullable()
                    ->comment('Complete CardConnect response JSON');
                $table->string('profileID', 36)
                    ->nullable()
                    ->comment('Vaulted customer profile ID from CardConnect');
                $table->json('metadata')
                    ->nullable()
                    ->comment('Business context: evaluationID, projectID, projectUUID, invoiceType, invoiceOrder, bidID, bidItemID');

                // === Timestamps ===
                $table->dateTime('authorizedAt', 6)
                    ->nullable()
                    ->comment('When transaction was authorized');
                $table->dateTime('capturedAt', 6)
                    ->nullable()
                    ->comment('When transaction was captured');
                $table->dateTime('voidedAt', 6)
                    ->nullable()
                    ->comment('When transaction was voided');
                $table->dateTime('refundedAt', 6)
                    ->nullable()
                    ->comment('When transaction was refunded');
                $table->dateTime('settledAt', 6)
                    ->nullable()
                    ->comment('When transaction was settled');
                $table->date('settlementDate')
                    ->nullable()
                    ->comment('Settlement business day (ET) when settlestat was run');
                $table->date('fundedAt')
                    ->nullable()
                    ->comment('Date when funding was confirmed');
                $table->string('fundingID', 22)
                    ->nullable()
                    ->comment('Unique funding identifier from CardConnect');
                $table->string('achReturnCode', 8)
                    ->nullable()
                    ->comment('ACH return code if transaction was returned');
            })
            ->column('propelrMerchantID', Table::COLUMN_TYPE_UUID, [
                'nullable' => false,
                'after' => 'propelrTransactionID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->fullText(['customerFirstName', 'customerLastName', 'customerEmail', 'customerPhone', 'batchID', 'fundingID', 'invoiceNumber'], 'search');
                $table->index('status', 'idx_txn_status');
                $table->index('batchID', 'idx_batch_id');
                $table->index('fundingID', 'idx_funding_id');
                $table->index('invoiceNumber', 'idx_invoice_number');
            })
            ->timestamps()
            ->useHistory(false)
            ->create();

        // --- PAYMENT LINKS ---
        $this->newTable('paymentLinks')
            ->primaryKey('paymentLinkID')
            ->columns(function (Blueprint $table) {
                $table->string('shortToken', 48)
                    ->unique()
                    ->comment('Random, URL-safe token for secure access');
            })
            ->column('propelrMerchantID', Table::COLUMN_TYPE_UUID, [
                'nullable' => false,
                'after' => 'paymentLinkID',
                'comment' => 'FK to merchant'
            ])
            ->column('createdByUserID', 'integer', [
                'nullable' => false,
                'after' => 'shortToken'
            ])
            ->column('deletedByUserID', 'integer', [
                'nullable' => true,
                'after' => 'deletedAt'
            ])
            ->timestamps(true, true)
            ->useHistory(false)
            ->create();
    }


    /** Reverse the migrations. */
    public function down(): void
    {
        $this->dropTables(
            [
                'paymentLinks',
                'propelrTransactions',
                'propelrMerchants',
            ],
            false
        );
    }

}

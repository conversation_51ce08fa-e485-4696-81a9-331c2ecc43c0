<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;
use App\Classes\DB\Schema;

final class CreateMySalesmanCredentialsTable extends Migration
{
    public function up(): void
    {
        if (!$this->schema->hasTable('mySalesmanCredentials')) {

            $this->newTable('mySalesmanCredentials')
                ->column('mySalesmanCredentialsID', Schema\Table::COLUMN_TYPE_UUID, [
                    'length' => 16,
                    'first' => true,
                    'primary' => true
                ])
                ->columns(function (Blueprint $table) {
                    $table->string('key', 64);
                    $table->unsignedTinyInteger('isActive')->default(1);
                    $table->dateTime('deactivatedAt')->nullable();
                })
                ->timestamps(false, false)
                ->useHistory(false)
                ->create();
        }

        // Add the userID column using raw SQL and then add the foreign key constraint
        DB::statement('ALTER TABLE `mySalesmanCredentials` ADD `userID` INT(11) NOT NULL AFTER `mySalesmanCredentialsID`');

        $this->schema->table('mySalesmanCredentials', function (Blueprint $table) {
            $table->foreign('userID')->references('userID')->on('user')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        if ($this->schema->hasTable('mySalesmanCredentials')) {
            $this->schema->drop('mySalesmanCredentials');
        }
    }
}

<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class UpdateEmailValidationMigration extends Migration
{
    public function up()
    {
        $this->updateTable('emailValidations')
            ->columns(function (Blueprint $table) {
                $table->unsignedBigInteger('time')->nullable(true)->after('hits');
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('emailValidations')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['time']);
            })
            ->alter();
    }
}

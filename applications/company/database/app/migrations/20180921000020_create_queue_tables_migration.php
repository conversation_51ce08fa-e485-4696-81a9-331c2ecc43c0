<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class CreateQueueTablesMigration extends Migration
{
    public function up()
    {
        $this->newTable('queueJobs')
            ->primaryKey('queueJobID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
                $table->longText('payload');
                $table->unsignedTinyInteger('status');
                $table->unsignedSmallInteger('tries');
            })
            ->column('handleAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'payload'
            ])
            ->column('handledAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'tries',
                'nullable' => true
            ])
            ->column('failedAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'handledAt',
                'nullable' => true
            ])
            ->column('createdAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'failedAt'
            ])
            ->column('updatedAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'createdAt'
            ])
            ->column('deletedAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'updatedAt',
                'nullable' => true
            ])
            ->noTimestamps()
            ->useHistory(false)
            ->indexes(function (Blueprint $table) {
                $table->index('type');
            })
            ->create();

        $this->newTable('queueJobLog')
            ->primaryKey('queueJobLogID')
            ->columns(function (Blueprint $table) {
                $table->text('message');
            })
            ->column('queueJobID', Table::COLUMN_TYPE_UUID, [
                'after' => 'queueJobLogID'
            ])
            ->column('createdAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'message'
            ])
            ->column('updatedAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'createdAt'
            ])
            ->column('deletedAt', Table::COLUMN_TYPE_DATETIME_PRECISION, [
                'precision' => 6,
                'after' => 'updatedAt',
                'nullable' => true
            ])
            ->noTimestamps()
            ->useHistory(false)
            ->indexes(function (Blueprint $table) {
                $table->index('queueJobID');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables(['queueJobs', 'queueJobLog']);
    }
}

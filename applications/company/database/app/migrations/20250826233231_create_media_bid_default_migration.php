<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;


final class CreateMediaBidDefaultMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('media')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isBidDefault')->after('isBidMedia')->default(1);
            })
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('media')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('isBidDefault');
            })
            ->alter();
    }
}

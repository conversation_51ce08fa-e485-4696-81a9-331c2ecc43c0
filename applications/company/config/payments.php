<?php

use function Core\Functions\env;

return [
    'card_connect' => [
        'gateway_url' => env('PAYMENTS_CARDCONNECT_GATEWAY_URL'),
    ],
    'card_secure' => [
        'tokenize_url' => env('PAYMENTS_CARDCONNECT_GATEWAY_URL'),
    ],
    'copilot' => [
        'gateway_url' => env('PAYMENTS_COPILOT_GATEWAY_URL'),
        'api_url' => env('PAYMENTS_COPILOT_API_URL'),
        'token_url' => env('PAYMENTS_COPILOT_TOKEN_URL'),
        'api_version' => env('PAYMENTS_COPILOT_API_VERSION', '1.0'),
        'sales_code' => env('PAYMENTS_COPILOT_SALES_CODE'),
        'application_template_id' => env('PAYMENTS_COPILOT_APPLICATION_TEMPLATE_ID'),
        'username' => env('PAYMENTS_COPILOT_USERNAME'),
        'password' => env('PAYMENTS_COPILOT_PASSWORD'),
        'client_id' => env('PAYMENTS_COPILOT_CLIENT_ID'),
        'client_secret' => env('PAYMENTS_COPILOT_CLIENT_SECRET'),
    ],
    'recaptcha_key' => env('RECAPTCHA_KEY'),
    'recaptcha_secret' => env('RECAPTCHA_SECRET'),

];

<?php

use App\Services\Email\Types;

use function Core\Functions\env;

return [
    /**
     * Mail driver to use
     *
     * Note: currently only 'smtp' is available
     */
    'driver' => 'smtp',

    /**
     * SMTP driver settings
     */
    'smtp' => [
        'host' => env('SMTP_HOST'),
        'port' => env('SMTP_PORT'),
        'user' => env('SMTP_USER'),
        'pass' => env('SMTP_PASS'),
        'secure' => env('SMTP_SECURE')
    ],

    'log_bounces' => env('MAIL_LOG_BOUNCES', false),

    'types' => [
        // customer
        1 => Types\Customer\CreatedType::class,
        2 => Types\Customer\LegacyBidViewType::class,
        3 => Types\Customer\ProjectEventCreatedType::class,
        4 => Types\Customer\LegacyBidRejectedType::class,
        16 => Types\Customer\BidViewType::class,
        18 => Types\Customer\ProjectEventReminderType::class,
        21 => Types\Customer\BidFollowUp1Type::class,
        22 => Types\Customer\BidFollowUp2Type::class,
        23 => Types\Customer\BidFollowUp3Type::class,
        24 => Types\Customer\BidFollowUp4Type::class,
        25 => Types\Customer\BidFollowUp5Type::class,

        31 => Types\Customer\BidFollowUp6Type::class,
        32 => Types\Customer\BidFollowUp7Type::class,
        33 => Types\Customer\BidFollowUp8Type::class,
        34 => Types\Customer\BidFollowUp9Type::class,
        35 => Types\Customer\BidFollowUp10Type::class,

        // lead
        37 => Types\Lead\CreatedType::class,

        // system
        5 => Types\System\FinalizeRegistrationType::class, // no longer in use
        17 => Types\System\EmailMessageRecipientBounceType::class,

        // single user
        6 => Types\User\PasswordResetType::class,
        19 => Types\User\AccessLinkType::class,
        26 => Types\User\TaskAssignedType::class,
        27 => Types\User\LeadAssignedType::class,
        28 => Types\User\ProjectAssignedType::class,
        36 => Types\User\ProjectEventCreatedType::class,

        // primary users
        7 => Types\User\CompanyActivatedType::class,
        8 => Types\User\CompanySuspendedType::class,
        9 => Types\User\PaymentFailedType::class,
        10 => Types\User\SubscriptionCancelledType::class,
        20 => Types\User\InvoiceCreateType::class,
        29 => Types\User\WisetackApplicationSubmittedType::class,
        30 => Types\User\WisetackApplicationApprovedType::class,

        38 => Types\User\PaymentsApplicationPendingSignatureType::class,
        39 => Types\User\PaymentsApplicationSubmittedType::class,
        40 => Types\User\PaymentsApplicationUnderwritingType::class,
        41 => Types\User\PaymentsApplicationDeclinedType::class,
        42 => Types\User\PaymentsApplicationApprovedType::class,
        43 => Types\User\PaymentsLiveType::class,

        // multi user
        11 => Types\User\BidSubmittedType::class,
        12 => Types\User\LegacyBidAcceptedType::class,
        // 13 => Legacy Bid Submitted (no longer used)
        14 => Types\User\LegacyBidViewedType::class,
        15 => Types\User\LegacyBidRejectedType::class
    ],

    'mailing_list' => [
        'enabled' => env('MAILING_LIST_ENABLED', true)
    ],

    'mailchimp' => [
        'api_key' => env('MAILCHIMP_API_KEY'),
        'list_id' => env('MAILCHIMP_LIST_ID'),
        'days_to_onboard' => env('MAILCHIMP_DAYS_TO_ONBOARD', 35)
    ],

    'mailgun' => [
        'api_key' => env('MAILGUN_API_KEY')
    ],

    'zerobounce' => [
        'api_key' => env('ZEROBOUNCE_API_KEY')
    ]
];

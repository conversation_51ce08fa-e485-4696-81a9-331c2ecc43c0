@use '~@cac-sass/base';

.s-notification-toasts {
    position: fixed;
    right: base.unit-rem-calc(16px);
    bottom: base.unit-rem-calc(16px);
    display: none;
    width: base.unit-rem-calc(320px);
    z-index: 3050;
    &.t-has-message {
        display: block;
    }
    @include base.respond-to('<400px') {
        width: 100%;
        right: 0;
        left: 0;
        bottom: base.unit-rem-calc(8px);
        padding: 0 base.unit-rem-calc(8px);
    }
    .i-nt-message {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-top: base.unit-rem-calc(16px);
        padding: base.unit-rem-calc(4px) base.unit-rem-calc(8px) base.unit-rem-calc(8px) base.unit-rem-calc(8px);
        background-color: base.$color-white-default;
        border-width: base.unit-rem-calc(1px);
        border-style: solid;
        border-color: base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        box-shadow: base.$elevation-level-3;
        &.t-info {
            .i-ntmh-icon{
                color: base.$color-primary-light-1;
            }
        }
        &.t-success {
            .i-ntmh-icon {
                color: base.$color-green-light-1;
            }
        }
        &.t-warning {
            .i-ntmh-icon {
                color: base.$color-yellow-light-1;
            }
        }
        &.t-error {
            .i-ntmh-icon {
                color: base.$color-red-light-2;
            }
        }
        &:first-child {
            margin-top: 0;
        }
        .i-ntm-actions {
            border-color: base.$color-grey-light-2;
        }
            .i-ntma-action {
                color: base.$color-grey-light-2;
                border: base.unit-rem-calc(1px) transparent solid;
                border-radius: base.unit-rem-calc(24px);
                transition: all 0.3s cubic-bezier(0.45, 0.05, 0.55, 0.95);
                @include base.respond-to('hover') {
                    &:hover {
                        scale: 1.2;
                    }
                    &:active {
                        color: base.$color-grey-dark-1;
                    }
                }
            }
    }
        .i-ntm-header {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(4px);
            height: base.unit-rem-calc(24px);
        }
            .i-ntmh-title {
                @include base.typo-paragraph-medium;
                text-transform: capitalize;
            }
            .i-ntmh-icon {
                display: flex;
                justify-content: center;
                align-items: center;
            }
                .i-ntmhi-svg {
                    @include base.svg-icon('default-18');
                }
        .i-ntm-message {
            flex: 1;
            color: base.$color-grey-dark-1;
            padding-left: base.unit-rem-calc(2px);
        }

            .i-ntmm-text {}
        .i-ntm-actions {
            display: flex;
            position: absolute;
            top: base.unit-rem-calc(4px);
            right: base.unit-rem-calc(4px);
            @include base.respond-to('<400px') {
                right: base.unit-rem-calc(12px);
            }
        }
            .i-ntma-action {
                display: flex;
                justify-content: center;
                align-items: center;
                &.t-icon {
                    width: base.unit-rem-calc(24px);
                    height: base.unit-rem-calc(24px);
                }
            }
                .i-ntmaa-icon {
                    @include base.svg-icon('default-18');
                }
}

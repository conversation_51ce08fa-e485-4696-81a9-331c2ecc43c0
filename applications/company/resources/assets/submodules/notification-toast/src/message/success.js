'use strict';

import {Types} from './constants';
import {createMessage} from './factory';
import {createCloseAction} from './action/icon/close';

/**
 * Factory to create success message
 *
 * @param {string} message
 * @param {object} [config={}]
 * @returns {module:NotificationToast.Message}
 */
export function createSuccessMessage(message, config = {}) {
    return createMessage(message, Types.SUCCESS, config).action(createCloseAction());
}

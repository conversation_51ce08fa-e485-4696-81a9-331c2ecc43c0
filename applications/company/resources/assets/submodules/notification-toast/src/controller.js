'use strict';

import {insertTemplate} from '@ca-package/dom';

import {Controller as BaseController} from '@ca-package/notification';

import container_tpl from '@cas-notification-toast-tpl/container.hbs';

/**
 * @memberof module:NotificationToast
 */
class Controller extends BaseController {
    constructor() {
        super();
        this.boot();
    };

    /**
     * Boot controller
     */
    boot() {
        this.elem.root = insertTemplate('body', this.render());

        super.boot();

        this.bindEvents();
        this.bootMessages();

        this.state.booted = true;
    };

    /**
     * Render controller
     *
     * @returns {string}
     */
    render() {
        this.state.rendered = true;

        return container_tpl({
            messages: this.renderMessages()
        });
    };
}

let instance = null;

/**
 * Get singleton of controller since only one can exist at a time
 *
 * @returns {module:NotificationToast.Controller}
 */
export function getController() {
    if (instance === null) {
        instance = new Controller();
    }
    return instance;
}

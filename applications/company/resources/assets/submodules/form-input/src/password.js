'use strict';

const $ = require('jquery');

const debounce = require('@cac-js/utils/debounce');

const FormInput = require('./index');
const PasswordStrength = require('./utils/password_strength');

const input_tpl = require('@cas-form-input-tpl/password.hbs');

require('remixicon/icons/System/eye-off-line.svg');
require('remixicon/icons/System/eye-line.svg');

/**
 * @memberof module:FormInput
 */
class Password extends FormInput {
    /**
     * Constructor
     *
     * @param {jQuery} element
     * @param {object} [config={}]
     */
    constructor(element, config = {}) {
        super(element);
        if (!element.is('input')) {
            throw new Error('Password input only works with input elements');
        }
        Object.assign(this.state, {
            min_score: config.min_score !== undefined ? config.min_score : 3,
            service: new PasswordStrength(),
            value: element.val()
        });

        this.elem.root = $(input_tpl());
        this.elem.view = this.elem.root.fxFind('view');
        this.elem.icon = this.elem.view.fxFind('icon');
        this.elem.strength = this.elem.root.fxFind('strength');
        this.elem.info = this.elem.strength.fxFind('info');
        this.elem.info_message = this.elem.info.fxFind('message').remove();
        element.after(this.elem.root);
        this.elem.view.after(element);

        // If min_score is explicitly false, hide strength meter from the start
        if (this.state.min_score === false) {
            this.elem.strength.hide();
        }

        element.fxEvent('input', debounce(() => this.setValue(element.val()), 400));
        element.fxEvent('password:value-update', () => this.setValue(element.val(), false));

        this.elem.view.fxClick(this.toggleVisibility.bind(this), true);
    };

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'password';
    };

    /**
     * Set input type
     *
     * @param {string} type
     */
    setInputType(type) {
        this.elem.icon[0].setAttributeNS('http://www.w3.org/1999/xlink', 'href', `#remix-icon--system--eye-${type === 'text' ? 'off-line' : 'line'}`);
        this.elem.main.attr('type', type);
    };

    /**
     * Toggle input between password and text types to allow user to verify the text they entered
     */
    toggleVisibility() {
        this.setInputType(this.elem.main.attr('type') === 'password' ? 'text' : 'password');
    };

    /**
     * Clear messages returned from strength check
     */
    clearInfo() {
        this.elem.info.empty().hide();
    };

    /**
     * Hide and reset meter, clear messages
     */
    clearStrength() {
        this.clearInfo();
        this.elem.strength.attr('data-score', '0').hide();
    };

    /**
     * Set value of input
     *
     * @param {string} value
     * @param {boolean} [notify=true]
     */
    setValue(value, notify = true) {
        let password = value.trim(),
            has_password = password !== '';
        this.state.value = password;
        
        if (this.state.min_score === false) {
            this.elem.strength.hide();
            if (notify) {
                this.elem.main.trigger('password:change', password);
            }
            return;
        }
        
        this.elem.strength.toggle(has_password);
        if (has_password) {
            this.state.service.getScore(password).then(({score, feedback = {}}) => {
                this.elem.strength.attr('data-score', score);
                this.elem.main.data('score', score);
                let info = [];
                if (score < this.state.min_score) {
                    info.push('<strong>Warning:</strong> Password is not secure enough');
                }
                if (Array.isArray(feedback.suggestions)) {
                    feedback.suggestions.forEach(suggestion => info.push(`<strong>Suggestion:</strong> ${suggestion}`));
                }
                this.clearInfo();
                if (info.length > 0) {
                    for (let message of info) {
                        let elem = this.elem.info_message.clone().html(message);
                        this.elem.info.append(elem);
                    }
                    this.elem.info.show();
                }
            });
        } else {
            this.elem.main.data('score', 0);
        }
        if (notify) {
            this.elem.main.trigger('password:change', password);
        }
    };

    /**
     * Cancel all active requests
     */
    cancel() {
        if (this.state.active === 0) {
            return;
        }
        for (let request of this.state.requests) {
            if (request === null) { // request is already complete
                continue;
            }
            request.abort();
        }
    };

    /**
     * Reset field to default state
     */
    reset() {
        this.setValue('');
        this.setInputType('password');
        this.elem.strength.removeAttr('data-score');
    };

    /**
     * Destroy instance, reset UI back to default and unbind events
     */
    destroy() {
        this.elem.root.after(this.elem.main);
        this.elem.root.remove();
        this.elem.main.removeData('score').fxEventDestroy(['keyup', 'password:value-update']);
        super.destroy();
    };
}

module.exports = Password;

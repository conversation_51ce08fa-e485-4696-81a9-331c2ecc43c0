/**
 * @module List/Types
 */

'use strict';

const lang = {
    isBoolean: require('lodash/isBoolean'),
    isObject: require('lodash/isObject'),
    isString: require('lodash/isString'),
    isArray: require('lodash/isArray')
};
const EventEmitter = require('events');
const Sortable = require('sortablejs');

const list_tpl = require('@cas-list-tpl/layout.hbs');
const item_tpl = require('@cas-list-tpl/types/base.hbs');
const action_tpl = require('@cas-list-tpl/action.hbs');

/**
 * @typedef {Object} ItemConfig
 * @property {number} id - Id of item
 * @property {?string} label - Label of item (used for default display type)
 * @property {?string} thumbnail_url - URL of thumbnail (used for media display type)
 * @property {?string} name - Name of media (used for media display type)
 * @property {Object} storage - Storage for extra information associated with item
 * @property {?Object} elem - Container for item jQuery elements
 * @property {?Object} elem.root - Item element jQuery object
 */

/**
 * @typedef {Object} ActionConfig
 * @property {number} id - Id of action
 * @property {string} label - Label of action
 * @property {function} handler - Closure to handle action click events
 * @property {?Object} elem - Container for item jQuery elements
 * @property {?Object} elem.root - Action wrapper element jQuery object
 * @property {?Object} elem.action - Action anchor element jQuery object
 */

/**
 * @memberof module:List/Types
 * @abstract
 */
class Base {
    /**
     * Constructor
     *
     * @param {Object} [config={}]
     */
    constructor(config = {}) {
        Base.__idx++;
        this.elem = {};
        /**
         * @protected
         */
        this.state = {
            rendered: false,
            booted: false,
            config: {
                display_type: Base.DisplayType.DEFAULT,
                sortable: false,
                no_items_text: 'No items',
                working_text: 'Loading...',
                preview_handler: null
            },
            id: Base.__idx,
            item_idx: 0,
            /**
             * @type {Map.<number, ItemConfig>}
             */
            items: new Map,
            action_idx: 0,
            /**
             * @type {Map.<number, ActionConfig>}
             */
            actions: new Map,
            reorder_timer: null,
            events: new EventEmitter,
            no_items: true,
            working: false
        };
        Object.assign(this.state.config, config);
    };

    /**
     * Action types
     *
     * @readonly
     *
     * @returns {{ADD: number, EDIT: number, REFRESH: number}}
     */
    static get ActionType() {
        return {
            ADD: 1,
            EDIT: 2,
            REFRESH: 3
        };
    };

    /**
     * Display types
     *
     * @readonly
     *
     * @returns {{DEFAULT: number, MEDIA: number}}
     */
    static get DisplayType() {
        return {
            DEFAULT: 1,
            MEDIA: 2
        };
    };

    /**
     * Add event listener
     *
     * @param {string} event - Event name
     * @param {function} closure - Event handler
     * @returns {module:List/Types.Base}
     */
    on(event, closure) {
        this.state.events.on(event, closure);
        return this;
    };

    /**
     * Toggle display of no items content
     *
     * @param {boolean} active
     */
    toggleNoItems(active) {
        if (this.state.no_items && active) {
            return;
        }
        this.state.no_items = active;
        if (!this.isWorking()) {
            this.elem.no_items[active ? 'show' : 'hide']();
        }
    };

    /**
     * Determines if list is in working state
     *
     * @returns {boolean}
     */
    isWorking() {
        return this.state.working;
    };

    /**
     * Toggle display of working content
     *
     * This will hide items and no items wrappers and restore them once it's disabled again
     *
     * @param {boolean} active
     */
    toggleWorking(active) {
        if (!this.isBooted() || (this.state.working && active)) {
            return;
        }
        this.state.working = active;
        this.toggleAllActions(!active);
        this.elem.no_items.toggle(active ? false : this.state.no_items);
        this.elem.items[active ? 'hide' : 'show']();
        this.elem.working[active ? 'show' : 'hide']();
    };

    /**
     * Render item
     *
     * @protected
     *
     * @param {ItemConfig} item
     * @returns {(undefined|string)}
     */
    renderItem(item) {
        let tpl_data = {
            id: item.id
        };
        switch (this.state.config.display_type) {
            case Base.DisplayType.DEFAULT:
                tpl_data.label = item.label;
                break;
            case Base.DisplayType.MEDIA:
                tpl_data.media = {
                    thumbnail_url: item.thumbnail_url,
                    name: item.name
                };
                break;
        }
        item.elem = {
            root: $(item_tpl(tpl_data))
        };
        item.elem.label = item.elem.root.fxFind('label');
        this.elem.items.append(item.elem.root);
    };

    /**
     * Hook to change configuration data of item
     *
     * @param {object} data
     * @param {object} item
     * @returns {object}
     */
    handleItemConfig(data, item) {
        switch (this.state.config.display_type) {
            case Base.DisplayType.DEFAULT:
                if (lang.isString(data)) {
                    item.label = data;
                    break;
                }
                if (lang.isObject(data)) {
                    if (data.label === undefined) {
                        throw new Error('Label is required');
                    }
                    item.label = data.label;
                    break;
                }
                throw new Error('Invalid display data');
            case Base.DisplayType.MEDIA:
                if (!lang.isObject(data)) {
                    throw new Error('Display data must be an object');
                }
                if (!lang.isString(data.name)) {
                    throw new Error('Name is required');
                }
                item.name = data.name;
                if (!lang.isString(data.thumbnail_url)) {
                    throw new Error('Thumbnail URL is required');
                }
                item.thumbnail_url = data.thumbnail_url;
                break;
        }
        return item;
    };

    /**
     * Add list item
     *
     * @param {(string|Object)} data - Display data of item
     * @param {Object} [storage={}] - Storage for extra data associated with this item
     * @returns {number}
     */
    addItem(data, storage = {}) {
        let id = ++this.state.item_idx;
        let item = this.handleItemConfig(data, {id, storage});
        this.state.items.set(id, item);
        if (this.isBooted()) {
            if (this.state.no_items) {
                this.toggleNoItems(false);
            }
            this.renderItem(item);
        }
        return id;
    };

    /**
     * Get item config
     *
     * @param {number} id
     * @returns {ItemConfig}
     */
    getItem(id) {
        return this.state.items.get(id);
    };

    /**
     * Set label for specified item
     *
     * @param {number} id
     * @param {string} label
     */
    setItemLabel(id, label) {
        let item = this.getItem(id);
        item.label = label;
        if (this.isBooted()) {
            item.elem.label.text(label);
        }
    };

    /**
     * Handle preview for item
     *
     * @param {number} id
     */
    handleItemPreview(id) {
        if (typeof this.state.config.preview_handler === 'function') {
            this.state.config.preview_handler(this.getItem(id));
        }
    };

    /**
     * Get item order from DOM positioning
     *
     * @returns {number[]}
     */
    getItemOrder() {
        let items = [];
        this.elem.items.fxChildren('item').each(function () {
            items.push(parseInt($(this).data('id')));
        });
        return items;
    };

    /**
     * Set item order
     *
     * Updates internal item map to be in the specified order and optionally reorders form elements in the DOM
     *
     * @param {number[]} list - Array of item id's
     * @param {boolean} [reorder=true] - Determines if items are reordered in the DOM
     * @param {boolean} [notify=true] - Determines if event is emitted
     *
     * @emits module:List~itemsReordered
     */
    setItemOrder(list, reorder = true, notify = true) {
        let items = new Map;
        for (let item_id of list) {
            let item = this.getItem(item_id);
            items.set(item.id, item);
        }
        this.state.items = items;
        if (reorder) {
            this.reorderItems();
        }
        if (notify) {
            this.state.events.emit('items-reordered', {
                order: list,
                list: this
            });
        }
    };

    /**
     * Reorder items in DOM according to the order they are in the items map
     */
    reorderItems() {
        if (!this.isBooted()) {
            return;
        }
        // loop through map and adjust placement
        let first = true,
            last_elem;
        for (let item of this.state.items.values()) {
            if (!first) {
                item.elem.root.insertAfter(last_elem);
            }
            last_elem = item.elem.root;
            if (first) {
                first = false;
            }
        }
    };

    /**
     * Handle reordering after debouncing
     *
     * Updates internal item map to correct order and emits proper events
     */
    handleReorder() {
        if (this.state.reorder_timer !== null) {
            clearTimeout(this.state.reorder_timer);
        }
        this.state.reorder_timer = setTimeout(() => {
            this.setItemOrder(this.getItemOrder(), false);
            this.state.reorder_timer = null;
        }, 1000);
    };

    /**
     * Delete item from internal cache and fire associated events
     *
     * @param {number} item_id
     *
     * @emits module:List~itemDeleted
     */
    deleteItem(item_id) {
        let item = this.getItem(item_id);
        if (item.elem !== undefined) {
            item.elem.root.remove();
        }
        this.state.items.delete(item_id);
        this.state.events.emit('item-deleted', {
            item: item
        });
        if (this.isBooted()) {
            if (this.state.items.size === 0) {
                this.toggleNoItems(true);
            }
        }
    };

    /**
     * Delete all items
     */
    deleteAllItems() {
        for (let item_id of this.state.items.keys()) {
            this.deleteItem(item_id);
        }
    };

    /**
     * Render action
     *
     * @protected
     *
     * @param {ActionConfig} action
     * @returns {(undefined|string)}
     */
    renderAction(action) {
        action.elem = {
            root: $(action_tpl({
                id: action.id,
                label: action.label,
                icon: action.icon,
                disabled: action.disabled
            }))
        };
        action.elem.action = action.elem.root.fxChildren('action');
        this.elem.actions.append(action.elem.root);
        if (!this.elem.actions.hasClass('t-has-actions')) {
            this.elem.actions.addClass('t-has-actions');
        }
    };

    /**
     * Add action to bottom of list
     *
     * @param {Object} action - Config for action
     * @param {number} action.type - Action type
     * @param {function} action.handler - Action handler for click events
     * @param {?string} action.label - Label for action
     * @param {?string} action.icon - Icon for action
     * @returns {number} - Action id
     */
    addAction(action) {
        let defaults = {
            [Base.ActionType.ADD]: {
                icon: 'remix-icon--system--add-circle-line'
            },
            [Base.ActionType.EDIT]: {
                icon: 'remix-icon--design--edit-2-line'
            },
            [Base.ActionType.REFRESH]: {
                icon: 'remix-icon--system--refresh-line'
            }
        };
        if (defaults[action.type] !== undefined) {
            action = Object.assign({disabled: false}, defaults[action.type], action);
        }
        let id = ++this.state.action_idx;
        action.id = id;
        this.state.actions.set(id, action);
        if (this.isBooted()) {
            this.renderAction(action);
        }
        return id;
    };

    /**
     * Get action config
     *
     * @param {number} id
     * @returns {ActionConfig}
     */
    getAction(id) {
        return this.state.actions.get(id);
    };

    /**
     * Find action by id and call proper handler if it isn't disabled
     *
     * @protected
     *
     * @param {number} id
     */
    handleAction(id) {
        let action = this.getAction(id);
        if (action.disabled) {
            return;
        }
        action.handler();
    };

    /**
     * Toggle state of action by id
     *
     * @param {number} id
     * @param {boolean} active
     */
    toggleAction(id, active) {
        let action = this.getAction(id);
        if (action.disabled === !active) {
            return;
        }
        action.disabled = !active;
        if (this.isBooted()) {
            action.elem.root[active ? 'removeClass' : 'addClass']('t-disabled');
        }
    };

    /**
     * Toggle state of all defined actions
     *
     * @param {boolean} active
     */
    toggleAllActions(active) {
        if (this.state.actions.size === 0) {
            return;
        }
        for (let id of this.state.actions.keys()) {
            this.toggleAction(id, active);
        }
    };

    /**
     * Delete action by id
     *
     * @param {number} id
     */
    deleteAction(id) {
        let action = this.getAction(id);
        if (action === undefined) {
            return;
        }
        // if rendered, then remove from DOM
        if (action.elem !== undefined) {
            action.elem.root.remove();
        }
        this.state.actions.delete(id);
        if (this.state.actions.size === 0) {
            this.elem.actions.removeClass('t-has-actions');
        }
    };

    /**
     * Delete list
     */
    delete() {
        if (this.isBooted()) {
            this.elem.root.remove();
        }
        this.state.events.emit('deleted', {
            list: this
        });
    };

    /**
     * Determines if list is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot list
     *
     * @param {jQuery} container - jQuery element which holds the rendered list
     */
    boot(container) {
        this.elem.container = container;
        this.elem.root = container.fxChildren('list', {id: this.state.id});
        if (this.elem.root.length === 0) {
            throw new Error('Unable to find list element');
        }
        this.elem.no_items = this.elem.root.fxChildren('no-items');
        this.elem.working = this.elem.root.fxChildren('working');
        this.elem.items = this.elem.root.fxChildren('items');
        this.elem.actions = this.elem.root.fxChildren('actions');

        if (this.state.config.sortable) {
            Sortable.create(this.elem.items[0], {
                onSort: () => {
                    this.handleReorder();
                }
            });
        }

        this.toggleNoItems(this.state.items.size === 0);

        // render items
        if (this.state.items.size > 0) {
            for (let item of this.state.items.values()) {
                this.renderItem(item);
            }
        }

        // render actions
        if (this.state.actions.size > 0) {
            for (let action of this.state.actions.values()) {
                this.renderAction(action);
            }
        }

        // setup event watchers
        const that = this;
        switch (this.state.config.display_type) {
            case Base.DisplayType.MEDIA:
                this.elem.items.fxClickWatcher('thumbnail', function (e) {
                    e.preventDefault();
                    that.handleItemPreview(parseInt($(this).fxParents('item').data('id')));
                    return false;
                });
                break;
        }
        this.elem.actions.fxClickWatcher('action', function (e) {
            e.preventDefault();
            that.handleAction(parseInt($(this).data('id')));
            return false;
        });

        this.state.booted = true;
    };

    /**
     * Determines if list is rendered
     *
     * @returns {boolean}
     */
    isRendered() {
        return this.state.rendered;
    };

    /**
     * Render list
     *
     * @returns {string}
     */
    render() {
        this.state.rendered = true;

        return list_tpl({
            id: this.state.id,
            no_items_text: this.state.config.no_items_text,
            working_text: this.state.config.working_text,
            sortable: this.state.config.sortable
        });
    };
}

Base.__idx = 0;

module.exports = Base;

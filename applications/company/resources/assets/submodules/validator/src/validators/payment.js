'use strict';

const Parsley = require('parsleyjs');
const valid = require('card-validator');

const digits = v => (v || '').replace(/\D/g, '');

// Export card type detection utility
export const getCardType = (cardNumber) => {
    const result = valid.number(digits(cardNumber));
    return result.card || null;
};

const validators = {
    credit: {
        requirementType: 'string',
        validateString: v => valid.number(digits(v)).isValid,
        messages: {
            en: 'Credit card number is not valid.'
        }
    },
    cvv: {
        requirementType: 'number',
        validateString: (v, len = 3) => valid.cvv(digits(v), len).isValid,
        messages: { en: 'Invalid CVV.' }
    },
    expiration: {
        requirementType: 'boolean',
        validateString: value => {
            const [month, year] = value.split('/').map(Number);
            const fullYear = year < 100 ? 2000 + year : year;
            return month >= 1 && month <= 12 && new Date(fullYear, month - 1) > new Date();
        },
        messages: {
            en: 'Credit card expiration must be in the future.'
        }
    },
    routing: {
        requirementType: 'boolean',
        validateString: function(routing) {
            let sum = 0,
                mod = 0,
                len = routing.length;
            if (len === 9) {
                sum = (
                    3 * (parseInt(routing.charAt(0)) + parseInt(routing.charAt(3)) + parseInt(routing.charAt(6))) +
                    7 * (parseInt(routing.charAt(1)) + parseInt(routing.charAt(4)) + parseInt(routing.charAt(7))) +
                    (parseInt(routing.charAt(2)) + parseInt(routing.charAt(5)) + parseInt(routing.charAt(8)))
                );
                mod = sum % 10;
            }
            return mod === 0 && len === 9;
        },
        messages: {
            en: 'Routing number is not correct.'
        }
    },
    ach_account: {
        requirementType: 'boolean',
        validateString: v => {
            const n = digits(v);
            return n.length >= 4 && n.length <= 17;
        },
        messages: { en: 'Account number must be 4–17 digits.' }
    },

    currency: {
        requirementType: 'boolean',
        validateString: v => /^\d+(\.\d{1,2})?$/.test((v || '').trim()),
        messages: { en: 'Please enter a valid amount (e.g., 100.00).' }
    }
};

Object.keys(validators).forEach(validator => Parsley.addValidator(validator, validators[validator]));

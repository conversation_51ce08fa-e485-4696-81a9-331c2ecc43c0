'use strict';

import moment from 'moment-timezone';

import Api from '@ca-package/api';
import Page from '@ca-package/router/src/page';
import {Base as Table} from '@ca-submodule/table';

const Cookie = require('@cac-js/utils/cookie');

import {Delete} from './manager-pages/delete';
import {Details} from './manager-pages/details';
import {Create} from './manager-pages/create';
import {Edit} from './manager-pages/edit';
import {Complete} from './manager-pages/complete';
import {Open} from './manager-pages/open';

import manager_tpl from '@cam-task-tpl/pages/main-pages/manager.hbs';
import {object} from "mathjs/lib/utils";

export class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            page_name: 'task',
            version: 'v1',
            saved_scope: null,
            saved_table_order: null,
            saved_table_visibility: null,
            table: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    due_date: Table.Sort.ASC
                },
                filters: {
                    status: [Table.Operators.EQUAL, Api.Constants.Tasks.Status.ACTIVE]
                }
            }
        });
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            create: {
                path: '/create',
                modal: Create
            },
            edit: {
                path: '/edit/{task_id}',
                modal: Edit,
                bindings: {
                    task_id: 'uuid'
                }
            },
            complete: {
                path: '/complete/{task_id}',
                modal: Complete,
                bindings: {
                    task_id: 'uuid'
                }
            },
            open: {
                path: '/open/{task_id}',
                modal: Open,
                bindings: {
                    task_id: 'uuid'
                }
            },
            delete: {
                path: '/delete/{task_id}',
                modal: Delete,
                bindings: {
                    task_id: 'uuid'
                }
            },
            details: {
                path: '/details/{task_id}',
                modal: Details,
                bindings: {
                    task_id: 'uuid'
                }
            }
        };
    };

    /**
     * Define the default settings for customer DataTable
     *
     * @returns {Object}
     */
    tableSettings() {
        return {
            server_paginate: false,
            load_tooltip: false,
            use_table_settings: true,
            column_order: this.state.saved_table_order,
            column_visibility: this.state.saved_table_visibility
        };
    };

    /**
     * Create the Tasks DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table, this.tableSettings())
            .on('row_click', (data) => {
                this.router.navigate('manager.details', {
                    task_id: data.id,
                });
            })
            .on('scope_change', (scope) => {
                this.state.table_scope = scope;
                let url_scope = Table.buildUrlFromScope(this.state.table_scope);

                // set cookie with scope to expire after an hour
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_scope`, url_scope.replace('?', ''), Cookie.expirationTypes().Hours, 1);
                window.history.replaceState(null, '', window.location.href.split('?')[0]+url_scope);
            }).on('table_settings_changed', (config) => {
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_order`, config.order.toString());
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_visibility`, config.visibility.toString());
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Tasks'
        });

        this.state.table.setFilterOptions({
            status: {
                label: 'Status',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'Open',
                        value: Api.Constants.Tasks.Status.ACTIVE
                    },
                    2: {
                        label: 'Completed',
                        value: Api.Constants.Tasks.Status.COMPLETED
                    }
                }
            },
            type: {
                label: 'Type',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'To Do',
                        value: Api.Constants.Tasks.Type.TODO
                    },
                    2: {
                        label: 'Call',
                        value: Api.Constants.Tasks.Type.CALL
                    },
                    3: {
                        label: 'Email',
                        value: Api.Constants.Tasks.Type.EMAIL
                    },
                    4: {
                        label: 'Text',
                        value: Api.Constants.Tasks.Type.TEXT
                    },
                    5: {
                        label: 'Visit',
                        value: Api.Constants.Tasks.Type.VISIT
                    }
                }
            },
            priority: {
                label: 'Priority',
                type: Table.FilterValueTypes.SELECT,
                options: {
                    1: {
                        label: 'High',
                        value: Api.Constants.Tasks.Priority.HIGH
                    },
                    2: {
                        label: 'Medium',
                        value: Api.Constants.Tasks.Priority.MEDIUM
                    },
                    3: {
                        label: 'Low',
                        value: Api.Constants.Tasks.Priority.LOW
                    }
                }
            },
            association_type: {
                label: 'Association Type',
                type: Table.FilterValueTypes.SELECT,
                options: {
                    1: {
                        label: 'Customer',
                        value: Api.Constants.Tasks.AssociationType.CUSTOMER
                    },
                    2: {
                        label: 'Property',
                        value: Api.Constants.Tasks.AssociationType.PROPERTY
                    },
                    3: {
                        label: 'Project',
                        value: Api.Constants.Tasks.AssociationType.PROJECT
                    },
                    4: {
                        label: 'Lead',
                        value: Api.Constants.Tasks.AssociationType.LEAD
                    },
                }
            },
            due_date: {
                label: 'Due Date',
                type: Table.FilterValueTypes.DATE,
            },
            assigned_to_user_id: {
                label: 'Assigned To',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: task_data.filter_options.all_users
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            created_by_user_id: {
                label: 'Created By',
                type: Table.FilterValueTypes.SELECT,
                options: task_data.filter_options.all_users
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_by_user_id: {
                label: 'Updated By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: task_data.filter_options.all_users
            },
            completed_at: {
                label: 'Completed Date',
                type: Table.FilterValueTypes.DATE,
            },
            completed_by_user_id: {
                label: 'Completed By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: task_data.filter_options.all_users
            }
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        let status_map = new Map([
            [Api.Constants.Tasks.Status.ACTIVE, '<span class="h-text t-blue">Open</span>'],
            [Api.Constants.Tasks.Status.COMPLETED, '<span class="h-text t-green">Completed</span>']
        ]);

        let type_map = new Map([
            [Api.Constants.Tasks.Type.TODO, 'To Do'],
            [Api.Constants.Tasks.Type.CALL, 'Call'],
            [Api.Constants.Tasks.Type.EMAIL, 'Email'],
            [Api.Constants.Tasks.Type.TEXT, 'Text'],
            [Api.Constants.Tasks.Type.VISIT, 'Visit']
        ]);

        let priority_map = new Map([
            [Api.Constants.Tasks.Priority.LOW, '<span class="h-text t-blue">Low</span>'],
            [Api.Constants.Tasks.Priority.MEDIUM, '<span class="h-text t-yellow">Medium</span>'],
            [Api.Constants.Tasks.Priority.HIGH, '<span class="h-text t-red">High</span>']
        ]);

        let association_type_map = new Map([
            [Api.Constants.Tasks.AssociationType.CUSTOMER, 'Customer'],
            [Api.Constants.Tasks.AssociationType.PROPERTY, 'Property'],
            [Api.Constants.Tasks.AssociationType.PROJECT, 'Project'],
            [Api.Constants.Tasks.AssociationType.LEAD, 'Lead']
        ]);

        // set columns config
        this.state.table.setColumns({
            due_date: {
                label: 'Due Date',
                value: (data) => {
                    if (data.due_date === null) {
                        return null;
                    }
                    let date = data.due_date,
                        current_date = moment().tz(this.parent.layout.user.timezone),
                        due_date = moment(date).tz(this.parent.layout.user.timezone);
                    if (due_date.isSameOrBefore(current_date)) {
                        return `<span class="t-red">${due_date.format('MM/DD/YYYY h:mm a')}</span>`;
                    }
                    return due_date.format('MM/DD/YYYY h:mm a');
                }
            },
            association_type: {
                label: 'Association',
                value: (data) => {
                    if (data.association_type === null) {
                        return;
                    }
                    let value = association_type_map.get(data.association_type);
                    switch(data.association_type) {
                        case Api.Constants.Tasks.AssociationType.CUSTOMER:
                            value = `${value}: ${data.association.first_name} ${data.association.last_name}`;
                            break;
                        case Api.Constants.Tasks.AssociationType.PROPERTY:
                            value = `${value}: ${data.association.address}`;
                            break;
                        case Api.Constants.Tasks.AssociationType.PROJECT:
                            value = `${value}: ${data.association.description}`;
                            break;
                        case Api.Constants.Tasks.AssociationType.LEAD:
                            value = `${value}: ${data.association.first_name} ${data.association.last_name}`;
                            break;
                    }
                    if (value.length > 30) {
                        value = `${value.substring(0, 25)}...`;
                    }
                    return value;
                }
            },
            title: {
                label: 'Title',
                value: (data) => {
                    return this.state.table.trimColumn(data.title, 50, true, true);
                }
            },
            priority: {
                label: 'Priority',
                value: data => priority_map.get(data.priority)
            },
            type: {
                label: 'Type',
                value: data => type_map.get(data.type)
            },
            assigned_to_user_name: {
                label: 'Assigned To'
            },
            status: {
                label: 'Status',
                value: data => status_map.get(data.status)
            },
            completed_at: {
                label: 'Completed',
                value: (data) => {
                    let date = data.completed_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            completed_by_user_name: {
                label: 'Completed By'
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            created_by_user_name: {
                label: 'Created By',
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_by_user_name: {
                label: 'Updated By'
            }
        });

        // set row action config
        this.state.table.setRowActions({
            details: {
                label: 'Details',
                action: (data) => {
                    this.router.navigate('manager.details', {
                        task_id: data.id
                    });
                }
            },
            complete: {
                label: 'Complete Task',
                confirm: true,
                visible: data => data.status !== Api.Constants.Tasks.Status.COMPLETED,
                action: (data) => {
                    this.router.navigate('manager.complete', {
                        task_id: data.id
                    });
                }
            },
            open: {
                label: 'Re-Open Task',
                confirm: true,
                visible: data => data.status !== Api.Constants.Tasks.Status.ACTIVE,
                action: (data) => {
                    this.router.navigate('manager.open', {
                        task_id: data.id
                    });
                }
            },
            edit: {
                label: 'Edit',
                action: (data) => {
                    this.router.navigate('manager.edit', {
                        task_id: data.id
                    });
                }
            },
            customer_association_link: {
                label: 'View Customer',
                visible: data => data.association_type === Api.Constants.Tasks.AssociationType.CUSTOMER,
                link: {
                    href: (data) => {
                        return window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.association.id);
                    },
                    target: '_blank'
                }
            },
            // property_association_link: {
            //     label: 'View Property',
            //     visible: data => data.association_type === Api.Constants.Tasks.AssociationType.PROPERTY,
            //     link: {
            //         href: (data) => {
            //             return window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.association.customer_id);
            //         },
            //         target: '_blank'
            //     }
            // },
            project_association_link: {
                label: 'View Project',
                visible: data => data.association_type === Api.Constants.Tasks.AssociationType.PROJECT,
                link: {
                    href: (data) => {
                        return window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}', data.association.id);
                    },
                    target: '_blank'
                }
            },
            lead_association_link: {
                label: 'View Lead',
                visible: data => data.association_type === Api.Constants.Tasks.AssociationType.LEAD,
                link: {
                    href: (data) => {
                        return window.fx_pages.LEAD.replace('{lead_id}', data.association.lead_uuid);
                    },
                    target: '_blank'
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                visible: data => data.created_by_user_id === task_data.user_id || task_data.is_primary,
                action: (data) => {
                    this.router.navigate('manager.delete', {
                        task_id: data.id
                    });
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            export_csv: {
                label: 'Export',
                type_class: 't-tertiary-icon',
                icon: 'system--download-line',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/tasks' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                }
            },
            add: {
                label: 'New Task',
                action: () => {
                    this.router.navigate('manager.create');
                },
                type_class: 't-primary'
            }
        });

        this.state.table.setAjax(Api.Resources.Tasks, (request) => {
            request.accept('application/vnd.adg.fx.collection-v1+json');
        });
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        // false means we don't want to reset paging
        this.state.table.draw(false);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.table_loaded) {
            // false means we don't want to reset paging
            this.state.table.draw(false);
        } else {
            // if request query contains scope we use it
            if (Object.keys(request.query).length > 0) {
                this.state.table_scope = Table.buildScopeFromQuery(request.query);
            // otherwise we get it from the cookie
            } else if (this.state.saved_scope !== null) {
                this.state.table_scope = Table.buildScopeFromQuery(this.state.saved_scope);
            }
            // otherwise we pull from the default scope stored in the state
            this.state.table.setState(this.state.table_scope);
            this.state.table.build();
            this.state.table_loaded = true;
        }
        await super.load(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.state.saved_scope = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_scope`);

        let column_order = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_order`);
        if (column_order !== null) {
            this.state.saved_table_order = column_order.split(',');
        }

        let column_visibility = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_visibility`);
        if (column_visibility !== null) {
            this.state.saved_table_visibility = column_visibility.split(',');
        }

        this.createTable();
    };

    /**
     * Render page
     */
    render() {
        return manager_tpl();
    };
}

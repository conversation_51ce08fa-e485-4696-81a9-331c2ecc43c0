<form class="m-edit-uploaded-file-modal" data-js="form">
    <div class="f-field">
        <label class="f-f-label">Name</label>
        <input class="f-f-input" type="text" data-js="name"/>
    </div>
    <div class="f-field">
        <label class="f-f-label">Description <span class="f-fl-optional">(Optional)</span></label>
        <input class="f-f-input" type="text" data-js="description"/>
    </div>
    <div class="c-eufm-marker-wrapper">
        <div class="c-eufm-mw-toolbar">
            <div class="c-eufm-mw-t-left">
                <div class="c-eufm-mw-tl-button t-color-picker">
                   <input type="text" data-input-type="color" data-js="color">
                </div>
                <div class="c-eufm-mw-tl-button" data-js="editor" data-id="freehand">
                    <svg data-icon><use xlink:href="#remix-icon--design--ball-pen-line"></use></svg>
                </div>
                <div class="c-eufm-mw-tl-button" data-js="editor" data-id="arrow">
                    <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-right-up-line"></use></svg>
                </div>
                <div class="c-eufm-mw-tl-button" data-js="editor" data-id="text">
                    <svg data-icon><use xlink:href="#remix-icon--editor--text"></use></svg>
                </div>
                <div class="c-eufm-mw-tl-button t-line-wrapper" data-js="editor" data-id="line">
                    <div class="t-line-icon"></div>
                </div>
                <div class="c-eufm-mw-tl-button" data-js="editor" data-id="rectangle">
                    <svg data-icon><use xlink:href="#remix-icon--design--square-line"></use></svg>
                </div>
                <div class="c-eufm-mw-tl-button" data-js="editor" data-id="ellipsees">
                    <svg data-icon><use xlink:href="#remix-icon--design--circle-line"></use></svg>
                </div>
            </div>
            <div class="c-eufm-mw-t-right">
                <div class="c-eufm-mw-tr-button" data-js="editor" data-id="select">
                    <svg data-icon><use xlink:href="#remix-icon--development--cursor-line"></use></svg>
                </div>
                <div class="c-eufm-mw-tr-button" data-js="editor" data-id="delete">
                    <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-line"></use></svg>
                </div>
                <div class="c-eufm-mw-tr-button" data-js="editor" data-id="undo">
                    <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-go-back-line"></use></svg>
                </div>
                <div class="c-eufm-mw-tr-button" data-js="editor" data-id="redo">
                    <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-go-forward-line"></use></svg>
                </div>
            </div>
        </div>
        <div class="c-eufm-mw-image" data-js="marker-container"></div>
    </div>
    <div class="c-eufm-actions">
        <a class="c-eufm-a-delete" data-js="delete-image">Delete Image</a>
    </div>
</form>
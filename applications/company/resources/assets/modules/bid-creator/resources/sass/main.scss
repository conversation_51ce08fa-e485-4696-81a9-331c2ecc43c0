@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/button/text';
@use '~@cac-sass/app/grid';
@use '~@cas-form-input-sass/button-group';
@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/color';
@use '~@cas-form-input-sass/file';
@use '~@cas-form-input-sass/file/webcam';
@use '~@cas-form-input-sass/number';
@use '~@cas-form-input-sass/switch';
@use '~@cas-form-input-sass/uppy';
@use '~@cas-form-input-sass/wysiwyg';
@use '~@cas-layout-sass/layout';
@use '~@cas-validator-sass/validator';
@use '~@cas-form-sass/form';
@use '~@cas-modal-sass/modal';
@use '~@cas-panel-stack-sass/panel-stack';
@use '~@cas-tooltip-sass/tooltip';
@use '~@cas-flash-message-sass/config' as flash-message-config;
@use 'config';
@use 'sass:color' as sass-color;
@use 'sass:math';

@use '~@fancyapps/fancybox/dist/jquery.fancybox';
@use '~@simonwep/pickr/src/scss/themes/monolith';

body.is-reveal-open {
    min-width: 100%;
}

html {
    -webkit-tap-highlight-color: transparent;
}

body {
    @include base.respond-to('<small') {
        background-color: base.$color-background;
    }
}

.t-hidden {
    display: none;
}

.wt-loading {
    height: 35px;
    width: 35px;
}

// header override for sub-header height
.a-a-header {
    .m-layout-dropdown-bar {
        margin-left: base.unit-rem-calc(49px);
    }
    .c-ldb-dropdown.t-top.t-show {
        height: base.unit-rem-calc(48px);
    }
        .c-ldbd-inner {
            height: base.unit-rem-calc(48px);
            margin-left: base.unit-rem-calc(-49px);
            .c-ldbdi-link {
                line-height: base.unit-rem-calc(48px);
            }
        }
}

// override foundation and global defaults
.reveal {
    border-radius: base.unit-rem-calc(12px);
    border: none;
    box-shadow: base.$elevation-level-1;
    outline: none;
}
table {
    margin: 0;
    border-radius: 0;
    thead,
    tfoot {
        background-color: transparent !important;
        color: unset;
    }
}
$border-width: base.unit-rem-calc(1px);
$top-bar-height: base.unit-rem-calc(48px);
.m-bid-creator {
    position: relative;
    @include base.full-width-height;
    .c-bc-loader {
        position: absolute;
        @include base.full-width-height;
        background: base.$color-background url('~@cac-public/images/loading_blue.svg') no-repeat center;
        background-size: base.unit-rem-calc(80px) base.unit-rem-calc(80px);
        z-index: 244;
    }
    .c-bc-messages {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 290;
        .i-fm-message {
            margin: base.unit-rem-calc(4px);
        }
    }
    .c-bc-columns {
        display: flex;
        @include base.full-width-height;
        overflow: hidden;
    }
        $nav-width: base.unit-rem-calc(48px);
        $nav-item-dimension: base.unit-rem-calc(40px);
        .c-bcc-sidebar {
            @include base.clearfix;
            z-index: 280;
            background-color: base.$color-white-default;
            box-shadow: base.$elevation-sidebar-left;
            &.t-drawer-open {
            }
            @include base.respond-to('<small') {
                box-shadow: none;
            }
        }
            .c-bccs-nav {
                float: left;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: base.unit-rem-calc(16px);
                height: 100%;
                border-right: $border-width base.$color-grey-light-4 solid;
            }
                %icon-active {
                    .c-bccsni-icon {
                        color: base.$color-primary-default;
                    }
                }
                .c-bccsn-item {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: base.unit-rem-calc(40px);
                    margin: 0 base.unit-rem-calc(4px);
                    height: $nav-item-dimension;
                    border-radius: base.unit-rem-calc(40px);
                    transition: all 0.2s ease-out;
                    .c-bccsni-icon {
                        color: base.$color-grey-light-1;
                        transition: all 0.2s ease-out;
                    }
                    @include base.respond-to('hover') {
                        &:hover {
                            background: #EDF1F7;
                        }
                    }
                    &.t-active {
                        background-color: base.$color-primary-light-4;
                        color: base.$color-primary-default;
                        @extend %icon-active;
                    }

                    &.t-menu {
                        margin: base.unit-rem-calc(4px) base.unit-rem-calc(4px) base.unit-rem-calc(5px);
                        .c-bccsni-menu {
                            width: base.unit-rem-calc(18px);
                            height: base.unit-rem-calc(16px);
                            cursor: pointer;
                                .c-bccsnim-top {
                                    display: flex;
                                    align-items: center;
                                .c-bccsnimt-lines {
                                    width: base.unit-rem-calc(9px);
                                    height: base.unit-rem-calc(9px);
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: space-between;
                                    transform: translatex(-9px);
                                    animation: ease .3s lines-1;
                                    &.t-preload {
                                        animation-duration: 0s !important;
                                        -webkit-animation-duration: 0s !important;
                                    }
                                    @keyframes lines-1 {
                                        0% {
                                            transform: translatex(0px);
                                        }
                                        100% {
                                            transform: translatex(-9px);
                                        }
                                    }
                                    span {
                                        width: base.unit-rem-calc(9px);
                                        height: base.unit-rem-calc(2px);
                                        background-color: base.$color-grey-dark-2;
                                    }
                                }
                                .c-bccsnimt-arrow {
                                    width: 9px;
                                    height: 9px;
                                    align-items: center;
                                    display: flex;
                                    justify-content: flex-end;
                                    transform: translatex(9px);
                                    animation: ease .3s arrow-1;
                                    &.t-preload {
                                        animation-duration: 0s !important;
                                        -webkit-animation-duration: 0s !important;
                                    }
                                    @keyframes arrow-1 {
                                        0% {
                                            transform:scale(1) translatex(0px) rotatey(180deg);
                                        }
                                        50% {
                                            transform: scale(0);
                                        }
                                        100% {
                                            transform: scale(1) translatex(9px);
                                        }
                                    }
                                }
                                    .c-bccsnimta-icon {
                                        width: base.unit-rem-calc(7px);
                                        height: base.unit-rem-calc(7px);
                                        border: solid base.$color-grey-dark-2;
                                        border-width: 2px 2px 0 0;
                                        transform: rotate(45deg);
                                    }
                            }
                            .c-bccsnim-bottom {
                                width: base.unit-rem-calc(18px);
                                height: base.unit-rem-calc(2px);
                                background-color: base.$color-grey-dark-2;
                                margin-top: base.unit-rem-calc(5px);
                            }
                        }
                        &.t-active {
                            border: transparent;
                            background-color: transparent;
                            .c-bccsnim-top {
                                .c-bccsnimt-lines {
                                    transform: translatex(0px);
                                    animation: ease .3s lines-2;
                                    @keyframes lines-2 {
                                        0% {
                                            transform: translatex(-9px);
                                        }
                                        100% {
                                            transform: translatex(0px);
                                        }
                                    }
                                }
                                .c-bccsnimt-arrow {
                                    transform: translatex(0px) rotatey(180deg);
                                    animation: ease .3s arrow-2;
                                    @keyframes arrow-2 {
                                        0% {
                                            transform:scale(1) translatex(9px);
                                        }
                                        50% {
                                            transform: scale(0);
                                        }
                                        100% {
                                            transform: scale(1) translatex(0px) rotatey(180deg);
                                        }
                                    }
                                }
                            }
                        }
                        &:hover {
                            border: transparent;
                            background-color: transparent;
                        }
                    }
                    &.t-pricing,
                    &.t-info,
                    &.t-terms-conditions,
                    &.t-uploads,
                    &.t-media,
                    &.t-templates,
                    &.t-section-manager, {
                        .c-bccsni-icon {
                            @include base.svg-icon('default-18');
                        }
                    }
                }
            .c-bccs-drawer {
                display: none;
                float: left;
                height: 100%;
                &.t-overlay {
                    float: none;
                    position: absolute;
                    top: 0;
                    left: $nav-width;
                    z-index: 290;
                    border-left: $border-width base.$color-grey-light-4 solid;
                    box-shadow: base.$elevation-sidebar-left;
                }
            }
        .c-bcc-content {
            position: relative;
            flex: 1;
            padding-top: $top-bar-height;
            z-index: 240;
        }
            .c-bccc-top-bar {
                display: flex;
                align-items: center;
                position: absolute;
                gap: base.unit-rem-calc(8px);
                top: 0;
                left: 0;
                width: 100%;
                height: $top-bar-height;
                padding-left: base.unit-rem-calc(20px);
                background-color: base.$color-white-default;
                box-shadow: base.$elevation-level-2;
                z-index: 115;
            }
                .c-bccctb-title {
                    flex: 1;
                    @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
                    color: base.$color-grey-dark-4;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                    .c-bccctbt-project {
                        flex: 1;
                        @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
                        color: base.$color-primary-default;
                    }
                    .c-bccctbt-save-status {
                        display: none;
                        margin-left: base.unit-rem-calc(8px);
                        @include base.typo-text($size: 13px);
                        font-weight: 400;
                        font-style: italic;
                        color: base.$color-grey-light-2;
                    }
                .c-bccctb-actions {
                    flex: 0 0 auto;
                    margin-right: base.unit-rem-calc(8px);
                }
                    .c-bccctba-group {
                        display: flex;
                        gap: base.unit-rem-calc(16px);
                    }
                        .c-bccctbag-action {
                            &.t-tertiary {
                                @include base.button-text-icon-tertiary;
                            }
                            &.t-icon {
                                @include base.button-icon-tertiary;
                            }
                            &.t-tertiary-negate {
                                @include base.button-text-tertiary('negate');
                                color: base.$color-grey-light-1 !important;
                                border-right: 1px solid base.$color-grey-light-4;
                                padding-right: base.unit-rem-calc(16px) !important;
                                border-radius: 0;
                                &:hover {
                                    color: base.$color-grey-dark-4 !important;
                                    border-right: 1px solid base.$color-grey-light-4;
                                }
                            }
                            &:last-child {
                                margin-right: 0;
                            }
                        }
            .c-bccc-panels {
                position: relative;
                width: 100%;
                height: 100%;
                overflow: hidden;
            }
                %content-panel {
                    position: absolute;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    overflow-y: auto;
                }
                .c-bcccp-builder {
                    @extend %content-panel;
                    position: relative;
                    top: 0;
                    z-index: 113;
                }
                    .c-bccpb-sections {
                        max-width: base.unit-rem-calc(1024px);
                        margin: 0 auto;
                        padding: base.unit-rem-calc(48px) base.unit-rem-calc(24px);
                        @include base.respond-to('<small') {
                            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px) base.unit-rem-calc(32px) base.unit-rem-calc(8px);
                        }
                    }
                        .c-bccpbs-inner {}
                        .c-bccpbs-add {
                            @include base.button-icon-primary;
                            margin: auto;
                            border-radius: base.unit-rem-calc(40px);
                            padding: base.unit-rem-calc(5px) base.unit-rem-calc(24px);
                        }
                    .c-bcccp-preview {
                        @extend %content-panel;
                        top: 100%;
                        background-color: base.$color-white-default;
                        z-index: 114;
                        transition: top 1s ease;
                        &.t-open {
                            top: 0;
                        }
                    iframe {
                        display: block;
                        position: relative;
                        @include base.full-width-height;
                        border: none;
                        z-index: 115;
                    }
                }
                    .c-bcccpp-loading {
                        position: absolute;
                        @include base.full-width-height;
                        background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
                        background-size: base.unit-rem-calc(80px) base.unit-rem-calc(80px);
                        z-index: 120;
                    }
}
///////////////////////////////////////////////////////
// Structures
///////////////////////////////////////////////////////
.s-sidebar-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: base.$color-white-default;
    $close-button-width: base.unit-rem-calc(32px);
    %sidebar-panel-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
        border-bottom: $border-width base.$color-grey-light-4 solid;
        padding-left: base.unit-rem-calc(16px);
        padding-right: base.unit-rem-calc(16px);
    }
    .i-sp-title {
        @extend %sidebar-panel-title;
        height: base.unit-rem-calc(49px);
    }
    .i-sp-content {
        flex: 1;
        overflow-y: auto;
        &.t-loading::before {
            content: '';
            position: absolute;
            @include base.full-width-height;
            background: rgba(255, 255, 255, 1) url('~@cac-public/images/loading_blue.svg') no-repeat center;
            background-size: base.unit-rem-calc(48px) base.unit-rem-calc(48px);
            background-position: center 20%;
            z-index: 120;
        }
    }
        .i-spc-title {
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            margin-top: base.unit-rem-calc(24px);
        }
    .i-spt-action {
        @include base.button-text-icon-tertiary;
    }
}
.s-sidebar-accordion {
    .i-sa-no-items {
        @extend %sidebar-list-text;
        padding: base.unit-rem-calc(8px);
        min-height: base.unit-rem-calc(40px);
        color: base.$color-grey-dark-1;
        font-style: italic;
        border: $border-width solid #E1E8F060;
        border-radius: base.unit-rem-calc(8px);
        background-color: base.$color-background-form;
    }
    .i-sa-items {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
    }
        $accordion-title-height: base.unit-rem-calc(50px);
        $accordion-arrow-padding: base.unit-rem-calc(30px);
        $arrow-width: base.unit-rem-calc(8px);
        $arrow-height: base.unit-rem-calc(12px);
        .i-sai-item {
            border: base.unit-rem-calc(1px) solid #E1E8F060;
            border-radius: base.unit-rem-calc(8px);
            overflow: hidden;
            background-color: base.$color-background-form;
            transition: all 0.3s ease;
            &.t-open {
                background-color: base.$color-background-edit;
                border-color: base.$color-grey-light-4;
                .i-saii-title {
                    color: base.$color-primary-default;
                    background-color: base.$color-primary-light-4;
                    .i-saiit-arrow {
                        transform: rotate(180deg);
                        color: base.$color-primary-default;
                    }
                }
            }
        }
            .i-saii-title {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                height: base.unit-rem-calc(38px);
                padding-left: base.unit-rem-calc(12x);
                @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
                padding-bottom: base.unit-rem-calc(1px);
                color: base.$color-grey-dark-4;
                cursor: pointer;
                background-color: transparent;
                transition: all 0.3s ease;
            }
                .i-saiit-pricing {
                    display: flex;
                    flex: 1;
                    gap: base.unit-rem-calc(8px);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    //this makes it so that the Pricing Section Title truncates properly inside of Flexbox.
                    min-width: 0;
                }
                .i-saiit-arrow {
                    @include base.svg-icon('default-24');
                    width: base.unit-rem-calc(32px);
                    color: base.$color-grey-light-2;
                    transition: all 0.3s ease;
                }
                .i-saiitp-title {
                    flex: 1;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    &.t-unsaved {
                        font-style: italic;
                    }
                }
                .i-saiitp-price {}
            .i-saii-panel {
                display: none;
                overflow: hidden;
                .i-sl-no-items {
                    background-color: transparent;
                    border: none;
                    min-height: base.unit-rem-calc(48px);
                }
            }
}
.s-sidebar-list {
    padding-left: base.unit-rem-calc(8px);
    &.t-no-action {
        border-bottom: none;
    }
    %sidebar-list-text {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: base.unit-rem-calc(8px) 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px);
        border-bottom: $border-width base.$color-grey-light-4 solid;
    }
    .i-sl-no-items {
        @extend %sidebar-list-text;
        color: base.$color-grey-dark-1;
        font-style: italic;
        border: base.unit-rem-calc(1px) solid #E1E8F060;
        border-radius: base.unit-rem-calc(8px);
        background-color: base.$color-background-form;
    }
    .i-sl-working {
        display: none;
        @extend %sidebar-list-text;
        justify-content: flex-end;
        color: base.$color-primary-default;
        font-style: italic;
        border-bottom: none;
        &.t-show {
            display: flex;
        }
    }
    .i-sl-wrapper {
        &.t-sortable {
            .i-slw-item {
                cursor: move;
            }
        }
    }
        $option-height: base.unit-rem-calc(32px);
        $option-action-dimension: base.unit-rem-calc(32px);
        .i-slw-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: base.unit-rem-calc(8px);
            min-height: $option-height;
            padding: base.unit-rem-calc(8px) 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px);
            border-bottom: $border-width base.$color-grey-light-4 solid;
            &:last-child {
                border-bottom: none;
            }
            &.t-no-action {
                padding-right: base.unit-rem-calc(20px);
            }
            &.t-media {
                padding: base.unit-rem-calc(8px) 0;
            }
            &.t-locked {
                .i-slwi-action {
                    color: base.$color-grey-light-4;
                    cursor: default;
                }
            }
        }
            .i-slwi-label {
                display: flex;
                flex: 1;
                flex-direction: column;
                gap: base.unit-rem-calc(8px);
                .t-adjustment {
                    margin: base.unit-rem-calc(8px) 0;
                }
            }
            .i-slwil-pricing {
                display: flex;
                flex-direction: column;
                gap: base.unit-rem-calc(4px);
                margin: base.unit-rem-calc(4px) 0;
                .i-slwilp-items {
                    display: flex;
                    gap: base.unit-rem-calc(8px);
                    align-items: end;
                    &.t-name {
                        justify-content: space-between;
                    }
                    .i-slwilp-icons {
                        margin-right: base.unit-rem-calc(-33px);
                    }
                    .i-slwilpi-materials {
                        @include base.svg-icon('default-18');
                        color: base.$color-green-light-1;
                    }
                    .i-slwilpi-price-overwritten {
                        @include base.svg-icon('default-18');
                        color: base.$color-grey-light-3;
                    }
                }
            }
            .i-slwilp-quantity {
                flex: 1;
            }
            .i-slwilp-total {
                font-weight: 500;
                color: base.$color-primary-default;
                cursor: pointer;
                &:hover {
                    color: base.$color-primary-light-1;
                }
            }
            .i-slwi-media {
                display: flex;
                gap: base.unit-rem-calc(8px);
                align-items: center;
            }
                $thumbnail-dimension: base.unit-rem-calc(24px);
                .i-slwim-thumbnail {
                    display: block;
                    flex: 0 0 auto;
                    width: $thumbnail-dimension;
                    height: $thumbnail-dimension;
                    border-radius: base.unit-rem-calc(3px);
                    border: $border-width base.$color-grey-light-4 solid;
                    background: base.$color-white-default;
                    overflow: hidden;
                }
                    .i-slwimt-image {
                        display: block;
                        width: $thumbnail-dimension;
                        height: $thumbnail-dimension
                    }
                .i-slwim-name {
                    flex: 1;
                }
                .i-slwi-action {
                    width: base.unit-rem-calc(32px);
                    padding: base.unit-rem-calc(6px);
                    @include base.button-icon-tertiary;
                    &.t-edit {
                        margin-top: auto;
                    }
                    &.t-negate {
                        padding: base.unit-rem-calc(6px) !important;
                        @include base.button-icon-tertiary('negate');
                    }
                    &.t-radio {
                        color: base.$color-grey-light-3;
                        order: -1;
                        &:hover {
                            color: base.$color-primary-light-1;
                        }
                        &.t-selected {
                            color: base.$color-primary-default;
                        }
                    }
                    &.t-check {
                        color: base.$color-grey-light-2;
                        &:hover {
                            color: base.$color-primary-light-1;
                        }
                        &.t-selected {
                            color: base.$color-primary-default;
                        }
                    }
                }
    .i-sl-actions {
        display: none;
        &.t-has-actions {
            display: flex;
            justify-content: right;
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(7px);
            border-top: $border-width base.$color-grey-light-4 solid;
        }
    }
        .i-sla-action {
            @include base.button-text-icon-tertiary;
            &.t-disabled {
                color: base.$color-grey-light-4;
                cursor: not-allowed;
            }
        }
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Local Modules (only available within bid creator module)
//////////////////////////////////////////////////////////////////////////////////////////////////////////////
.m-bid-creator {
    .m-section {
        position: relative;
        margin-bottom: base.unit-rem-calc(40px);
        background-color: base.$color-white-default;
        border-radius: base.unit-rem-calc(12px);
        box-shadow: base.$elevation-level-2;
        overflow: hidden;
        &:last-child {
            margin-bottom: base.unit-rem-calc(32px);
            @include base.respond-to('<small') {
                margin-bottom: base.unit-rem-calc(24px);
            }
        }
        @include base.respond-to('<small') {
            margin-bottom: base.unit-rem-calc(24px);
        }
        .c-s-messages {}
        .c-s-header {
            display: flex;
            align-items: center;
            padding: base.unit-rem-calc(12px);
            border-bottom: $border-width base.$color-grey-light-4 solid;
            gap: base.unit-rem-calc(8px);
        }
            .c-sh-text {
                flex: 1;
                padding-left: base.unit-rem-calc(4px);
                word-break: break-word;
                @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
                &.t-empty {
                    color: base.$color-grey-light-2;
                    font-style: italic;
                }
                @include base.respond-to('<640px') {
                    @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
                }
            }
            .c-sh-edit-action {
                @include base.button-text-icon-tertiary;
            }
        .c-s-form-action-menu {
            display: none;
            background-color: base.$color-white-default;
            border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
            border-radius: base.unit-rem-calc(8px);
            box-shadow: base.$elevation-level-3;
            padding: base.unit-rem-calc(4px);
            margin-top: base.unit-rem-calc(4px);
            margin-right: base.unit-rem-calc(5px);
        }
            .c-sfam-action {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                color: base.$color-red-default;
                height: base.unit-rem-calc(32px);
                border-radius: base.unit-rem-calc(4px);
                padding: 0 base.unit-rem-calc(8px);
                transition: all 0.2s ease-out;
                .c-sfama-icon {
                    @include base.svg-icon('default-18');
                    color: base.$color-red-default;
                }
                &:hover {
                    background-color: base.$color-red-light-4;
                }
            }
        .c-s-forms {
            padding: 0 base.unit-rem-calc(24px);
            @include base.respond-to('<small') {
                padding: 0 base.unit-rem-calc(16px);
            }
        }
            .c-sf-form {
                padding: base.unit-rem-calc(16px) 0 base.unit-rem-calc(32px);
                border-top: $border-width base.$color-grey-light-4 solid;
                &:first-child {
                    border-top: none;
                }
                &:last-child {
                    padding-bottom: base.unit-rem-calc(24px);
                }
                @include base.respond-to('<small') {
                    &:last-child {
                        padding-bottom: base.unit-rem-calc(8px);
                    }
                }
            }
                .c-sff-messages {
                    .s-flash-messages.t-has-message {
                        margin-bottom: base.unit-rem-calc(20px);
                    }
                }
                .c-sff-header {
                    display: flex;
                    align-items: center;
                    padding-bottom: base.unit-rem-calc(24px);
                }
                    .c-sffh-text {
                        flex: 1;
                        @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
                        @include base.respond-to('<small') {
                            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px))
                        }
                    }
                    .c-sffh-action-menu {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: base.unit-rem-calc(32px);
                        height: base.unit-rem-calc(32px);
                        border-radius: base.unit-rem-calc(32px);
                        transition: all 0.2s ease-out;
                        &:hover {
                            background-color: base.$color-primary-light-4;
                            .c-sffham-icon {
                                color: base.$color-primary-default;
                            }
                        }
                    }
                        .c-sffham-icon {
                            @include base.svg-icon('default-18');
                            color: base.$color-grey-light-2;
                            transition: all 0.2s ease-out;
                        }
                .c-sff-content {}
    }
    .m-sidebar-section-manager {
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
    }
    .m-sidebar-pricing {
        padding: base.unit-rem-calc(16px) 0;
        .c-sp-group {
            padding: 0 base.unit-rem-calc(8px);
        }
        .c-sp-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px);
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
        }
        .c-spt-label {
            text-decoration: underline;
        }
        .c-spt-price {
            padding-right: base.unit-rem-calc(32px);
        }
        .c-spt-total {
            height: base.unit-rem-calc(32px);
            padding: 0 base.unit-rem-calc(32px);
            background-color: base.$color-green-default;
            border-radius: base.unit-rem-calc(32px);
            color: base.$color-white-default;
        }
        .c-sp-payment-terms {
            padding-bottom: base.unit-rem-calc(8px);
            .s-sidebar-list {
                border-top: $border-width base.$color-grey-light-4 solid;
                border-bottom: $border-width base.$color-grey-light-4 solid;
                .i-sl-no-items {
                    background-color: transparent;
                    border: none;
                    min-height: base.unit-rem-calc(48px);
                }
                .i-sl-actions {
                    padding-right: base.unit-rem-calc(16px);
                }
                .i-slw-item {
                    padding-right: base.unit-rem-calc(8px);
                }
            }
        }

        .project-financing-section {
            margin-top: 1.5em;
        }

        .c-sp-financing-callout {
            margin: 12px;
            font-style: italic;
            @include base.callout-info;

            .c-spc-title {
                font-family: "Barlow", "Roboto", Arial, sans-serif;
                font-size: 1rem;
                font-weight: 600;
                line-height: 2rem;
                padding: 0.5em;
            }
        }
        .c-sp-financing-callout-enabled {
            padding: 0 1em;
            margin:1em 0em;

            .c-sp-title {
                font-size: 14px;
                font-weight: 500;
            }

            .m-tooltip-info {
                margin-left: 0.5em;
                vertical-align: sub!important;
            }
        }


        .c-sp-financing {
            margin: 0.25em;
            padding: 0.25em;
            font-size: 1em;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #E1E8F0;


            .c-spc-title {
                font-family: "Barlow", "Roboto", Arial, sans-serif;
                font-size: 1rem;
                font-weight: 600;
                line-height: 2rem;
                padding: 0.5em;
            }

            .c-spt-project-financing {
                height: base.unit-rem-calc(24px);
                padding: 0 base.unit-rem-calc(24px);
                border-radius: base.unit-rem-calc(32px);
                color: base.$color-white-default;
                display: flex;
                justify-content: center;
                align-items: center;

                &.enabled {
                    background-color: base.$color-green-default;
                }

                &.disabled {
                    background-color: base.$color-grey-default;
                }
            }
        }

        .c-sp-financing-link {
            font-size: 0.9em;
            max-height: 20px;
            padding: 0 1em;
            margin: 1em 0;

            .link-red, .link {
                max-height: 24px;
                display: flex;
                justify-content: right;
                gap: 1em;
            }


            svg {
                max-height: 18px;
                max-width: 18px;
                display: block;
                fill: base.$color-primary-default;
            }


            .link-red {
                color: base.$color-red-default!important;
                svg {
                    fill: base.$color-red-default!important;
                }
            }
        }
        .c-sp-financing-promo {
            display: flex;
            margin-top: 1em;
            justify-content: center;
        }
    }
    .m-sidebar-form {
        height: 100%;
        &.t-includes-actions {
            .c-sf-input {
                height: calc(100% - 49px - 57px); // subtract the buttons height and the actions height
            }
        }
        .c-sf-input {
            padding: base.unit-rem-calc(16px);
            height: calc(100% - 57px); // subtract the buttons height
            overflow: auto;
        }
        .c-sfi-form {
            width: 100%;
            padding-bottom: base.unit-rem-calc(16px);
            .s-grid {
                display: flex;
                flex-direction: column;
                gap: base.unit-rem-calc(16px);
                .i-g-row {
                    width: auto;
                    padding-right: 0;
                    margin: 0;
                    .i-gr-column {
                        padding-right: 0;
                    }
                    &.t-unit-price {
                        display: flex;
                        flex-direction: column;
                        gap: base.unit-rem-calc(16px);
                        padding: 0 base.unit-rem-calc(16px) base.unit-rem-calc(24px);
                        margin: 0 base.unit-rem-calc(-16px);
                        border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                    }
                }
            }
        }
        .c-sfi-form[data-type="fee"], [data-type="discount"] {
            .i-g-row {
                margin: 0;
            }
            .c-sfif--adjustments {
                padding: 0 base.unit-rem-calc(16px) base.unit-rem-calc(24px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                margin: 0 base.unit-rem-calc(-16px);
            }
        }
        .c-sfif--component {
            display: none;
        }
        .c-sfif--quantity-unit {
            @include base.typo-text($size: 12px);
            font-weight: 400;
            font-style: italic;
            text-transform: capitalize;
            margin-left: base.unit-rem-calc(4px);
            line-height: base.unit-rem-calc(16px);
        }
        .c-sfif--adjustments {
            display: flex;
            flex-wrap: wrap;
            column-gap: base.unit-rem-calc(8px);
            row-gap: 0;
            .t-label {
                width: 100%;
            }
            .f-f-input-number {
                flex: 1;
            }
            .f-f-button-group {
                width: base.unit-rem-calc(58px);
                .f-fbg-button {
                    padding: 0;
                    font-family: 'Roboto', sans-serif;
                    font-weight: 500;
                    max-width: base.unit-rem-calc(26px);
                }
            }
        }
        .c-sfif--total {
            display: flex;
            padding: 0 base.unit-rem-calc(8px);
            margin: 0 base.unit-rem-calc(-8px);
        }
            .c-sfif--t-title {
                flex: 1;
            }
            .c-sfif--t-total {}
        .c-sfif--total-override {
            display: none;
        }
        .c-sfif--component-price-adjustment {
            display: none;
            .c-sfif--cpa-switch {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
        }
        .c-sfif--to-header {
            @include base.callout-warning;
            display: flex;
            justify-content: center;
            margin: base.unit-rem-calc(8px) 0 base.unit-rem-calc(16px);
            .t-price {
                display: none;
            }
            .t-components {
                display: none;
            }
            .t-price-components {
                display: none;
                ul {
                    margin-bottom: 0;
                    margin-left: base.unit-rem-calc(16px);
                }
            }
        }
        .c-sfif--to-line-group {
            display: none;
        }
        .c-sfif--to-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: base.$color-grey-light-1;
            height: base.unit-rem-calc(32px);
            @include base.typo-text($size: 14px);
            &.t-adjustment {
                color: base.$color-primary-default;
                &.t-positive {
                    color: base.$color-green-default;
                }
                &.t-negative {
                    color: base.$color-red-default;
                }
            }
            &.t-adjusted-total {
                color: base.$color-grey-dark-4;
                border-top: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            }
        }
        .c-sfif--to-total {
            display: flex;
            justify-content: space-between;
            margin: base.unit-rem-calc(8px) 0 base.unit-rem-calc(24px);
        }
        .c-sfiftt-results {
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
        }
        .c-sfif--to-reset {
            justify-content: flex-end;
            display: none;
            &.t-show {
                display: flex;
                margin-bottom: base.unit-rem-calc(-8px);
            }
            .c-sfiftr-action {
                @include base.button-text-icon-tertiary;
            }
        }
        .i-sf-actions {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: base.unit-rem-calc(8px);
            border-top: $border-width base.$color-grey-light-4 solid;
            .i-sfaa-button {
                @include base.button-text-tertiary('negate');
            }
        }
        .c-sf-buttons {
            display: flex;
            justify-content: right;
            gap: base.unit-rem-calc(16px);
            border-top: $border-width base.$color-grey-light-4 solid;
            padding: base.unit-rem-calc(12px);
        }
    }
    .m-components {
        .c-c-product {
            display: flex;
            flex-direction: column;
            background-color: base.$color-background-edit;
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(8px);
            overflow: hidden;
            margin: 0 base.unit-rem-calc(-8px);
            transition: all 0.3s ease;
            &.t-hide {
                height: base.unit-rem-calc(40px);
                border-color: #E1E8F060;
            }
                .c-cp-header {
                    display: flex;
                    flex: 1;
                    align-items: center;
                    padding-left: base.unit-rem-calc(12px);
                    min-height: base.unit-rem-calc(40px);
                    background-color: base.$color-primary-light-4;
                    transition: all 0.3s ease;
                    &.t-close {
                        background-color: base.$color-background-form;
                        .c-cp-title {
                            color: base.$color-grey-dark-4;
                        }
                        .c-cp-button {
                            color: base.$color-grey-light-2;
                            transform: rotate(180deg);
                        }
                    }
                }
        }
            .c-cp-title {
                color: base.$color-primary-default;
                @include base.typo-paragraph-medium;
                flex: 1;
                transition: all 0.3s ease;
            }
            .c-cp-button {
                @include base.svg-icon('default-24');
                color: base.$color-primary-default;
                margin: base.unit-rem-calc(4px);
                transition: all 0.3s ease;
            }
        .c-cp-component {
            padding: base.unit-rem-calc(8px) 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px);
        }
        .c-cc-header {
            display: flex;
            align-items: center;
            padding: 0 base.unit-rem-calc(7px);
        }
            .c-cch-title {
                flex: 1;
            }
            .c-cch-reset {
                @include base.button-text-icon-tertiary;
                display: none;
                &.t-show {
                    display: inline-flex;
                }
            }
        .c-cc-section {}
            .c-ccs-header {
                display: flex;
                align-items: center;
                padding: base.unit-rem-calc(6px) base.unit-rem-calc(8px) 0;
                gap: base.unit-rem-calc(8px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            }
                .c-ccsh-title {
                    flex: 1px;
                }
                .c-ccsh-quantity {
                    width: base.unit-rem-calc(60px);
                }
            .c-ccs-body {
                display: flex;
                flex-direction: column;
                gap: base.unit-rem-calc(16px);
                padding: base.unit-rem-calc(16px) 0;
            }
                .c-ccsb-item {
                    display: flex;
                    gap: base.unit-rem-calc(8px);
                    align-items: center;
                    padding-right: base.unit-rem-calc(8px);
                }
                    .c-ccsbi-remove {
                        @include base.button-icon-tertiary('negate');
                    }
                    .c-ccsbi-option {
                        flex: 1;
                        width: base.unit-rem-calc(160px);
                        padding-right: base.unit-rem-calc(8px);
                    }
                    .c-ccsbi-title {
                        flex: 1;
                        padding: base.unit-rem-calc(6px) base.unit-rem-calc(8px);
                        justify-content: space-between;
                        display: flex;
                        align-items: center;
                        .m-tooltip-info {
                            margin-left: base.unit-rem-calc(8px);
                        }
                    }
                        .c-ccsbit-name {
                            width: base.unit-rem-calc(150px);
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                    .c-ccsbi-quantity {
                        width: base.unit-rem-calc(62px);
                    }
            .c-ccs-footer {
                display: flex;
                justify-content: flex-end;
            }
                .c-ccsf-add {
                    @include base.button-text-icon-tertiary;
                    padding-right: base.unit-rem-calc(7px) !important;
                }
    }
    .m-sidebar-terms-conditions {
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
    }
    .m-sidebar-uploads {
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
        .i-spc-title {
            margin-bottom: 0;
        }
    }
    .m-sidebar-media {
        .s-sidebar-list {
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
            .i-sl-wrapper {
                &.t-sortable {
                    display: flex;
                    flex-direction: column;
                    min-height: base.unit-rem-calc(48px);
                    border: 1px solid #E1E8F060;
                    border-radius: base.unit-rem-calc(8px);
                    background-color: base.$color-background-form;
                }
            }
        }
    }
    .m-sidebar-info {
        padding: base.unit-rem-calc(16px) 0;
        .c-si-details {
            padding: 0 0 base.unit-rem-calc(40px) 0;
        }
            .c-si-upload {
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                padding: 0 base.unit-rem-calc(16px);
                border-bottom: $border-width base.$color-grey-light-4 solid;
                .c-siu-placeholder {
                    display: flex;
                    align-items: center;
                    padding: base.unit-rem-calc(16px);
                    height: base.unit-rem-calc(160px);
                    width: 100%;
                    border-radius: base.unit-rem-calc(8px);
                    border: $border-width base.$color-grey-light-4 dashed;
                }
                .c-sui-upload {
                    display: flex;
                    justify-content: flex-end;
                    padding: base.unit-rem-calc(8px) 0;
                }
                .c-siuu-action {
                    @include base.button-text-icon-tertiary;
                }
            }
            .c-sid-image-placeholder {
                border-bottom: $border-width base.$color-grey-light-4 solid;
                margin-top: base.unit-rem-calc(8px);
                .c-sidp-image {
                    height: base.unit-rem-calc(160px);
                    width: base.unit-rem-calc(268px);
                    border: $border-width base.$color-grey-light-4 dashed;
                }
            }
            .c-sid-image-display {
                border-bottom: $border-width base.$color-grey-light-4 solid;
            }
                .c-sidd-image {
                    display: block;
                    margin: 0 base.unit-rem-calc(16px);
                    border: $border-width base.$color-grey-light-4 solid;
                    border-radius: base.unit-rem-calc(8px);
                    overflow: hidden;
                    > img {
                        display: block;
                        min-width: 100%;
                    }
                }
                .c-sidd-delete {
                    display: flex;
                    justify-content: flex-end;
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(24px);
                    @include base.clearfix;
                }
                    .c-siddd-action {
                        @include base.button-text-icon-tertiary('negate');
                    }
            .c-sid-sections {
                > p {
                    margin-bottom: 0;
                }
            }
        .c-sids-segment {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(8px);
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            &:last-child {
                border-bottom: none;
            }
        }
        .c-sidss-info {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(16px);
            > p {
                margin-bottom: 0;
                word-break: break-all;
            }
        }
        .c-sidssi-icon {
            @include base.svg-icon('default-18');
            color: base.$color-grey-light-2;
        }
    }
    .m-sidebar-templates {
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
        .i-slw-item {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: base.unit-rem-calc(7px);
            border-bottom: none;
            padding: 0;
        }
        .s-sidebar-list {
            margin: base.unit-rem-calc(8px) 0;
        }
    }
}
//////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Modals (can't be localized since they are created at the root of the document
//////////////////////////////////////////////////////////////////////////////////////////////////////////////
.m-section-modal {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-sm-messages {
        .s-flash-messages {
            &.t-has-message {
                padding-bottom: base.unit-rem-calc(16px);
            }
        }
    }
    .c-sm-header {
        display: flex;
        padding: base.unit-rem-calc(12px);
        border-bottom: $border-width base.$color-grey-light-4 solid;
    }
        .c-smh-wrapper {
            flex: 1;
            width: 100%;
            align-self: center;
        }
            $header-height: base.unit-rem-calc(40px);
            .c-smhw-text {
                flex: 1;
                align-self: center;
                word-break: break-word;
                @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
                padding-left: base.unit-rem-calc(4px);
                color: base.$color-grey-dark-4;
                cursor: text;
                @include base.respond-to('<640px') {
                    @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
                }
                &.t-empty {
                    color: base.$color-grey-light-2;
                    font-style: italic;
                }
            }
            .c-smhw-input {
                display: none;
                width: 98%;
                height: base.unit-rem-calc(32px);
                padding-left: base.unit-rem-calc(8px);
                @include base.typo-paragraph;
                border: $border-width base.$color-primary-light-1 solid;
                border-radius: base.unit-rem-calc(3px);
                outline: none;
                box-shadow: 0 0 0 0.25rem base.$color-primary-light-4;
                &::placeholder {
                    color: base.$color-grey-light-3;
                }
                @include base.respond-to('<small') {
                    font-size: base.unit-rem-calc(16px);
                }
            }
        .c-smh-close {
            display: flex;
            justify-content: center;
            align-items: center;
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            outline: 0 transparent solid;
            border-radius: base.unit-rem-calc(32px);
            color: base.$color-grey-light-2;
            transition: all 0.2s ease-out;
            .c-smhc-icon {
                @include base.svg-icon('default-24');
            }
            @include base.respond-to('hover') {
                &:hover {
                    scale: 1.1;
                    outline: base.unit-rem-calc(1.5px) base.$color-grey-light-4 solid;
                }
                &:active {
                    color: base.$color-grey-dark-1;
                }
            }
        }
    .c-sm-content {
        display: flex;
        flex-direction: column;
        padding: base.unit-rem-calc(16px);
        height: 100%;
        overflow: scroll;
        p {
            margin: 0;
        }
    }
        .c-smc-filters {
            display: flex;
            align-items: center;
            column-gap: base.unit-rem-calc(16px);
            row-gap: base.unit-rem-calc(8px);
            margin-bottom: base.unit-rem-calc(8px);
            @include base.respond-to('<xsmall') {
                flex-direction: column;
            }
        }
            %filter-header {
                display: flex;
            }
            %filter-header-text {
                flex: 1;
                @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px))
            }
            %filter-header-reset {
                @include base.button-text-icon-tertiary;
            }
            .c-smcf-categories {
                width: 100%;
                @include dropdown.select2();
            }
                .c-smcfc-header {
                    @extend %filter-header;
                }
                    .c-smcfch-text {
                        @extend %filter-header-text;
                    }
                    .c-smcfch-reset {
                        @extend %filter-header-reset;
                    }
            .c-smcf-search {
                width: 100%;
            }
                .c-smcfs-header {
                    @extend %filter-header;
                }
                    .c-smcfsh-text {
                        @extend %filter-header-text;
                    }
                    .c-smcfsh-reset {
                        @extend %filter-header-reset;
                    }
        .c-smc-forms {
            @include base.clearfix;
        }
            .c-smcf-header {
                @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
                border-bottom: $border-width base.$color-grey-light-4 solid;
                padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
                margin: 0 base.unit-rem-calc(-16px);
                @include base.respond-to('<640px') {
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(24px);
                }
            }
            .c-smcf-message {
                margin: base.unit-rem-calc(24px) 0;
                font-size: base.unit-rem-calc(16px);
                font-style: italic;
                color: base.$color-grey-light-2;
                text-align: center;
            }
            .c-smcf-list {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                padding: base.unit-rem-calc(24px) 0;
                min-height: base.unit-rem-calc(80px);
                max-height: base.unit-rem-calc(200px);
                gap: base.unit-rem-calc(16px);
                overflow: auto;
                @include base.respond-to('<800px') {
                    grid-template-columns: repeat(2, 1fr);
                }
                @include base.respond-to('<640px') {
                    grid-template-columns: 1fr;
                    max-height: 100%;
                }
                &.t-empty {
                    display: none;
                }
            }
                    .c-smcfli-form {
                        display: flex;
                        padding: base.unit-rem-calc(16px);
                        justify-content: center;
                        text-align: center;
                        min-height: base.unit-rem-calc(64px);
                        height: 100%;
                        color: base.$color-grey-dark-4;
                        border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                        border-radius: base.unit-rem-calc(8px);
                        background-color: base.$color-white-default;
                        box-shadow: base.$elevation-level-1;
                        transition: all 0.3s ease-in-out;
                        &:hover {
                            color: base.$color-primary-default;
                            border: $border-width transparent solid;
                            background-color: base.$color-primary-light-4;
                            box-shadow: none;
                        }
                        @include base.respond-to('no-hover') {
                            &:hover {
                                color: base.$color-grey;
                                background-color: transparent;
                            }
                        }
                        @include base.respond-to('<640px') {
                            min-height: base.unit-rem-calc(32px);
                            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
                            justify-content: left;
                        }
                    }
                        .c-smcflif-name {
                            align-self: center;
                            @include base.typo-text($size: 14px);
                        }
    .c-sm-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: base.unit-rem-calc(12px);
        border-top: $border-width base.$color-grey-light-4 solid;
    }
        .c-smc-forms-added {
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
            min-height: base.unit-rem-calc(42px);
            overflow: auto;
            @include base.respond-to('<640px') {
                padding: base.unit-rem-calc(8px) base.unit-rem-calc(24px);
            }
        }
        .c-smcf-selected-forms {
            display: flex;
            gap: base.unit-rem-calc(8px);
            flex-wrap: wrap;
        }
            $form-height: base.unit-rem-calc(28px);
            .c-smfsf-form {
                display: flex;
                align-items: center;
                padding: base.unit-rem-calc(2px) base.unit-rem-calc(8px) base.unit-rem-calc(2px) base.unit-rem-calc(16px);
                @include base.typo-text($size: 14px);
                background-color: base.$color-primary-light-1;
                color: base.$color-white-default;
                white-space: nowrap;
                border-radius: base.unit-rem-calc(32px);
                cursor: move;
                &.t-archived {
                    background-color: base.$color-grey-light-2;
                }
            }
                .c-smfsff-remove {
                    display: flex;
                    order: 2;
                    margin-left: base.unit-rem-calc(8px);
                }
                    .c-smfsffr-icon {
                        color: base.$color-white-default;
                        @include base.svg-icon('default-18');
                    }
        .c-smf-actions {
            display: flex;
            justify-content: right;
            align-items: center;
            gap: base.unit-rem-calc(16px);
            width: 50%;
            @include base.respond-to('<640px') {
                width: 100%;
            }
        }
            .c-smfa-working {
                display: none;
                width: base.unit-rem-calc(24px);
                height: base.unit-rem-calc(24px);
                padding: base.unit-rem-calc(8px);
                background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
                background-size: base.unit-rem-calc(24px) base.unit-rem-calc(24px);
                &.t-show {
                    display: flex;
                }
            }
}
.m-media-modal {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-mm-messages {}
    .c-mm-header {
        display: flex;
        padding: base.unit-rem-calc(12px);
        border-bottom: $border-width base.$color-grey-light-4 solid;
    }
        .c-mmh-text {
            flex: 1;
            align-self: center;
            @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
            color: base.$color-grey-dark-4;
            padding-left: base.unit-rem-calc(4px);
            margin-bottom: 0;
        }
        .c-mmh-close {
            display: flex;
            justify-content: center;
            align-items: center;
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            outline: 0 transparent solid;
            border-radius: base.unit-rem-calc(32px);
            color: base.$color-grey-light-2;
            transition: all 0.2s ease-out;
            .c-mmhc-icon {
                @include base.svg-icon('default-24');
            }
            @include base.respond-to('hover') {
                &:hover {
                    scale: 1.1;
                    outline: base.unit-rem-calc(1.5px) base.$color-grey-light-4 solid;
                }
                &:active {
                    color: base.$color-grey-dark-1;
                }
            }
        }
    .c-mm-content {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
        height: 100%;
        overflow: scroll;
        p {
            margin: 0;
        }
    }
        .c-mmc-message {
            margin: base.unit-rem-calc(16px) 0;
            font-size: base.unit-rem-calc(16px);
            font-style: italic;
            color: base.$color-light-grey;
            text-align: center;
        }
        .c-mmc-items {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: base.unit-rem-calc(16px);
            overflow-y: auto;
            padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px);
            max-height: 40vh;
            @include base.respond-to('<800px') {
                grid-template-columns: repeat(2, 1fr);
            }
            @include base.respond-to('<640px') {
                max-height: 100%;
                grid-template-columns: 1fr;
                padding: base.unit-rem-calc(24px);
            }
        }
            .c-mmci-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: base.unit-rem-calc(4px);
                border-radius: base.unit-rem-calc(8px);
                padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
                border: $border-width base.$color-grey-light-4 solid;
                box-shadow: base.$elevation-level-2;
                transition: all 0.3s ease-in-out;
                &:hover {
                    border: $border-width transparent solid;
                    background-color: base.$color-primary-light-4;
                    box-shadow: none;
                    .c-mmciin-inner {
                        color: base.$color-primary-default;
                    }
                    .c-mmciii-icon {
                        color: base.$color-primary-default;
                    }
                }
                &.t-active {
                    background-color: base.$color-primary-light-4;
                    border: $border-width base.$color-primary-default solid;
                    box-shadow: none;
                    .c-mmciin-inner {
                        color: base.$color-primary-default;
                    }
                    .c-mmciii-icon {
                        color: base.$color-primary-default;
                    }
                }
            }
                .c-mmcii-image {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                    .c-mmciii-icon {
                        @include base.svg-icon('default-32');
                        color: base.$color-grey-default;
                    }
                .c-mmcii-name {
                    display: flex;
                    justify-content: center;
                    word-break: break-all;
                }
                    .c-mmciin-inner {
                        @include base.typo-text($size: 13px);
                        text-align: center;
                        color: base.$color-grey-default;
                    }
    .c-mm-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: base.unit-rem-calc(12px);
        border-top: $border-width base.$color-grey-light-4 solid;
    }
        .c-mm-media-added {
            border-top: $border-width base.$color-grey-light-4 solid;
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            min-height: base.unit-rem-calc(42px);
            @include base.respond-to('<640px') {
                padding: base.unit-rem-calc(8px) base.unit-rem-calc(24px);
            }
        }
        .c-mmma-selected-items {
            display: flex;
            flex-wrap: wrap;
            gap: base.unit-rem-calc(8px);
        }
            .c-mmfsi-item {
                display: flex;
                align-items: center;
                padding: base.unit-rem-calc(2px) base.unit-rem-calc(8px) base.unit-rem-calc(2px) base.unit-rem-calc(16px);
                @include base.typo-text($size: 14px);
                background-color: base.$color-primary-light-1;
                color: base.$color-white-default;
                white-space: nowrap;
                border-radius: base.unit-rem-calc(32px);
                cursor: move;
            }
                .c-mmfsii-remove {
                    display: flex;
                    order: 2;
                    margin-left: base.unit-rem-calc(8px);
                }
                    .c-mmfsiir-icon {
                        color: base.$color-white-default;
                        @include base.svg-icon('default-18');
                    }
        .c-mmf-actions {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(16px);
            justify-content: right;
            width: 30%;
            @include base.respond-to('<640px') {
                width: 100%;
            }
        }
            .c-mmfa-working {
                display: none;
                width: base.unit-rem-calc(24px);
                height: base.unit-rem-calc(24px);
                padding: base.unit-rem-calc(8px);
                background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
                background-size: base.unit-rem-calc(24px) base.unit-rem-calc(24px);
                &.t-show {
                    display: flex;
                }
            }
}
.m-payment-term-choose-modal {
    .c-ptcm-header {
        display: flex;
        padding: base.unit-rem-calc(12px);
        border-bottom: $border-width base.$color-grey-light-4 solid;
    }
        .c-ptcmh-text {
            flex: 1;
            align-self: center;
            @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
            color: base.$color-grey-dark-4;
            padding-left: base.unit-rem-calc(4px);
            @include base.respond-to('<640px') {
                @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
            }
        }
        .c-ptcmh-close {
            display: flex;
            justify-content: center;
            align-items: center;
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            outline: 0 transparent solid;
            border-radius: base.unit-rem-calc(32px);
            color: base.$color-grey-light-2;
            transition: all 0.2s ease-out;
            .c-ptcmhc-icon {
                @include base.svg-icon('default-24');
            }
            @include base.respond-to('hover') {
                &:hover {
                    scale: 1.1;
                    outline: base.unit-rem-calc(1.5px) base.$color-grey-light-4 solid;
                }
                &:active {
                    color: base.$color-grey-dark-1;
                }
            }
        }
    .c-ptcm-content {
        padding: base.unit-rem-calc(32px) base.unit-rem-calc(24px);
        @include base.respond-to('<640px') {
            padding: base.unit-rem-calc(16px);
        }
    }
        .c-ptcmc-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: base.unit-rem-calc(16px);
            @include base.respond-to('<640px') {
                grid-template-columns: 1fr;
            }
        }
            .c-ptcmcl-item {
            }
                .c-ptcmcli-inner {
                    display: flex;
                    justify-content: center;
                    color: base.$color-grey-dark-4;
                    padding:  base.unit-rem-calc(16px) base.unit-rem-calc(8px);
                    border: $border-width base.$color-grey-light-4 solid;
                    border-radius: base.unit-rem-calc(8px);
                    box-shadow: base.$elevation-level-2;
                    &:hover {
                        border: $border-width base.$color-primary-light-3 solid;
                        background-color: base.$color-primary-light-4;
                        box-shadow: base.$elevation-level-3;
                        color: base.$color-primary-default;
                    }
                    @include base.respond-to('no-hover') {
                        &:hover {
                            background-color: transparent;
                        }
                    }
                }
                    .c-ptcmclii-name {
                        @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
                        text-align: center;
                    }
}
.m-payment-term-configure-modal {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-ptcm-messages {
        width: 100%;
        margin: base.unit-rem-calc(16px) 0 base.unit-rem-calc(8px);
    }
    .c-ptcm-header {
        display: flex;
        padding: base.unit-rem-calc(12px);
        border-bottom: $border-width base.$color-grey-light-4 solid;
    }
        .c-ptcmh-text {
            flex: 1;
            align-self: center;
            @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
            color: base.$color-grey-dark-4;
            padding-left: base.unit-rem-calc(4px);
            @include base.respond-to('<640px') {
                @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
            }
        }
        .c-ptcmh-close {
            display: flex;
            justify-content: center;
            align-items: center;
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            outline: 0 transparent solid;
            border-radius: base.unit-rem-calc(32px);
            color: base.$color-grey-light-2;
            transition: all 0.2s ease-out;
            .c-ptcmhc-icon {
                @include base.svg-icon('default-24');
            }
            @include base.respond-to('hover') {
                &:hover {
                    scale: 1.1;
                    outline: base.unit-rem-calc(1.5px) base.$color-grey-light-4 solid;
                }
                &:active {
                    color: base.$color-grey-dark-1;
                }
            }
        }
    .c-ptcm-content {
        display: flex;
        align-items: center;
        flex-direction: column;
        height: 100%;
        padding: 0 base.unit-rem-calc(24px) base.unit-rem-calc(8px);
        @include base.respond-to('<640px') {
            flex-direction: column;
            align-items: flex-start;
            overflow: scroll;
            padding: base.unit-rem-calc(16px);
            .m-installment-type {
                min-width: base.unit-rem-calc(800px);
            }
        }
    }
    .c-ptcm-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: base.unit-rem-calc(12px);
        border-top: $border-width base.$color-grey-light-4 solid;
    }
        .c-ptcmf-actions {
            display: flex;
            justify-content: right;
            gap: base.unit-rem-calc(16px);
            width: 50%;
            @include base.respond-to('<640px') {
                width: 100%;
            }
        }
            .c-ptcmfa-working {
                display: none;
                width: base.unit-rem-calc(24px);
                height: base.unit-rem-calc(24px);
                padding: base.unit-rem-calc(8px);
                background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
                background-size: base.unit-rem-calc(24px) base.unit-rem-calc(24px);
                &.t-show {
                    display: flex;
                }
            }
    /**** SUBMODULES ****/
    .m-one-time-type {
        width: 100%;
        margin-bottom: base.unit-rem-calc(32px);
        .c-oty-amount {
            display: flex;
            flex-direction: column;
            .c-otya-title {
                @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
            }
            .c-otya-amount {
                display: flex;
                align-items: center;
                padding: 0 base.unit-rem-calc(8px);
                color: base.$color-grey-light-1;
                border-bottom: $border-width base.$color-grey-light-4 solid;
                height: base.unit-rem-calc(32px);
            }
        }
    }
    .m-installment-type {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(8px);
        width: 100%;
        .c-it-header {
            display: flex;
            padding: 0 base.unit-rem-calc(4px);
            @include base.clearfix;
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
        }
        .c-ith-title {
            flex: 1;
            margin-right: base.unit-rem-calc(16px);
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
        }
        .c-it-installments {
            width: 100%;
            border-radius: base.unit-rem-calc(8px);
            box-shadow: inset 0 0 0 base.unit-rem-calc(1px) base.$color-grey-light-4;
            overflow: hidden;
            background: base.$color-background-edit;
            > tbody {
                display: flex;
                gap: base.unit-rem-calc(16px);
                flex-direction: column;
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
                border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                background-color: transparent;
            }
        }
        .c-it-totals {
            display: flex;
            justify-content: flex-end;
            gap: base.unit-rem-calc(24px);
            padding: base.unit-rem-calc(8px) 0;
            .c-itt-total {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                .c-ittt-label {
                    display: flex;
                    align-items: center;
                    gap: base.unit-rem-calc(4px);
                    @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
                    .c-ti-icon {
                        @include base.svg-icon('default-18');
                        color: base.$color-grey-light-3;
                    }
                    .m-tooltip-info {
                        padding-top: base.unit-rem-calc(1px);
                    }
                }
                .c-ittt-value {
                    padding: 0 base.unit-rem-calc(16px) base.unit-rem-calc(2px);
                    border-radius: base.unit-rem-calc(32px);
                    color: base.$color-white-default;
                    background-color: base.$color-green-default;
                    @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(24px));
                    &.t-invalid {
                        background-color: base.$color-red-default;
                    }
                }
            }
        }
            .c-iti-row {
                display: grid;
                grid-template-columns: 2fr 2fr 2fr 2fr base.unit-rem-calc(32px);
                column-gap: base.unit-rem-calc(8px);
                row-gap: base.unit-rem-calc(16px);
                align-items: center;
                padding-left: base.unit-rem-calc(8px);
                background-color: transparent;
                > th,
                > td {
                }
                &.t-header {
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(8px) 0 base.unit-rem-calc(16px);
                    .c-itir-data {
                        @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
                        &.t-value {
                            display: flex;
                            gap: base.unit-rem-calc(4px);
                            color: base.$color-grey-dark-4;
                            border-bottom: none;
                            padding-left: 0;
                            .c-ti-icon {
                                @include base.svg-icon('default-18');
                                color: base.$color-grey-light-3;
                            }
                        }
                    }
                }
            }
            .c-itir-data {
                padding: 0;
                margin-right: base.unit-rem-calc(8px);
                &.t-value {
                    display: flex;
                    align-items: center;
                    color: base.$color-grey-light-1;
                    height: base.unit-rem-calc(32px);
                    border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                    padding-left: base.unit-rem-calc(8px);
                    margin-right: 0;
                }
                &.t-amount {
                    display: flex;
                    gap: base.unit-rem-calc(8px);
                    .f-f-button-group {
                        width: base.unit-rem-calc(58px);
                        .f-fbg-button {
                            padding: 0;
                            font-family: 'Roboto', sans-serif;
                            font-weight: 500;
                            max-width: base.unit-rem-calc(26px);
                        }
                    }
                }
                &.t-actions {
                    margin-right: 0;
                }
            }
                .c-itird-action {
                    @include base.button-icon-tertiary('negate');
                    padding: base.unit-rem-calc(6px);
                }
        .c-iti-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 base.unit-rem-calc(15px) base.unit-rem-calc(16px);
        }
        .c-itif-add {
            @include base.button-text-icon-tertiary;
        }
    }
}
.m-terms-conditions-content-manager-modal {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-tccmm-header {
        display: flex;
        padding: base.unit-rem-calc(12px);
        border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
    }
        .c-tccmmh-text {
            flex: 1;
            align-self: center;
            @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
            color: base.$color-grey-dark-4;
            padding-left: base.unit-rem-calc(4px);
            @include base.respond-to('<640px') {
                @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
            }
        }
        .c-tccmmh-close {
            display: flex;
            justify-content: center;
            align-items: center;
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            outline: 0 transparent solid;
            border-radius: base.unit-rem-calc(32px);
            color: base.$color-grey-light-2;
            transition: all 0.2s ease-out;
            .c-tccmmhc-icon {
                @include base.svg-icon('default-24');
            }
            @include base.respond-to('hover') {
                &:hover {
                    scale: 1.1;
                    outline: base.unit-rem-calc(1.5px) base.$color-grey-light-4 solid;
                }
                &:active {
                    color: base.$color-grey-dark-1;
                }
            }
        }
    .c-tccmm-content {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
        padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px);
        height: 100%;
        overflow: scroll;
        p {
            margin: 0;
        }
    }
        .c-tccmm-messages {}
        .c-tccmmc-header {
            width: 100%;
            margin: 0;
            padding: base.unit-rem-calc(10px) base.unit-rem-calc(20px);
            font-size: base.unit-rem-calc(18px);
            color: base.$color-grey-default;
            background-color: base.$color-lighter-grey;
            border-color: base.$color-grey-light-4;
            border-width: base.unit-rem-calc(1px) 0;
            border-style: solid;
            &:first-child {
                border-top: none;
            }
        }
        .c-tccmmc-content {
            @include base.clearfix;
            padding: base.unit-rem-calc(20px);
        }
            %wysiwyg-content {
                div, p, ul, ol, h1, h2, h3, h4, h5, h6 {
                    margin: 0 0 base.unit-rem-calc(20px);
                }
                ul, ol {
                    padding-left: base.unit-rem-calc(15px);
                }
                h3 {
                    font-size: base.unit-rem-calc(24px);
                }
                h4 {
                    font-size: base.unit-rem-calc(20px);
                }
                h5 {
                    font-size: base.unit-rem-calc(16px);
                }
            }
            .c-tccmmcc-message {
                display: none;
                font-size: base.unit-rem-calc(12px);
                color: base.$color-grey-light-3;
            }
            .c-tccmmcc-accordion {
                display: none;
                max-height: base.unit-rem-calc(200px); // 4 accordion titles @todo make a variable for it
                border-color: base.$color-grey-light-4;
                border-width: base.unit-rem-calc(1px) base.unit-rem-calc(1px) 0;
                border-style: solid;
                border-radius: base.unit-rem-calc(2px);
                overflow: auto;
            }
                $accordion-title-height: base.unit-rem-calc(50px);
                $accordion-arrow-width: base.unit-rem-calc(45px);
                .c-tccmmcca-title {
                    display: flex;
                    align-items: center;
                    min-height: $accordion-title-height;
                    border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                    cursor: pointer;
                    &.t-open {
                        .c-tccmmccat-arrow {
                            transform: rotate(90deg);
                        }
                    }
                }
                    .c-tccmmccat-arrow {
                        flex: 0 0 auto;
                        position: relative;
                        width: $accordion-arrow-width;
                        height: $accordion-title-height;
                        transition: all 0.5s ease;
                        .c-tccmmccata-icon {
                            @include base.svg-icon-base;
                            top: base.unit-rem-calc(20px);
                            left: base.unit-rem-calc(15px);
                            width: base.unit-rem-calc(14px);
                            height: base.unit-rem-calc(10px);
                            color: base.$color-grey-light-2;
                        }
                    }
                    .c-tccmmccat-text {
                        flex: 1;
                        color: base.$color-grey-default;
                        font-size: base.unit-rem-calc(13px);
                        font-weight: 500;
                    }
                    .c-tccmmccat-action {
                        display: block;
                        flex: 0 0 auto;
                        position: relative;
                        width: base.unit-rem-calc(60px);
                        height: $accordion-title-height;
                        &:hover {
                            .c-tccmmccata-icon {
                                color: base.$color-grey-default;
                            }
                        }
                        .c-tccmmccata-icon {
                            @include base.svg-icon-base;
                            top: base.unit-rem-calc(15px);
                            left: base.unit-rem-calc(20px);
                            width: base.unit-rem-calc(20px);
                            height: base.unit-rem-calc(20px);
                            color: base.$color-grey-light-2;
                        }
                    }
                .c-tccmmcca-panel {
                    display: none;
                    padding: base.unit-rem-calc(20px) base.unit-rem-calc(20px) 0 $accordion-arrow-width;
                    font-size: base.unit-rem-calc(13px);
                    border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                    overflow: hidden;
                }
                    .c-tccmmccap-inner {
                        @extend %wysiwyg-content;
                    }
            .c-tccmmcc-selected {
                display: none;
                &.t-no-sort {
                    $disabled-color: sass-color.adjust(base.$color-grey-light-2, $lightness: 10%);
                    .c-tccmmccsit-grip {
                        cursor: not-allowed;
                        &:hover {
                            .c-tccmmccsitg-icon {
                                color: base.$color-grey-light-4;
                            }
                        }
                    }
                        .c-tccmmccsitg-icon {
                            color: base.$color-grey-light-4;
                        }
                }
            }
                $item-title-height: base.unit-rem-calc(30px);
                .c-tccmmccs-item {
                    margin-bottom: base.unit-rem-calc(8px);
                    border: $border-width base.$color-grey-light-4 solid;
                    border-radius: base.unit-rem-calc(2px);
                    &:last-child {
                        margin-bottom: 0;
                    }
                    &.t-locked {
                        .c-tccmmccsitit-locked {
                            display: inline-block;
                        }
                    }
                    &.t-required {
                        .c-tccmmccsic-text {
                            padding-right: base.unit-rem-calc(20px);
                        }
                    }
                }
                    .c-tccmmccsi-title {
                        display: flex;
                        align-items: center;
                        min-height: $item-title-height;
                        background-color: base.$color-lighter-grey;
                    }
                        .c-tccmmccsit-grip {
                            flex: 0 0 auto;
                            position: relative;
                            width: base.unit-rem-calc(30px);
                            height: $item-title-height;
                            cursor: move;
                            &:hover {
                                .c-tccmmccsitg-icon {
                                    color: base.$color-grey-default;
                                }
                            }
                        }
                            .c-tccmmccsitg-icon {
                                @include base.svg-icon-base;
                                top: base.unit-rem-calc(7px);
                                left: base.unit-rem-calc(10px);
                                width: base.unit-rem-calc(10px);
                                height: base.unit-rem-calc(16px);
                                color: base.$color-grey-light-2;
                            }
                        .c-tccmmccsit-inner {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            &:hover {
                                .c-tccmmccsitia-icon {
                                    color: base.$color-grey-default;
                                }
                            }
                        }
                            .c-tccmmccsiti-text {
                                flex: 1;
                                font-size: base.unit-rem-calc(12px);
                                color: base.$color-grey-default;
                                text-transform: uppercase;
                            }
                                .c-tccmmccsitit-locked {
                                    display: none;
                                    @include base.svg-icon-base(false);
                                    margin-left: base.unit-rem-calc(5px);
                                    width: base.unit-rem-calc(14px);
                                    height: base.unit-rem-calc(18px);
                                    stroke-width: base.unit-rem-calc(1px);
                                    vertical-align: bottom;
                                }
                                .c-tccmmccsitit-error {
                                    display: none;
                                    margin-left: base.unit-rem-calc(20px);
                                    color: base.$color-red-default;
                                    text-transform: none;
                                }
                            .c-tccmmccsiti-action {
                                flex: 0 0 auto;
                                position: relative;
                                width: base.unit-rem-calc(60px);
                                height: $item-title-height;
                            }
                                .c-tccmmccsitia-icon {
                                    @include base.svg-icon-base;
                                    top: base.unit-rem-calc(5px);
                                    left: base.unit-rem-calc(20px);
                                    width: base.unit-rem-calc(20px);
                                    height: base.unit-rem-calc(20px);
                                    color: base.$color-grey-light-2;
                                }
                    .c-tccmmccsi-content {
                        display: none;
                        border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                        &.t-open {
                            display: flex;
                        }
                    }
                        .c-tccmmccsic-text {
                            flex: 1;
                            font-size: base.unit-rem-calc(12px);
                            padding: base.unit-rem-calc(20px) 0 0 base.unit-rem-calc(20px);
                        }
                            .c-tccmmccsict-inner {
                                @extend %wysiwyg-content;
                            }
                            .c-tccmmccsict-input {
                                margin: 0 0 base.unit-rem-calc(20px);
                                .mce-btn-group:last-child {
                                    float: none;
                                }
                                &.t-answer-required-switch {
                                    display: flex;
                                    gap: base.unit-rem-calc(8px);
                                    align-items: center;
                                }
                            }
                        .c-tccmmccsic-delete {
                            display: block;
                            flex: 0 0 auto;
                            position: relative;
                            width: base.unit-rem-calc(60px);
                            height: base.unit-rem-calc(60px);
                            &:hover {
                                .c-tccmmccsicd-icon {
                                    color: base.$color-grey-default;
                                }
                            }
                        }
                            .c-tccmmccsicd-icon {
                                @include base.svg-icon-base;
                                top: base.unit-rem-calc(20px);
                                left: base.unit-rem-calc(21px);
                                width: base.unit-rem-calc(18px);
                                height: base.unit-rem-calc(20px);
                                color: base.$color-grey-light-2;
                            }
            $custom-add-height: base.unit-rem-calc(20px);
            .c-tccmmcc-container {
                display: flex;
                justify-content: flex-end;
            }
            .c-tccmmccc-custom-add {
                @include base.button-text-icon-tertiary;
            }
    .c-tccmm-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: base.unit-rem-calc(12px);
        border-top: $border-width base.$color-grey-light-4 solid;
    }
        .c-tccmmf-actions {
            display: flex;
            align-items: center;
            justify-content: right;
            width: 30%;
            @include base.respond-to('<640px') {
                width: 100%;
            }
        }
            .c-tccmmfa-working {
                display: none;
                width: base.unit-rem-calc(24px);
                height: base.unit-rem-calc(24px);
                padding: base.unit-rem-calc(8px);
                background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
                background-size: base.unit-rem-calc(24px) base.unit-rem-calc(24px);
                &.t-show {
                    display: flex;
                }
            }
}
.c-missing-terms {
    .c-mt-button {
        margin-top: base.unit-rem-calc(16px);
        @include base.button-text-icon-tertiary;
    }
}
.m-finalize-modal {
    .s-flash-messages {
        &.t-has-message {
            margin-bottom: base.unit-rem-calc(20px);
        }
    }
    p {
        margin-bottom: base.unit-rem-calc(16px) !important;
    }
    .f-field {
        &.t-switch {
            margin-top: base.unit-rem-calc(16px);
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(16px);
        }
    }
}

.m-edit-uploaded-file-modal {
    .c-eufm-marker-wrapper {
        margin-top: base.unit-rem-calc(16px);
        background: base.$color-background-edit;
        border-radius: base.unit-rem-calc(8px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-3;
    }
        .c-eufm-mw-toolbar {
            display: flex;
            justify-content: space-between;
            padding: base.unit-rem-calc(10px) base.unit-rem-calc(10px) 0 base.unit-rem-calc(10px);
            @include base.respond-to('<500px') {
                flex-direction: column;
                gap: base.unit-rem-calc(8px);
            }
        }
        %marker-icon {
            background-color: base.$color-background;
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-3;
            border-radius: base.unit-rem-calc(4px);
            display: flex;
            align-items: center;
            padding: base.unit-rem-calc(8px);
            cursor: pointer;
            &.t-line-wrapper {
                padding: base.unit-rem-calc(4px) base.unit-rem-calc(8px);
            }
            > [data-icon] {
                @include base.svg-icon('default-18');
                color: base.$color-grey-dark-4;
            }
            .t-line-icon {
                width: 18px;
                height: 1.5px;
                background-color: base.$color-grey-dark-4;
                transform: rotate(-45deg);
                transform-origin: bottom;
            }
            .t-color-picker {
                width: 18px;
                height: 18px;
                //background-color: red;
            }
            &.t-active {
                background-color: base.$color-primary-light-2 !important;
            }
        }
            .c-eufm-mw-t-left {
                display: flex;
                gap: base.unit-rem-calc(4px);

            }
                .c-eufm-mw-tl-button {
                    @extend %marker-icon;
                    &.t-color-picker {
                        .pickr {
                            display: flex;
                            .pcr-button {
                                width: base.unit-rem-calc(18px);
                                height: base.unit-rem-calc(18px);
                                //border-radius: base.unit-rem-calc(4px);
                            }
                        }
                    }
                }
            .c-eufm-mw-t-right {
                display: flex;
                gap: base.unit-rem-calc(4px);
            }
                .c-eufm-mw-tr-button {
                    @extend %marker-icon;
                }

        .c-eufm-mw-image {}
    .c-eufm-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: base.unit-rem-calc(8px);
    }
        .c-eufm-a-delete {
            @include base.button-text-tertiary('negate');
        }
}

/******* Modal Overrides ********/


// Pickr style overrides
.pcr-app {
    width: auto !important;
    background: base.$color-white-default;
    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
    box-shadow: base.$elevation-level-3;
    border-radius: base.unit-rem-calc(12px);
    &.t-swatches {
        .pcr-swatches {
            margin-top: 0 !important;
        }
    }
    .pcr-selection {
        display: none !important;
        &.t-show {
            display: flex !important;
        }
    }
    .pcr-color-palette {
        .pcr-palette {
            border-radius: base.unit-rem-calc(3px) !important;
            &:before {
                border-radius: base.unit-rem-calc(3px) !important;
                background-size: 0 !important;
            }
        }
    }
    .pcr-swatches {
        margin: base.unit-rem-calc(16px) 0 0 0 !important;
        grid-template-columns: repeat(8, 1fr) !important;
        gap: base.unit-rem-calc(6px);
        & > button {
            margin: 0 !important;
            width: base.unit-rem-calc(24px);
            height: base.unit-rem-calc(24px);
            border-radius: base.unit-rem-calc(24px) !important;
            transition: all 0.3s ease-in-out;
            &::before {
                background-size: base.unit-rem-calc(24px) !important;
                border-radius: base.unit-rem-calc(24px) !important;
            }
            &::after {
                background-size: base.unit-rem-calc(24px) !important;
                border-radius: base.unit-rem-calc(24px) !important;
            }
            &:hover {
                scale: 1.2;
            }
            &.pcr-active {
                &:hover {
                    scale: unset;
                }
            }
        }
    }
    .pcr-cancel {
        flex: 0 0 97%;
        background-color: base.$color-primary-default !important;
        border-radius: base.$prop-border-radius !important;
        box-shadow: base.$elevation-level-2 !important;
        color: base.$color-white-default !important;
        border: base.unit-rem-calc(1px) solid base.$color-primary-default !important;
        text-transform: uppercase;
        font-family: "Barlow", "Roboto", Arial, sans-serif;
        height: base.unit-rem-calc(32px);
        font-size: base.unit-rem-calc(14px) !important;
        font-weight: 600;
        display: none !important;
        margin-top: base.unit-rem-calc(24px) !important;
        @include base.respond-to('hover') {
            &:hover {
                background-color: base.$color-primary-light-1 !important;
                filter: unset !important;
                border-color: base.$color-primary-light-1 !important;
                color: base.$color-white-default !important;
                box-shadow: base.$elevation-level-3 !important;
            }
        }
        &.t-show {
            display: inline-block !important;
        }
    }
    .pcr-result {
        flex: 1 1 auto !important;
        border: 1px solid base.$color-grey-light-3;
        background-color: base.$color-white-default !important;
        color: base.$color-grey-dark-4 !important;
        height: base.unit-rem-calc(32px);
        font-family: 'Roboto', sans-serif;
        font-size: base.unit-rem-calc(14px) !important;
        border-radius: base.$prop-border-radius !important;
        letter-spacing: unset !important;
        text-align: center !important;
        display: none !important;
        box-shadow: none;
        @include base.respond-to('hover') {
            &:hover {
                filter: unset !important;
            }
        }
        &:active,
        &:focus {
            border-color: base.$form-input-active-border-color;
            box-shadow: 0 0 0 base.unit-rem-calc(4px) base.$form-input-active-drop-shadow-color !important;
        }
        &.t-show {
            display: inline-block !important;
        }
    }
}
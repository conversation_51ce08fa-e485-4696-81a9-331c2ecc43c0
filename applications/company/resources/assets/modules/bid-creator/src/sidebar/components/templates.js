/**
 * @module BidCreator/Sidebar/Components/Templates
 */

'use strict';

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Accordion = require('../accordion');
const Component = require('../component');
const ComponentNavItem = require('../nav-items/component_nav_item');
const List = require('../list');
const PubSub = require('../../pubsub');

const templates_tpl = require('@cam-bid-creator-tpl/sidebar/components/templates/main.hbs');

/**
 * @alias module:BidCreator/Sidebar/Components/Templates
 */
class Templates extends Component {
    /**
     * Constructor
     *
     * @param {module:BidCreator.Sidebar} sidebar
     * @param {Object} data
     * @param {Array} data.templates - List of all available templates
     * @param {Object.<string, string>} data.active_templates - Object with template field name as key and selected template id as value
     */
    constructor(sidebar, data) {
        super(sidebar, 'templates', 'Templates');

        this.state.templates = new Map([
            [Templates.Type.COVER, {
                title: 'Cover Template',
                field: 'cover_content_template_id',
                options: []
            }],
            [Templates.Type.INTRO, {
                title: 'Intro Template',
                field: 'intro_content_template_id',
                options: []
            }],
            [Templates.Type.SECTIONS, {
                title: 'Sections Template',
                field: 'sections_content_template_id',
                options: []
            }],
            [Templates.Type.LINE_ITEMS, {
                title: 'Line Items Template',
                field: 'line_items_content_template_id',
                options: []
            }],
            [Templates.Type.TERMS_CONDITIONS, {
                title: 'Terms & Conditions Template',
                field: 'terms_conditions_content_template_id',
                required: true,
                options: []
            }],
            [Templates.Type.IMAGES, {
                title: 'Images Template',
                field: 'images_content_template_id',
                required: true,
                options: []
            }],
            [Templates.Type.MEDIA, {
                title: 'Media Template',
                field: 'media_content_template_id',
                required: true,
                options: []
            }]
        ]);

        // store templates into the proper type
        for (let template of data.templates) {
            let $template = this.state.templates.get(template.type);
            $template.options.push(template);
        }

        // set selected status for each template based on active template data passed in
        for (let template of this.state.templates.values()) {
            template.selected = null;
            if (data.active_templates[template.field] !== undefined) {
                template.selected = data.active_templates[template.field];
            }
        }

        // create accordion and build lists
        let accordion = new Accordion.Controller;
        accordion.on(
            'item-booted',
            /**
             * @listens module:BidCreator/Sidebar/Accordion/Controller~event:itemBooted
             * @param {module:BidCreator/Sidebar/Accordion/Controller~event:itemBooted} event
             */
            (event) => {
                // boot each list after accordion item is booted
                event.item.storage('list').boot(event.item.elem.panel);
            }
        );

        this.state.templates.forEach((template) => {
            let list = new List({
                type: List.Type.RADIO,
                no_items_text: 'No templates available'
            });
            let selected_item = null;
            if (template.required === undefined || !template.required) {
                selected_item = list.addItem('None', {template_id: null});
            }
            for (let option of template.options) {
                let item_id = list.addItem(option.name + (option.is_default ? ' (default)' : ''), {
                    template_id: option.id
                });
                if (template.selected === option.id) {
                    selected_item = item_id;
                }
            }
            if (selected_item !== null) {
                list.setSelected(selected_item);
            }
            list.on('changed', (event) => {
                let template_id = list.getItem(event.new_ids[0]).storage.template_id;
                this.updateTemplate(template, template_id);
            });
            accordion.addItem(new Accordion.Item(template.title, list.render(), {
                list: list
            }));
        });

        this.state.accordion = accordion;
    };

    /**
     * Types
     *
     * @readonly
     *
     * @returns {{COVER: number, INTRO: number, SECTIONS: number, LINE_ITEMS: number, TERMS_CONDITIONS: number, IMAGES: number, MEDIA: number}}
     */
    static get Type() {
        return {
            COVER: 1,
            INTRO: 2,
            SECTIONS: 3,
            LINE_ITEMS: 4,
            TERMS_CONDITIONS: 11,
            IMAGES: 5,
            MEDIA: 10
        };
    };

    /**
     * Get nav item for sidebar
     *
     * @returns {ComponentNavItem}
     */
    getNavItem() {
        return new ComponentNavItem(this.name, {
            icon: 'remix-icon--design--layout-5-line',
            classes: ['t-templates']
        });
    };

    /**
     * Send API request to update template
     *
     * @param {Object} template - Template config
     * @param {?string} template_id - Id of template
     */
    updateTemplate(template, template_id) {
        let payload = {
            id: this.sidebar.bid.id
        };
        payload[template.field] = template_id;
        template.selected = template_id;
        let request = new Api.BatchRequest.Single('bid-item', 'partial-update', payload);
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, request);
    };

    /**
     * Validate template settings
     *
     * @returns {Promise}
     */
    validate() {
        return new Promise((resolve, reject) => {
            let required = [],
                selected = false;
            for (let template of this.state.templates.values()) {
                if (template.required !== undefined && template.required && template.selected === null) {
                    required.push(template.title);
                    continue;
                }
                if (template.selected !== null) {
                    selected = true;
                }
            }
            if (required.length > 0) {
                let content = `<p>The following template types require a selection:</p><ul><li>${required.join('</li><li>')}</li></ul>`;
                this.sidebar.bid.getMessageModal().openWithContent('Template Selection Required', content);
                reject();
                return;
            }
            if (!selected) {
                this.sidebar.bid.getMessageModal().openWithContent('No Template Selected', '<p>At least one template is required to preview</p>');
                reject();
                return;
            }
            resolve();
        });
    };

    /**
     * Boot templates component
     *
     * @param {Object} root - jQuery element for panel
     */
    boot(root) {
        super.boot(root);

        this.elem.templates = this.elem.content.fxFind('templates');

        this.state.accordion.boot(this.elem.templates);
    };

    /**
     * Render panel content
     *
     * @returns {string}
     */
    renderPanelContent() {
        return templates_tpl({
            accordion: this.state.accordion.render()
        });
    };
}

module.exports = Templates;

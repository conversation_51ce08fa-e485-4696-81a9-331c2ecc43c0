/**
 * @module FlashMessage/Message
 */

'use strict';

const message_tpl = require('@cam-bid-creator-tpl/flash-messages/message.hbs');

/**
 * @alias module:FlashMessage/Message
 */
class Message {
    /**
     * Constructor
     *
     * @param {string} message
     * @param {number} type
     */
    constructor(message, type) {
        Message.__idx++;
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            id: Message.__idx,
            message: message,
            type: type,
            action_idx: 0,
            actions: new Map
        };
    };

    /**
     * Short hand method to create message instance
     *
     * @param {string} message
     * @param {number} type
     * @returns {Message}
     */
    static make(message, type) {
        return new Message(message, type);
    };

    /**
     * Types
     *
     * @readonly
     *
     * @returns {{ERROR: number, INFO: number}}
     */
    static get Type() {
        return {
            ERROR: 1,
            INFO: 2
        };
    };

    /**
     * Get id
     *
     * @readonly
     *
     * @returns {number}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Add preconfigured action for clear
     */
    actionClear() {
        this.action('remix-icon--system--close-line', () => {
            this.delete();
        });
    };

    /**
     * Add preconfigured action for retry
     *
     * @param {function} handler
     */
    actionRetry(handler) {
        this.action('remix-icon--system--refresh-line', handler);
    };

    /**
     * Add action
     *
     * @param {string} icon - Icon to display for action
     * @param {function} handler
     */
    action(icon, handler) {
        this.state.actions.set(this.state.action_idx, {
            idx: this.state.action_idx,
            icon: icon,
            handler: handler
        });
        this.state.action_idx++;
    };

    /**
     * Handle action
     *
     * @param {number} idx
     */
    handleAction(idx) {
        let action = this.state.actions.get(idx);
        if (action === undefined) {
            return;
        }
        action.handler();
    };

    /**
     * Delete message
     */
    delete() {
        this.elem.root.remove();
        this.state.controller.clearMessage(this.state.id);
    };

    /**
     * Boot message
     *
     * @param {module:FlashMessage/Controller} controller
     */
    boot(controller) {
        this.state.controller = controller;

        this.elem.root = controller.elem.root.fxChildren('message', {id: this.state.id});
    };

    /**
     * Render message
     *
     * @returns {string}
     */
    render() {
        let type_map = {
            [Message.Type.ERROR]: 'error',
            [Message.Type.INFO]: 'info'
        };

        return message_tpl({
            id: this.state.id,
            type: type_map[this.state.type],
            message: this.state.message,
            actions: Array.from(this.state.actions.values())
        });
    };
}

Message.__idx = 0;

module.exports = Message;

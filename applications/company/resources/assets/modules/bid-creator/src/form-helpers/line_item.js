'use strict';

const FormLineItemHelper = require('@cas-form-js/helpers/line_item').LineItemHelper;

const PubSub = require('../pubsub');
const LineItem = require('../entities/line_item');

class LineItemHelper extends FormLineItemHelper {
    /**
     * Add line item from form rule event
     *
     * @param {module:Form.Group} group
     * @param {module:Form/Group/Rule.Event} event
     * @param {object} data
     * @param {number} data.type
     * @param {string|undefined} data.name
     * @param {string|undefined} data.quantity
     * @param {string|undefined} data.product_id
     * @param {string|undefined} data.amount
     * @param {number|undefined} data.amount_type
     * @param {string|undefined} data.total
     */
    async add(group, event, data) {
        // get line item from cache if one already exists for this group and event so we aren't generating new line
        // items all the time
        let line_item = this.get(group, event.id);
        if (line_item === null) {
            try {
                line_item = LineItem.Base.getByType(data.type);
                line_item.source = LineItem.Source.FORM_EVENT;
                line_item.bid_item_section_id = group.form.storage('bid_form.section.id');
                line_item.form_item_group = group;
                line_item.form_item_group_rule_event = event;
                // if an existing line item matches this one, the system will merge them. When this happens we want to
                // replace the one in our cache with the final merged one so it won't happen again
                line_item.on('merged', line_item => this.set(group, event.id, line_item));
                this.set(group, event.id, line_item);
            } catch (e) {
                console.log(e);
                return;
            }
        }

        line_item.quantity = data.quantity;
        if (data.total !== undefined) {
            line_item.total = data.total;
        }
        switch (line_item.type) {
            case LineItem.Type.GENERAL:
                line_item.name = data.name;
                line_item.amount = data.amount;
                break;
            case LineItem.Type.PRODUCT:
                line_item.product_id = data.product_id;
                if (typeof data.name === 'string') {
                    line_item.name_override = data.name;
                }
                break;
            case LineItem.Type.DISCOUNT:
            case LineItem.Type.FEE:
                line_item.name = data.name;
                line_item.amount_type = data.amount_type;
                line_item.raw_amount = data.amount;
                break;
        }

        // publish line item synchronously to make sure batch request order is handled right
        PubSub.Handler.publishSync(PubSub.Topics.LineItem.SAVE, {
            line_item,
            update_total: !group.form.storage('bid_form.section.bid').isLoading()
        });
    };

    /**
     * Handle form group destroy event
     *
     * We use destroy here since we don't need to make any api calls to delete individual items. This is automatically
     * handled server side by whatever caused the group to be destroyed.
     *
     * @param {object} line_item
     */
    onGroupDestroy(line_item) {
        line_item.destroy();
    };

    /**
     * Handle removal of line item by form rule event
     *
     * @param {object} line_item
     */
    onRemove(line_item) {
        line_item.delete();
    };
}

module.exports = LineItemHelper;

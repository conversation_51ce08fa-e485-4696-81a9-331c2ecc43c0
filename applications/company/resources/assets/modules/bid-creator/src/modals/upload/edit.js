'use strict';

const {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} = require("@markerjs/markerjs3");
const Pickr = require("@simonwep/pickr");

const Api = require('@ca-package/api');
const FormValidator = require('@ca-submodule/validator');

const Modal = require('@ca-submodule/modal').Base;

const modal_tpl = require('@cam-bid-creator-tpl/modals/upload/edit.hbs');

const OutputFormats = {
    RGB: 1,
    RGBA: 2,
    HSL: 3,
    HSLA: 4,
    HEX: 5
};
const OutputFormatMethods = new Map([
    [OutputFormats.RGB, 'toRGBA'],
    [OutputFormats.RGBA, 'toRGBA'],
    [OutputFormats.HSL, 'toHSLA'],
    [OutputFormats.HSLA, 'toHSLA'],
    [OutputFormats.HEX, 'toHEXA']
]);

class Edit extends Modal {
    constructor() {
        super(modal_tpl(), {
            size: Modal.Size.TINY,
            closable: false
        });
        this.setTitle('Edit File');
        this.addAction({
            type: Modal.Action.CANCEL,
            label: 'Cancel',
            handler: () => this.close()
        });
        this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        Object.assign(this.state, {
            pickr: [],
            output_format: OutputFormats.HEX,
            color_precision: 0
        });

        this.elem.form = this.elem.content.fxFind('form');
        this.elem.marker_container = this.elem.content.fxFind('marker-container');
        this.elem.color = this.elem.content.fxFind('color');
        this.elem.delete = this.elem.content.fxFind('delete-image');

        this.state.editors = {
            freehand: {
                type: 'editor',
                marker_editor: 'FreehandMarker'
            },
            arrow: {
                type: 'editor',
                marker_editor: 'ArrowMarker'
            },
            text: {
                type: 'editor',
                marker_editor: 'TextMarker'
            },
            line: {
                type: 'editor',
                marker_editor: 'LineMarker'
            },
            rectangle: {
                type: 'editor',
                marker_editor: 'FrameMarker'
            },
            ellipsees: {
                type: 'editor',
                marker_editor: 'EllipseFrameMarker'
            },
            select: {
                type: 'tool',
                marker_tool: 'switchToSelectMode'
            },
            delete: {
                type: 'tool',
                marker_tool: 'deleteSelectedMarkers'
            },
            undo: {
                type: 'tool',
                marker_tool: 'undo'
            },
            redo: {
                type: 'tool',
                marker_tool: 'redo'
            }
        }

        // find editors, add to state, and setup click event
        this.elem.editors =  this.elem.content.fxFind('editor');
        for (let i=0; i < this.elem.editors.length; i++) {
            let this_item = $(this.elem.editors[i]),
                id = this_item.data('id');
            this.state.editors[id]['elem'] = this_item;
            this_item.fxClick((e) => {
                e.preventDefault();
                this.activateEditor(id);
            });
        }
        this.state.active_editor = null;

        this.elem.input = {};
        for (let name of ['name', 'description']) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.getPickr('color');

        this.state.validator = FormValidator.create(this.elem.form, {
            name: {
                required: true,
                maxlength: 150
            },
            description: {
                maxlength: 250
            }
        })
            .on('submit', () => {
                this.save();
                return false;
            });

        this.elem.delete.fxClick((e) => {
            e.preventDefault();
            this.delete();
        });
    };

    /**
     * Get picker config
     *
     * @returns {object}
     */
    getPickrConfig(name) {
        let use_opacity = [OutputFormats.HSLA, OutputFormats.RGBA].indexOf(this.state.output_format) !== -1;
        return {
            el: this.elem[`${name}`][0],
            theme: 'monolith',
            appClass: `m-color-picker-${name}`,
            default: '#000000',
            swatches: [
                '#ff0000', '#000000', '#00b600', '#0070ff',
                '#ff5d00', '#858585', '#a000ff'
            ],
            position: 'top-end',
            lockOpacity: !use_opacity,
            adjustableNumbers: false,
            components: {
                palette: true,
                preview: false,
                opacity: use_opacity,
                hue: true,
                interaction: {
                    hex: false,
                    rgba: false,
                    hsla: false,
                    hsva: false,
                    cmyk: false,
                    input: true,
                    cancel: true,
                    clear: false,
                    save: false
                }
            }
        };
    };

    /**
     * Get pickr instance
     *
     * @returns {Promise<Pickr>}
     */
    getPickr(name) {
        if (this.state.pickr[name] === undefined) {
            let pickr = new Pickr(this.getPickrConfig(name));
            pickr.on('init', (instance) => {
                let appClass = instance.options.appClass,
                    name = appClass.replace('m-color-picker-',''),
                    pickrElem = $(`.${appClass}`),
                    btn = pickrElem.find('.pcr-cancel');
                btn.text('More');
                btn.val('More');
                btn.attr('aria-label', 'show more');

                this.elem[`${name}_pickr`] = pickrElem;
                this.elem[`${name}_pcr_selection`] = pickrElem.find('.pcr-selection');
                this.elem[`${name}_pcr_result`] = pickrElem.find('.pcr-result');
                this.elem[`${name}_pcr_more`] = btn;
                this.showPalette(name);
            });
            pickr.on('change', (hsva_color, instance) => {
                this.handlePickrSave(hsva_color, instance,false);
                if (this.state.active_editor !== null && this.state.active_editor !== 'select') {
                    this.activateEditor('select');
                }
            });
            pickr.on('swatchselect', (hsva_color, instance) => {
                this.handlePickrSave(hsva_color, instance);
                this.activateEditor('select');
            });
            pickr.on('cancel', (instance) => {
                let appClass = instance.options.appClass,
                    name = appClass.replace('m-color-picker-','');
                this.showPalette(name);
            });
            this.state.pickr[name] = pickr;
        }
        return this.state.pickr[name];
    };

    /**
     * Handle saving of the color picker option
     */
    handlePickrSave(color, instance, close = true) {
        let name = $(instance.options.el).data('js');
        let method = OutputFormatMethods.get(this.state.output_format);
        let new_color = color[method]().toString(this.state.color_precision);
        this.setPickrColor(name, new_color);
        if (close) {
            instance.hide();
        }
    };

    /**
     * Set color picker color
     */
    setPickrColor(name, color) {
        let pickr = this.state.pickr[name];
        pickr.setColor(color, true);
        pickr.applyColor(true);
        this.state.marker_color = color;
    };

    /**
     * Show palette to reset pickers or if user wants more color options
     *
     * @param {string|null} name
     */
    showPalette(name = null) {
        let pickers = name !== null ? [`${name}`] : ['user_color'];
        for (let picker of pickers) {
            this.elem[`${picker}_pickr`].removeClass('t-swatches');
            this.elem[`${picker}_pcr_selection`].addClass('t-show');
            this.elem[`${picker}_pcr_result`].addClass('t-show');
            this.elem[`${picker}_pcr_more`].removeClass('t-show');
        }
    }

    /**
     * Activate the selected editor and disable the rest
     *
     * @param {string} editor
     */
    activateEditor(editor) {
        let new_editor = this.state.editors[editor],
            previous_editor = this.state.active_editor;
        if (this.state.active_editor === editor) {
            return;
        }
        if (this.state.active_editor !== null) {
            this.state.editors[this.state.active_editor].elem.removeClass('t-active');
        }

        this.state.active_editor = editor;
        new_editor.elem.addClass('t-active');

        if (new_editor.type === 'editor') {
            this.state.marker_area.createMarker(new_editor.marker_editor);
        } else {
            this.state.marker_area[new_editor.marker_tool]();
            if (editor !== 'select') {
                if (this.state.editors[previous_editor].type === 'editor') {
                    this.activateEditor(previous_editor);
                } else {
                    this.activateEditor('select');
                }
            }
        }
    };

    /**
     * Populate data into modal
     *
     * @param {object} data
     */
    populate(data) {
        this.setPickrColor('color', '#ff0000');

        this.elem.input.name.val(data.name);
        this.elem.input.description.val(data.description);

        this.state.image = document.createElement('img');
        this.state.image.src = this.state.file_info.file.state.url;

        this.state.marker_area = new MarkerArea();
        this.state.marker_area.targetImage = this.state.image;
        this.state.marker_area.targetWidth = 455;
        this.elem.marker_container.append(this.state.marker_area);

        this.state.marker_area.addEventListener("markercreate", (e) => {
            this.activateEditor('select');
        });

        this.state.marker_area.addEventListener('markercreating', (e) => {
            let editor = e.detail.markerEditor;
            switch(this.state.active_editor) {
                case 'freehand':
                    editor.strokeColor = this.state.marker_color;
                    break;
                case 'arrow':
                    editor.strokeColor = this.state.marker_color;
                    break;
                case 'text':
                    editor.marker.color = this.state.marker_color;
                    break;
                case 'line':
                    editor.strokeColor = this.state.marker_color;
                    break;
                case 'rectangle':
                    editor.strokeColor = this.state.marker_color;
                    break;
                case 'ellipsees':
                    editor.strokeColor = this.state.marker_color;
                    break;
            }
            // other properties include for the future
            // fillColor, strokeWidth, strokeDasharray, arrowType, fontFamily
        });

        this.activateEditor('freehand');
    };

    /**
     * Fetch data from the server
     *
     * @param {string} id
     * @returns {Promise<void>}
     */
    async fetchFileData(id) {
        let {data: entity} = await Api.Resources.Files()
            .retrieve(id);
        this.populate(entity);
    };

    /**
     * Open modal
     *
     * @param {object} image_file
     */
    async open(image_file) {
        this.state.file_id = image_file.file.state.file_id;
        this.state.file_info = image_file;
        let url_array = this.state.file_info.file.state.url.split('?');
        this.state.file_info.file.state.url = url_array.length > 1 && url_array[1].includes('_csm') ? url_array[0] : this.state.file_info.file.state.url;
        await this.fetchFileData(this.state.file_id);
        super.open();
    };

    /**
     * Convert base 64 data into blob
     *
     * @param {string} base64
     * @param {string} mimeType
     * @returns {Blob|null}
     */
    base64ToBlob(base64, mimeType) {
        try {
            if (typeof base64 !== 'string' || !base64.includes(',')) {
                throw new Error('Invalid base64 input');
            }
            if (typeof mimeType !== 'string' || !mimeType) {
                throw new Error('Invalid mimeType');
            }
            let base64Data = base64.split(',')[1],
                byteCharacters = atob(base64Data),
                byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            let byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        } catch (e) {
            console.error('base64ToBlob error:', e);
            return null;
        }
    };

    /**
     * Cleanup modal after request
     *
     * @param {object} data
     * @param {boolean} image_update
     */
    afterRequest(data, image_update = false) {
        this.state.file_info.file.state.name = data.name;
        this.state.file_info.file.state.description = data.description;
        if (image_update) {
            this.state.file_info.file.state.url = `${this.state.file_info.file.state.url}?rand=${Math.random() * 99999999}`;
        }

        this.state.file_info.file.state.events.emit('edited');
        this.resetWorking();
        this.close();
    };

    /**
     * Save file info
     */
    async save() {
        this.startWorking();
        let data = {
            name:  this.elem.input.name.val(),
            description:  this.elem.input.description.val()
        };

        let new_image = null;
        if (this.state.marker_area.getState().markers.length > 0) {
            this.state.renderer = new Renderer();
            this.state.renderer.targetImage = this.state.image;
            this.state.renderer.imageType = 'image/jpeg';
            const dataUrl = await this.state.renderer.rasterize(this.state.marker_area.getState());
            new_image = this.base64ToBlob(dataUrl, 'image/jpeg')
        }

        Api.Resources.Files().partialUpdate(this.state.file_id, data).then(() => {
            if (new_image !== null) {
                Api.Resources.Files().file(new_image, 'image').method(Api.Request.Method.PUT).custom(`${this.state.file_id}/image`).then(() => {
                    this.afterRequest(data, true);
                }, (error, response) => {
                    this.resetWorking();
                    alert('Error saving file');
                });
            } else {
                this.afterRequest(data);
            }
        }, (error, response) => {
        });
    };

    /**
     * Handle delete file
     */
    delete() {
        this.state.file_info.file.startDelete();
        this.close();
    };

    /**
     * Close modal
     */
    close() {
        this.resetWorking();
        this.state.validator.reset();
        this.elem.form[0].reset();

        // clear fields
        this.elem.input.name.val('');
        this.elem.input.description.val('');

        this.elem.marker_container.empty();

        this.state.editors[this.state.active_editor].elem.removeClass('t-active');
        this.state.active_editor = null;

        // this.elem.input.color.val('#FF0000').trigger('change');

        super.close();
    };
}

module.exports = Edit;

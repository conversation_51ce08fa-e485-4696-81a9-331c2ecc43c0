/**
 * @module BidCreator/Entities/LineItem/Base
 */

'use strict';

const lang = require('lodash/lang');
const includes = require('lodash/includes');
const uuid4 = require('uuid/v4');
const isUuid = require('validator/lib/isUUID');

/**
 * @type {typeof module:Utils.Number}
 */
const Number = require('@cac-js/utils/number');
/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const Entity = require('../../entity');
const PubSub = require('../../pubsub');

const TypeClasses = new Map;

/**
 * Base class for all line item types, not used directly
 *
 * @alias module:BidCreator/Entities/LineItem/Base
 */
class Base extends Entity {
    /**
     * Constructor
     *
     * @param {Object} [data={}]
     * @param {boolean} [existing=false]
     */
    constructor(data = {}, existing = false) {
        super();

        Object.assign(this.state, {
            id: !lang.isNil(data.id) ? data.id : null,
            type: null,
            item_id: !lang.isNil(data.item_id) ? data.item_id : null,
            order: null,
            total_override: false,
            quantity_changed: false
        });

        // only fill certain fields since they need to be validated, we don't want users setting things like id's
        this.fill([
            'bid_item_section_id', 'form_item_entry_group_id', 'form_item_group_rule_event_id', 'source', 'name',
            'quantity', 'original_amount', 'amount', 'order'
        ], data);
        if (data.subtotal !== data.total) {
            this.total = data.total;
            this.state.total_override = true;
        }
        // if we are loading an existing entity, we don't want the state to be changed which could trigger an
        // unnecessary save
        if (existing) {
            this.setStateChanged(false);
        }
    };

    /**
     * Types
     *
     * @readonly
     *
     * @returns {{GENERAL: number, PRODUCT: number, DISCOUNT: number, FEE: number}}
     */
    static get Type() {
        return {
            GENERAL: 1,
            PRODUCT: 2,
            DISCOUNT: 3,
            FEE: 4
        };
    };

    /**
     * Adjustment Modes
     *
     * @readonly
     *
     * @returns {{PLUS: number, MINUS: number}}
     */
    static get AdjustmentMode() {
        return {
            POSITIVE: 1,
            NEGATIVE: 2
        };
    };

    /**
     * Adjustment Types
     *
     * @readonly
     *
     * @returns {{TOTAL: number, PERCENTAGE: number}}
     */
    static get AdjustmentType() {
        return {
            TOTAL: 1,
            PERCENTAGE: 2
        };
    };

    /**
     * Sources
     *
     * @readonly
     *
     * @returns {{BID_SIDEBAR_PRICING: number, FORM_EVENT: number}}
     */
    static get Source() {
        return {
            BID_SIDEBAR_PRICING: 1,
            FORM_EVENT: 2
        };
    };

    /**
     * Amount Types
     *
     * @readonly
     *
     * @returns {{TOTAL: number, PERCENTAGE: number}}
     */
    static get AmountType() {
        return {
            TOTAL: 1,
            PERCENTAGE: 2
        };
    };

    /**
     * Get class object by type
     *
     * @param {number} type
     * @returns {Object}
     */
    static getClassByType(type) {
        if (!TypeClasses.has(type)) {
            throw new Error(`Unable to find class for type: ${type}`);
        }
        return TypeClasses.get(type);
    };

    /**
     * Get new line item instance by type
     *
     * @param {number} type
     */
    static getByType(type) {
        let type_class = this.getClassByType(type);
        return new type_class;
    };

    /**
     * Get proper type instance based on entity object
     *
     * @param {Object} data
     * @param {number} data.type
     * @param {boolean} [existing=false]
     */
    static getByData(data, existing = false) {
        let type = this.getClassByType(data.type);
        return new type(data, existing);
    };

    /**
     * Get type
     *
     * @readonly
     *
     * @returns {number}
     */
    get type() {
        return this.state.type;
    };

    /**
     * Get id
     *
     * If no id is set, one will be generated and returned
     *
     * @readonly
     *
     * @returns {string} - Uuid
     */
    get id() {
        if (lang.isNil(this.state.id)) {
            this.setState({
                id: uuid4()
            }, false);
        }
        return this.state.id;
    };

    /**
     * Get item id
     *
     * If no id is set, one will be generated and returned
     *
     * @readonly
     *
     * @returns {string} - Uuid
     */
    get item_id() {
        if (lang.isNil(this.state.item_id)) {
            this.setState({
                item_id: uuid4()
            }, false);
        }
        return this.state.item_id;
    };

    /**
     * Set bid item section id
     *
     * @param {string} value - Section uuid
     */
    set bid_item_section_id(value) {
        this.setState({
            bid_item_section_id: value
        });
    };

    /**
     * Get bid item section id
     *
     * @readonly
     *
     * @returns {?string}
     */
    get bid_item_section_id() {
        return this.state.bid_item_section_id;
    };

    /**
     * Set form item group instance
     *
     * @param {module:Form.Group} group
     */
    set form_item_group(group) {
        this.setState({
            form_item_group: group,
            form_item_entry_group_id: group.entry.id_full
        });
    };

    /**
     * Get form item group if exists
     *
     * @returns {module:Form.Group|undefined}
     */
    get form_item_group() {
        return this.state.form_item_group;
    };

    /**
     * Set form item entry group id
     *
     * @param {string} value - Form item entry group uuid
     */
    set form_item_entry_group_id(value) {
        this.setState({
            form_item_entry_group_id: value
        });
    };

    /**
     * Get form item entry group id
     *
     * @readonly
     *
     * @returns {?string}
     */
    get form_item_entry_group_id() {
        return this.state.form_item_entry_group_id;
    };

    /**
     * Set form item group rule event instance
     *
     * @param {module:Form/Group/Rule.Event} event
     */
    set form_item_group_rule_event(event) {
        this.setState({
            form_item_group_rule_event: event,
            form_item_group_rule_event_id: event.id
        });
    };

    /**
     * Set form item group rule event id
     *
     * @param {string} value - From item group rule event uuid
     */
    set form_item_group_rule_event_id(value) {
        this.setState({
            form_item_group_rule_event_id: value
        });
    };

    /**
     * Get form item group rule event id
     *
     * @readonly
     *
     * @returns {?string}
     */
    get form_item_group_rule_event_id() {
        return this.state.form_item_group_rule_event_id;
    };

    /**
     * Set source
     *
     * @param {number} value
     */
    set source(value) {
        if (!includes(Base.Source, value)) {
            throw new Error('Source is not valid');
        }
        this.setState({
            source: value
        });
    };

    /**
     * Line item source
     *
     * @readonly
     *
     * @returns {number}
     */
    get source() {
        return this.state.source;
    };

    /**
     * Set name
     *
     * @param {string} value
     */
    set name(value) {
        if (!lang.isString(value)) {
            throw new Error('Name is not valid');
        } else if (value.length < 1 || value.length > 100) {
            throw new Error('Name must be between 1 and 100 characters');
        }
        this.setState({
            name: value
        });
    };

    /**
     * Get name
     *
     * @returns {string}
     */
    getName() {
        return this.state.name;
    };

    /**
     * Get name
     *
     * @readonly
     *
     * @returns {string}
     */
    get name() {
        return this.getName();
    };

    /**
     * Set quantity
     *
     * @param {(string|Decimal)} value
     */
    set quantity(value) {
        if (this.quantity.equals(Number.ofInput(value))) {
            return;
        }
        if (this.state.quantity !== undefined && value !== this.state.quantity) {
            this.state.quantity_changed = true;
        }
        this.setState({
            quantity: value
        });
        // if quantity changes and it's a product or the total has been overwritten,
        // set total to null so that it can be recalculated
        if (this.state.total_override || this.type === Base.Type.PRODUCT) {
            this.setState({
                total: null
            });
        }
    };

    /**
     * Get quantity
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get quantity() {
        return Number.ofInput(this.state.quantity);
    };

    /**
     * Set original amount
     *
     * @param {(string|Decimal)} value
     */
    set original_amount(value) {
        if (this.original_amount.equals(Number.ofInput(value))) {
            return;
        }
        this.setState({
            original_amount: value
        });
    };

    /**
     * Get original amount
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get original_amount() {
        return Number.ofInput(this.state.original_amount);
    };

    /**
     * Get original amount
     *
     * @param {boolean} [formatted=true] - Determines if amount is properly formatted
     * @returns {Decimal}
     */
    getOriginalAmount(formatted = true) {
        let original_amount = this.original_amount;
        if (formatted) {
            original_amount = Number.toCurrency(original_amount);
        }
        return original_amount;
    };

    /**
     * Set amount
     *
     * @param {(string|Decimal)} value
     */
    set amount(value) {
        if (this.amount.equals(Number.ofInput(value))) {
            return;
        }
        this.setState({
            amount: value
        });
    };

    /**
     * Get amount
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get amount() {
        return Number.ofInput(this.state.amount);
    };

    /**
     * Get amount
     *
     * @param {boolean} [formatted=true] - Determines if amount is properly formatted
     * @returns {Decimal}
     */
    getAmount(formatted = true) {
        let amount = this.amount;
        if (formatted) {
            amount = Number.toCurrency(amount);
        }
        return amount;
    };

    /**
     * Get subtotal
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get subtotal() {
        // if type is product and it contains adjustments or components,
        // calculate the total based on quantity, unit price, adjustments, and components
        if (this.type === Base.Type.PRODUCT) {
            let new_value = parseFloat(this.amount.times(this.quantity));
            if (this.state.adjustment !== null) {
                if (this.state.adjustment_type === Base.AdjustmentType.TOTAL) {
                    let adjustment_amount = parseFloat(this.state.adjustment);
                    if (this.state.adjustment_mode === Base.AdjustmentMode.POSITIVE) {
                        new_value = new_value + adjustment_amount;
                    } else {
                        new_value = new_value - adjustment_amount;
                    }
                } else {
                    let adjustment_amount = new_value * (parseFloat(this.state.adjustment)/100);
                    if (this.state.adjustment_mode === Base.AdjustmentMode.POSITIVE) {
                        new_value = new_value + adjustment_amount;
                    } else {
                        new_value = new_value - adjustment_amount;
                    }
                }
            }
            if (this.state.is_component_adjustment && this.state.component_amount !== null) {
                new_value = new_value + parseFloat(this.state.component_amount);
            }
            return Number.roundCurrency(new_value);
        }
        return Number.roundCurrency(this.amount.times(this.quantity));
    };

    /**
     * Get subtotal
     *
     * @param {boolean} [formatted=true] - Determines if subtotal is properly formatted
     * @returns {Decimal}
     */
    getSubtotal(formatted = true) {
        let subtotal = this.subtotal;
        if (formatted) {
            subtotal = Number.toCurrency(subtotal);
        }
        return subtotal;
    };

    /**
     * Set total
     *
     * @param {(string|Decimal)} value
     */
    set total(value) {
        if (this.total.equals(Number.ofInput(value))) {
            return;
        }
        this.setState({
            total: value
        });
    };

    /**
     * Get total
     *
     * @readonly
     *
     * @returns {Decimal}
     */
    get total() {
        if (this.state.total === undefined || this.state.total === null) {
            return this.subtotal;
        }
        return Number.roundCurrency(Number.ofInput(this.state.total));
    };

    /**
     * Determines if price has been overridden
     *
     * @returns {boolean}
     */
    isPriceOverridden() {
        return this.getAmount(true) !== this.getOriginalAmount(true);
    };

    /**
     * Remove total override
    */
    removeTotalOverride() {
        if (this.state.total === null) {
            return;
        }
        this.setState({
            total: null
        });
    };

    isPriceAdjusted() {
        return parseFloat(Number.roundCurrency(this.amount.times(this.quantity))).toFixed(2) !== parseFloat(this.total).toFixed(2);
    };

    /**
     * Get total
     *
     * @param {boolean} [formatted=true] - Determines if total is properly formatted
     * @returns {Decimal}
     */
    getTotal(formatted = true) {
        let total = this.total;
        if (formatted) {
            total = Number.toCurrency(total);
        }
        return total;
    };

    /**
     * Set order
     *
     * @param {number} value
     */
    set order(value) {
        // @todo validate order once it's used
        this.setState({
            order: value
        });
    };

    /**
     * Get order
     *
     * @readonly
     *
     * @returns {number}
     */
    get order() {
        return !lang.isNil(this.state.order) ? this.state.order : 1;
    };

    /**
     * Compare specified line item to internal data and return if they are the same
     *
     * @abstract
     *
     * @param {Base} line_item
     */
    matches(line_item) {};

    /**
     * Merge line item into this one
     *
     * @param {Base} line_item
     * @returns {boolean}
     */
    merge(line_item) {
        if (line_item.type !== this.type) {
            throw new Error('Cannot merge line items of different types');
        }
        line_item.events.emit('merged', this);
        if (this.matches(line_item)) {
            return false;
        }
        let state = line_item.getState(true);
        state.id = this.id;
        state.item_id = this.item_id;
        this.setState(state);
        return true;
    };

    /**
     * Get payload for API save request
     *
     * @param {string} bid_item_id - Bid uuid
     * @returns {Object}
     */
    getPayload(bid_item_id) {
        let payload = {
            id: this.id,
            bid_item_id: bid_item_id,
            type: this.type,
            source: this.source,
            name: this.name,
            quantity: this.quantity.toString(),
            amount: this.amount.toString(),
            original_amount: this.original_amount.toString(),
            subtotal: this.subtotal.toString(),
            total: this.total.toString(),
            order: this.order,
            is_locked: false
        };
        if (!lang.isNil(this.bid_item_section_id)) {
            payload.bid_item_section_id = this.bid_item_section_id;
        }
        if (this.source === Base.Source.FORM_EVENT) {
            payload.form_item_entry_group_id = this.form_item_entry_group_id;
            payload.form_item_group_rule_event_id = this.form_item_group_rule_event_id;
        }
        return payload;
    };

    /**
     * Validate line item
     *
     * @returns {Array}
     */
    validate() {
        let errors = [];
        if (lang.isNil(this.source)) {
            errors.push('No source defined');
        }
        if (!isUuid(this.id, 4)) {
            errors.push('Invalid id');
        }
        if (lang.isNil(this.name)) {
            errors.push('No name defined');
        }
        if (lang.isNil(this.source)) {
            errors.push('No source defined');
        }
        switch (this.source) {
            case Base.Source.FORM_EVENT:
                if (!lang.isNil(this.state.form_item_group) && this.state.form_item_group.isDestroyed()) {
                    errors.push('Form item group no longer exists');
                }
                if (lang.isNil(this.form_item_entry_group_id)) {
                    errors.push('Form item entry group ID not defined');
                }
                if (lang.isNil(this.form_item_group_rule_event_id)) {
                    errors.push('Form item group rule event ID not defined');
                }
                break;
        }
        return errors;
    };

    /**
     * Prepare line item
     *
     * Fetch/prep any necessary data before saving
     *
     * @param {boolean} editing
     * @returns {Promise}
     */
    prepare(editing) {
        return new Promise((resolve, reject) => {
            let errors = this.validate();
            if (errors.length > 0) {
                reject(errors);
                return;
            }
            resolve(this);
        });
    };

    /**
     * Save line item
     *
     * @param {string} bid_item_id - Bid uuid
     * @returns {*}
     */
    save(bid_item_id) {
        this.setStateChanged(false);
        let request = new Api.BatchRequest.Single('bid-item-line-item', 'poly-update-or-create', this.getPayload(bid_item_id));
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, request);
        return request.promise;
    };

    /**
     * Delete item by sending API request, destroy if successful, and emit the respective events
     *
     * @returns {Promise}
     *
     * @emits module:BidCreator/Entities/LineItem/Base~deleted
     */
    delete() {
        return new Promise((resolve, reject) => {
            let request = new Api.BatchRequest.Single('bid-item-line-item', 'delete', {
                id: this.state.id
            });
            request.promise.then((data) => {
                this.events.emit('deleted');
                this.destroy();
                resolve(data);
            }, (error) => {
                console.log(error);
                reject(error);
            });
            PubSub.Handler.publish(PubSub.Topics.Save.SEND_REQUEST, request);
        });
    };

    /**
     * Destroy line item
     *
     * @emits module:BidCreator/Entities/LineItem/Base~destroyed
     */
    destroy() {
        this.events.emit('destroyed');
    };
}

module.exports = Base;

TypeClasses.set(Base.Type.GENERAL, require('./general'));
TypeClasses.set(Base.Type.PRODUCT, require('./product'));
TypeClasses.set(Base.Type.DISCOUNT, require('./discount'));
TypeClasses.set(Base.Type.FEE, require('./fee'));

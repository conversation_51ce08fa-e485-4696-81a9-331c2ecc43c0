@use '~@cac-sass/base';
@use '~@cac-sass/app/form';
@use '~@cac-sass/app/highlight';
@use '~@cac-sass/app/grid';

@use '~@cas-layout-sass/layout';
@use '~@cas-table-sass/table';
@use '~@cas-modal-sass/modal';
@use '~@cas-tooltip-sass/tooltip';

@use '~@cac-sass/lib/callout';

@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/number';
@use '~@cas-form-input-sass/date-time';
@use '~@cas-form-input-sass/uppy';

html {
    @include base.full-width-height;
    overscroll-behavior: none;
}

body {
    @include base.full-width-height;
    overscroll-behavior: none;
    background: linear-gradient(
        135deg,
        var(--primary-color-background, base.$color-primary-default),
        var(--primary-color-hover, #3b82f6)
    );
    background-attachment: fixed;
    @include base.respond-to('<small') {
        background: var(--primary-color-background)
    }
}

.l-public {
    @include base.full-width-height;
}

.a-p-inner {
    @include base.full-width-height;
}

.a-pi-content {
    @include base.full-width-height;
}

.p-payments {
    @include base.full-width-height;
}

.a-pic-logo,
.a-p-image {
    display: none;
}

.m-payment {
    align-items: center;
    display: flex;
    justify-content: center;
    padding: 0;
    position: relative;
    @include base.full-width-height;

    .m-p-pages {
        @include base.full-width-height;
    }

    .p-main-payment {
        align-items: center;
        display: flex;
        justify-content: center;
        padding: base.unit-rem-calc(16px);
        width: 100%;
        height: 100%;
        @include base.respond-to('<large') {
            align-items: flex-start;
        }
    }

    .c-mp-form-container {
        width: 100%;
        height: calc(100dvh - 32px);
    }

    .l-mp-wrapper {
        background-color: white;
        border-radius: base.unit-rem-calc(16px);
        display: flex;
        height: 900px;
        margin: 0 auto;
        overflow: hidden;
        width: 1024px;
        @include base.respond-to('<large') {
            flex-direction: column;
            width: base.unit-rem-calc(696px);
            overflow: auto;
            height: 100%;
        }
        @include base.respond-to('<medium') {
            width: 100%;
        }
    }

    .l-mp-left {
        background-color: base.$color-white-default;
        border-right: 1px solid base.$color-border;
        padding: base.unit-rem-calc(40px);
        width: 50%;
        @include base.respond-to('<large') {
            width: 100%;
            border-right: none;
            border-bottom: 1px solid base.$color-border;
            padding: base.unit-rem-calc(24px);

            .c-mp-logo {
                margin-bottom: base.unit-rem-calc(16px);

                .c-mp-logo__image {
                    max-width: 200px;
                    height: auto;
                    @include base.respond-to('<small') {
                        max-width: 150px;
                    }
                }
            }
        }

        .t-mp-title {
            margin: base.unit-rem-calc(16px) 0;
        }

        .c-f-wrapper {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(4px);
            overflow-y: auto;
            @include base.respond-to('<large') {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                grid-column-gap: base.unit-rem-calc(16px);
                grid-row-gap: base.unit-rem-calc(8px);
            }
            @include base.respond-to('<small') {
                grid-template-columns: repeat(1, 1fr);
                grid-row-gap: base.unit-rem-calc(8px);
            }
        }
    }

    .l-mp-right {
        position: relative;
        width: 50%;
        @include base.respond-to('<large') {
            width: 100%;
            position: relative;
        }
    }

    .c-mp-logo {
        display: flex;
        justify-content: center;
    }

    .c-mp-content {
        background-color: base.$color-background-form;
        height: 100%;
        padding: base.unit-rem-calc(40px);
        @include base.respond-to('<large') {
            padding: base.unit-rem-calc(24px);
            margin-bottom: base.unit-rem-calc(155px);
        }

        .c-mpc-tabs {
            background-color: base.$color-background;
            border-radius: base.unit-rem-calc(12px);
            cursor: pointer;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            margin-bottom: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(6px);

            .f-f-iframe-wrapper.error {
                color: base.$color-grey-dark-4;
                background-color: base.$color-white-default;
                border: base.unit-rem-calc(1px) base.$form-input-error-border-color solid;
                border-radius: base.unit-rem-calc(3px);
                box-shadow: 0 0 0 base.unit-rem-calc(4px) base.$form-input-error-border-box-color;
            }

        }

        .c-mpc-tab {
            align-items: center;
            border-radius: base.unit-rem-calc(6px);
            color: base.$color-grey-dark-1;
            cursor: pointer;
            display: flex;
            font-weight: 500;
            gap: base.unit-rem-calc(8px);
            justify-content: center;
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            text-align: center;

            svg[data-icon] {
                height: base.unit-rem-calc(15px);
                width: base.unit-rem-calc(15px);
            }

            &.is-active {
                background-color: base.$color-white-default;
                color: base.$color-grey-dark-2;
            }
        }

        .c-f-wrapper {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(8px);

        }

        .f-field__input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(8px);

            .f-f-input {
                flex: 1;
            }
        }

        .c-card-brand-display {
            border-radius: base.unit-rem-calc(3px);
            flex-shrink: 0;
            display: none;
            align-items: center;
            justify-content: center;
            height: 2rem;
            color: base.$color-grey-dark-2;
            font-size: base.unit-rem-calc(10px);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 base.unit-rem-calc(8px);
            background-color: base.$color-background;
            border: 1px solid base.$color-grey-light-3;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s ease-in-out;
            pointer-events: none;
            white-space: nowrap;

            &.is-visible {
                display: flex !important;
                opacity: 1;
                transform: scale(1);
            }

        }

        .l-f-flex {
            display: grid;
            gap: base.unit-rem-calc(8px);
            grid-template-columns: repeat(2, 1fr);
            @include base.respond-to('<large') {
                grid-template-columns: 1fr;
                gap: base.unit-rem-calc(8px);
            }
        }

        .l-f-flex-three {
            display: grid;
            gap: base.unit-rem-calc(8px);
            grid-template-columns: repeat(3, 1fr);
            @include base.respond-to('<large') {
                grid-template-columns: 1fr;
                gap: base.unit-rem-calc(8px);
            }
        }

        .f-radio-item {
            align-items: baseline;
            display: flex;
            gap: base.unit-rem-calc(4px);
        }
    }

    .c-mp-footer {
        background-color: base.$color-white-default;
        border-top: 1px solid base.$color-border;
        bottom: 0;
        padding: base.unit-rem-calc(16px);
        position: absolute;
        width: 100%;
        @include base.respond-to('<large') {
            bottom: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(24px);
            position: fixed;
            z-index: 1000;
            width: base.unit-rem-calc(696px);
            border-radius: 0 0 base.unit-rem-calc(16px) base.unit-rem-calc(16px);
        }
        @include base.respond-to('<medium') {
            width: calc(100% - 32px);
        }
    }

    .c-mp-callout {
        display: none;
        margin-bottom: base.unit-rem-calc(16px);

        &.is-success {
            @include callout.success;
            display: block;
        }

        &.is-error {
            @include callout.error;
            display: block;
        }
    }

    .c-mp-footer__wrapper {
        margin-bottom: base.unit-rem-calc(8px);

        .c-mp-footer__item {
            align-items: center;
            color: base.$color-grey-dark-2;
            display: flex;
            font-weight: 500;

            &::before {
                background: base.$color-grey-light-4;
                content: "";
                flex: 1;
                height: 1px;
                margin: 0 base.unit-rem-calc(8px);
                order: 1;
            }

            &.large {
                font-size: base.unit-rem-calc(16px);
                line-height: base.unit-rem-calc(20px);
                font-weight: 500;
            }

            .t-footer-title {
                flex-shrink: 0;
                font-weight: 500;
                margin: 0;
                order: 0;
            }

            .c-mp-footer__value {
                flex-shrink: 0;
                margin: 0;
                order: 2;
            }
        }
    }

    .c-ps-container {
        float: right;
        display: flex;
        gap: 1em;
        align-items: center;
        @include base.respond-to('<large') {
            float: none;
            justify-content: right;
            margin-top: base.unit-rem-calc(16px);
        }

        button {
            border-radius: base.unit-rem-calc(6px);
        }

        .loading-spinner-container {
            display: none;
            height: base.unit-rem-calc(24px);
            width: base.unit-rem-calc(24px);
            animation: spin 1s linear infinite;

            &:global(.show) {
                display: inline-block;
            }
        }
    }

    .c-psc-submit {
        @include base.button-text-primary;
        background-color: var(--primary-color-background, base.$color-primary-default);
        border: 1px solid var(--primary-color-background, base.$color-primary-default);
        color: var(--primary-color-text, base.$color-white-default);

        align-items: center;
        display: flex;
        gap: base.unit-rem-calc(8px);

        &:hover {
            background-color: var(--primary-color-hover, base.$color-primary-light-1);
            border: 1px solid var(--primary-color-hover, base.$color-primary-light-1);
            color: var(--primary-color-text-hover, base.$color-white-default);
        }
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    // TEMPORARY TEST BUTTON STYLES - REMOVE WHEN NO LONGER NEEDED
    .c-test-fill-button {
        @include base.button-text-secondary;
        background-color: base.$color-grey-light-2;
        border: 1px solid base.$color-grey-light-3;
        color: base.$color-grey-dark-2;
        margin-right: base.unit-rem-calc(8px);

        &:hover {
            background-color: base.$color-grey-light-3;
            border: 1px solid base.$color-grey-light-4;
        }

        &::before {
            content: "DEV: ";
            font-size: base.unit-rem-calc(10px);
            opacity: 0.7;
        }
    }
    // END TEMPORARY TEST BUTTON STYLES
}

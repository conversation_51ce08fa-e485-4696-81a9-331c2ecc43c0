<div class="p-main-payment">
    <form class="c-mp-form-container" data-js="ca-p-form">
        <div class="l-mp-wrapper">
        <div class="l-mp-left">
            <div class="c-mp-logo">
                <img src="" alt="Company Logo" class="c-mp-logo__image" data-js="company-logo">
            </div>
            <h3 class="t-mp-title">Make Payment</h3>

            <div class="c-f-wrapper">
                <div class="f-field">
                    <label class="f-f-label" for="payer_first_name">First Name</label>
                    <input class="f-f-input" id="payer_first_name" name="payer_first_name" type="text"
                           data-js="payer_first_name"/>
                </div>
                <div class="f-field">
                    <label class="f-f-label" for="payer_last_name">Last Name</label>
                    <input class="f-f-input" id="payer_last_name" name="payer_last_name" type="text"
                           data-js="payer_last_name"/>
                </div>
                <div class="f-field">
                    <label class="f-f-label" for="payer_email">Email</label>
                    <input class="f-f-input" id="payer_email" name="payer_email" type="email"
                            data-js="payer_email"/>
                </div>
                <div class="f-field">
                    <label class="f-f-label" for="payer_phone">Phone</label>
                    <input class="f-f-input" id="payer_phone" name="payer_phone" type="tel" data-js="payer_phone"/>
                </div>
                <div class="f-field">
                    <label class="f-f-label" for="payment_amount">Amount</label>
                    <input class="f-f-input" id="payment_amount" name="payment_amount" type="text"
                           data-js="payment_amount" data-fx-form-input="number"/>
                </div>
                <div class="f-field">
                    <label class="f-f-label" for="invoice_number">Invoice Number <small>(Optional)</small></label>
                    <input class="f-f-input" id="invoice_number" name="invoice_number" type="text"
                           data-js="invoice_number"/>
                </div>
            </div>
        </div>

        <div class="l-mp-right">
            <div class="c-mp-content">
                <div class="c-mpc-tabs" data-js="payment-tabs">
                    <div class="c-mpc-tab is-active" data-js="credit-card-tab"><svg data-icon><use xlink:href="#remix-icon--finance--bank-card-2-line"></use></svg>Credit/Debit</div>
                    <div class="c-mpc-tab" data-js="ach-tab"><svg data-icon><use xlink:href="#remix-icon--buildings--bank-line"></use></svg>Bank Account</div>
                </div>

                <div class="c-f-wrapper" data-js="credit-card-tab-content">
                    <div class="f-field">
                        <label class="f-f-label" for="cardholder_name">Cardholder Name</label>
                        <input class="f-f-input" id="cardholder_name" name="cardholder_name" type="text" data-js="cardholder_name"/>
                    </div>

                    <div class="f-field">
                        <label class="f-f-label" for="card_number">Card Number</label>
<!--                        <div class="f-f-iframe-wrapper" id="iframe-container"></div>-->
                        <div class="f-field__input-wrapper">
                            <input class="f-f-input" type="text" id="card_number" name="card_number" data-js="card_number"/>
                            <div class="c-card-brand-display" data-js="card-brand-display"></div>
                        </div>
                        <input type="hidden" id="card_brand" name="card_brand" data-js="card_brand"/>
                    </div>

                    <div class="l-f-flex">
                        <div class="f-field">
                            <label class="f-f-label" for="card_expiry">Expiry (MM/YY)</label>
                            <input class="f-f-input" id="card_expiry" name="card_expiry" type="text" 
                                   maxlength="5" data-js="card_expiry"/>
                        </div>
                        <div class="f-field">
                            <label class="f-f-label" for="card_cvv">CVV</label>
                            <input class="f-f-input" id="card_cvv" name="card_cvv" type="text" 
                                   maxlength="4" data-js="card_cvv"/>
                        </div>
                    </div>

                    <div class="l-f-flex">
                        <div class="f-field">
                            <label class="f-f-label" for="billing_address">Billing Address</label>
                            <input class="f-f-input" id="billing_address" name="billing_address" type="text" data-js="billing_address"/>
                        </div>
                        <div class="f-field">
                            <label class="f-f-label" for="billing_address2">Address 2 <small>(Optional)</small></label>
                            <input class="f-f-input" id="billing_address2" name="billing_address2" type="text" data-js="billing_address2"/>
                        </div>
                    </div>

                    <div class="l-f-flex-three">
                        <div class="f-field">
                            <label class="f-f-label" for="billing_city">City</label>
                            <input class="f-f-input" id="billing_city" name="billing_city" type="text" data-js="billing_city"/>
                        </div>
                        {{{states_component}}}
                        <div class="f-field">
                            <label class="f-f-label" for="billing_zip">Zip Code</label>
                            <input class="f-f-input" id="billing_zip" name="billing_zip" type="text" data-js="billing_zip"/>
                        </div>
                    </div>
                </div>

                <div class="c-f-wrapper" data-js="ach-tab-content">
                    <div class="f-field">
                        <label class="f-f-label" for="ach_account_name">Account Name</label>
                        <input class="f-f-input" id="ach_account_name" name="ach_account_name" type="text" data-js="ach_account_name"/>
                    </div>

                    <div class="f-field">
                        <label class="f-f-label">Account Type</label>
                        <div class="f-radio-group">
                            <div class="f-radio-item">
                                <input class="f-f-radio" id="ach_account_type_savings" name="ach_account_type" type="radio" value="savings" data-js="ach_account_type"/>
                                <label class="f-f-radio-label" for="ach_account_type_savings">Savings</label>
                            </div>
                            <div class="f-radio-item">
                                <input class="f-f-radio" id="ach_account_type_checking" name="ach_account_type" type="radio" value="checking" data-js="ach_account_type" checked/>
                                <label class="f-f-radio-label" for="ach_account_type_checking">Checking</label>
                            </div>
                        </div>
                    </div>

                    <div class="l-f-flex">
                        <div class="f-field">
                            <label class="f-f-label" for="ach_account_number">Account Number</label>
                            <input class="f-f-input" id="ach_account_number" name="ach_account_number" type="text" data-js="ach_account_number"/>
                        </div>

                        <div class="f-field">
                            <label class="f-f-label" for="ach_routing_number">Routing Number</label>
                            <input class="f-f-input" id="ach_routing_number" name="ach_routing_number" type="text" data-js="ach_routing_number"/>
                        </div>
                    </div>

                    <div class="l-f-flex">
                        <div class="f-field">
                            <label class="f-f-label" for="ach_billing_address">Billing Address</label>
                            <input class="f-f-input" id="ach_billing_address" name="ach_billing_address" type="text" data-js="ach_billing_address"/>
                        </div>
                        <div class="f-field">
                            <label class="f-f-label" for="ach_billing_address2">Address 2 <small>(Optional)</small></label>
                            <input class="f-f-input" id="ach_billing_address2" name="ach_billing_address2" type="text" data-js="ach_billing_address2"/>
                        </div>
                    </div>

                    <div class="l-f-flex-three">
                        <div class="f-field">
                            <label class="f-f-label" for="ach_billing_city">City</label>
                            <input class="f-f-input" id="ach_billing_city" name="ach_billing_city" type="text" data-js="ach_billing_city"/>
                        </div>
                        {{{ach_states_component}}}
                        <div class="f-field">
                            <label class="f-f-label" for="ach_billing_zip">Zip Code</label>
                            <input class="f-f-input" id="ach_billing_zip" name="ach_billing_zip" type="text" data-js="ach_billing_zip"/>
                        </div>
                    </div>
                </div>
            </div>

            <div class="c-mp-footer">
                <div class="c-mp-callout" data-js="payment-callout"></div>
                <div class="c-mp-footer__wrapper">
                    <div class="c-mp-footer__item">
                        <p class="t-footer-title">Payment Amount</p>
                        <div class="c-mp-footer__value" data-js="payment-amount-display"></div>
                    </div>
                    <div class="c-mp-footer__item">
                        <p class="t-footer-title" data-js="credit-card-fee-label">Convenience Fee</p>
                        <div class="c-mp-footer__value" data-js="credit-card-processing-fee-display"></div>
                    </div>
                    <div class="c-mp-footer__item large">
                        <p class="t-footer-title">Total</p>
                        <div class="c-mp-footer__value" data-js="payment-total"></div>
                    </div>
                </div>
                <div class="c-ps-container">
                    <!-- TEMPORARY TEST BUTTON - REMOVE WHEN NO LONGER NEEDED -->
                    <button class="c-test-fill-button" type="button" data-js="fill-test-data-button">Fill Test Data</button>
                    <!-- END TEMPORARY TEST BUTTON -->

                    <svg class="loading-spinner-container" data-js="submit-spinner"><use xlink:href="#loader--spinning-circle"></use></svg>
                    <button class="c-psc-submit" type="submit" data-js="submit-payment-button">
                        Submit Payment
                    </button>
                </div>
            </div>
        </div>
    </div>
    </form>
</div>

import $ from 'jquery';
import * as UI from './ui-enhancer';

/**
 * Payment Integrations Manager
 * Handles initialization and setup of external integrations for the payment form
 */
export class PaymentIntegrations {
    constructor(tokenizer, formElements) {
        this.tokenizer = tokenizer;
        this.elem = formElements;
    }

    /**
     * Initialize all payment integrations
     */
    initialize() {
        this.setupReCaptcha();
        this.setupTokenizer();
        this.setupIframeReadyListener();
    }

    /**
     * Setup Google reCAPTCHA integration
     */
    setupReCaptcha() {
        const captcha_key = window.payments_data?.captcha_key;
        
        if (!captcha_key) {
            console.error('reCAPTCHA key not found in payments_data');
        }

        this.loadReCaptchaScript(captcha_key);
        this.setupReCaptchaExecutionListener(captcha_key);
        this.hideReCaptchaBadge();
    }

    /**
     * Load reCAPTCHA script dynamically
     * @param {string} captcha_key - Google reCAPTCHA site key
     */
    loadReCaptchaScript(captcha_key) {
        if (!window.grecaptcha) {
            const recaptcha_script = document.createElement('script');
            recaptcha_script.src = `https://www.google.com/recaptcha/api.js?render=${captcha_key}`;
            recaptcha_script.async = true;
            recaptcha_script.defer = true;
            document.head.appendChild(recaptcha_script);
        }
    }

    /**
     * Setup reCAPTCHA execution event listener
     * @param {string} captcha_key - Google reCAPTCHA site key
     */
    setupReCaptchaExecutionListener(captcha_key) {
        document.addEventListener('trigger_recaptcha_execution', () => {
            if (!window.grecaptcha) {
                console.error('reCAPTCHA not loaded');
                return;
            }

            window.grecaptcha.ready(() => {
                window.grecaptcha.execute(captcha_key, { action: 'payment_submit' })
                    .then((token) => {
                        this.handleReCaptchaToken(token);
                    })
                    .catch((error) => {
                        console.error('reCAPTCHA execution failed:', error);
                    });
            });
        });
    }

    /**
     * Handle reCAPTCHA token and trigger form submission
     * @param {string} token - reCAPTCHA token
     */
    handleReCaptchaToken(token) {
        // Create or update hidden input for the token
        let token_input = document.getElementById('recaptcha_token');
        if (!token_input) {
            token_input = document.createElement('input');
            token_input.type = 'hidden';
            token_input.id = 'recaptcha_token';
            token_input.name = 'recaptcha_token';
            this.elem.form.append(token_input);
        }
        token_input.value = token;
        const event = new CustomEvent('recaptcha_token_set', { token: token});
        document.dispatchEvent(event);
    }

    /**
     * Hide reCAPTCHA badge from UI
     */
    hideReCaptchaBadge() {
        $(document).ready(() => {
            const interval_id = setInterval(() => {
                const $recaptcha_badge = $('.grecaptcha-badge');
                if ($recaptcha_badge.length) {
                    $recaptcha_badge.css('visibility', 'hidden');
                    clearInterval(interval_id);
                }
            }, 50);
        });
    }

    /**
     * Setup CardConnect tokenizer
     */
    setupTokenizer() {
        this.tokenizer.initialize('cardholder_name');
    }

    /**
     * Setup iframe ready event listener for skeleton loading
     */
    setupIframeReadyListener() {
        document.addEventListener('iframe-ready', () => {
            // Add a small delay to ensure the iframe loaded its styles successfully
            setTimeout(() => {
                UI.hideSkeletonLoading(this.elem.form, this.elem.skeleton);
            }, 1000);
        });
    }

    /**
     * Clean up event listeners and integrations
     */
    destroy() {
        // Remove event listeners
        document.removeEventListener('trigger_recaptcha_execution', this.setupReCaptchaExecutionListener);
        document.removeEventListener('iframe-ready', this.setupIframeReadyListener);
        
        // Clean up tokenizer
        if (this.tokenizer && typeof this.tokenizer.destroy === 'function') {
            this.tokenizer.destroy();
        }
    }
}
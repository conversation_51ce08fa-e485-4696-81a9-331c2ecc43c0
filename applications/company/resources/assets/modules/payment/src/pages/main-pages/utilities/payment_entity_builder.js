import $ from 'jquery';

export default class PaymentEntityBuilder {
  constructor({ form, validator, credit_card_tab_content, ach_tab_content }) {
    this.form = form;
    this.validator = validator;
    this.credit_card_tab_content = credit_card_tab_content;
    this.ach_tab_content = ach_tab_content;
  }

  build() {
    const form_data = new FormData();
    this._appendFields(form_data, this._getCommonFields());
    this._appendMetadataFields(form_data);

    if (this._isCreditTabActive()) {
      this._appendFields(form_data, this._getCreditCardFields());
    } else {
      this._appendFields(form_data, this._getAchFields());
    }

    return form_data;
  }

  _appendFields(form_data, field_names) {
    field_names.forEach(field_name => {
      const $element = this.validator.getInputElem(field_name) || this.form.find(`[name="${field_name}"]`);
      if ($element?.length) {
        const value = $element.val();
        if (value != null && value !== '') {
          form_data.append($element.attr('name') || field_name, value);
        }
      }
    });
  }

  _appendMetadataFields(form_data) {
    // Find all hidden fields with metadata_ prefix
    const metadata_fields = this.form.find('input[type="hidden"][name^="metadata_"]');
    
    metadata_fields.each(function() {
      const $field = $(this);
      const field_name = $field.attr('name');
      const value = $field.val();
      
      if (value != null && value !== '') {
        form_data.append(field_name, value);
      }
    });
  }

  _isCreditTabActive() {
    return this.credit_card_tab_content.is(':visible');
  }

  _getCommonFields() {
    return [
      'payment_amount',
      'credit_card_processing_fee',
      'total_amount',
      'payer_first_name',
      'payer_last_name',
      'payer_email',
      'payer_phone',
      'invoice_number'
    ];
  }

  _getCreditCardFields() {
    return [
      'cardholder_name',
      'card_number',
      'card_expiry',
      'card_cvv',
      'card_brand',
      'billing_address',
      'billing_address2',
      'billing_city',
      'billing_state',
      'billing_zip'
    ];
  }

  _getAchFields() {
    return [
      'ach_account_name',
      'ach_routing_number',
      'ach_account_number',
      'ach_account_type',
      'ach_billing_address',
      'ach_billing_city',
      'ach_billing_state',
      'ach_billing_zip'
    ];
  }
}
'use strict';

import $ from 'jquery';
import Page from '@ca-package/router/src/page';
const {findChild, jsSelector} = require("@ca-package/dom");

import FormInput from "@cas-form-input-js";
const FormValidator = require("@cas-validator-js");
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(require('@ca-submodule/form-input/src/dropdown'));
FormInput.use(NumberInput);

import validation_rules from './utilities/validation.js';
import PaymentEntityBuilder from "./utilities/payment_entity_builder";
import * as UI from "./utilities/ui-enhancer";
import { getCardType } from '@cas-validator-js/validators/payment';
import { CardConnectTokenizer } from "./utilities/tokenizer";
import { PaymentIntegrations } from "./utilities/integrations";
import { fillTestData } from "./utilities/_testing";
import manager_tpl from '@cam-payment-tpl/pages/main-pages/manager.hbs';
import '@cac-loader/spinning-circle.svg';
const state_options = [
    { label: 'States', options: require('@cac-js/data/states') },
    { label: 'US Territories', options: require('@cac-js/data/us_territories') },
    { label: 'Provinces', options: require('@cac-js/data/provinces') }
];


export class Manager extends Page {

    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            validator: null,
            action_url: window.payments_data?.action_url || null,
            integrations: null,
            tokenizer: null,
        });


        this.state.tokenizer = new CardConnectTokenizer();
        this.elem.billing_state = UI.buildSelectField('State', 'billing_state', state_options);
        this.elem.ach_billing_state = UI.buildSelectField('State', 'ach_billing_state', state_options);
    };


    async save() {
        this.elem.submit_spinner.show();
        UI.setFormDisabled(this.elem.form, true);
        this.elem.payment_callout.removeClass('is-success is-error').text(''); // Clear any existing callouts
        
        try {
            const builder = new PaymentEntityBuilder({
                form: this.elem.form,
                validator: this.state.validator,
                credit_card_tab_content: this.elem.credit_card_tab_content,
                ach_tab_content: this.elem.ach_tab_content
            });
            const form_data = builder.build();

            form_data.append('recaptcha_token', $('#recaptcha_token').val() || '');
            const url = this.elem.form.attr('action');
            const resp = await fetch(url, { method: 'POST', body: form_data });
            const data = await resp.json();

            if (!resp.ok) {
                this._handlePaymentError(data);
                return;
            }

            // Success - show success message, reset form, and clear displays
            UI.showSuccessCallout(this.elem.payment_callout, 'Payment processed successfully! Thank you for your payment.');
            this.state.validator.reset();
            this._clearPaymentDisplays();

        } catch(e) {
            console.error('Error saving payment:', e);
            this._handlePaymentError({});
        } finally {
            this.elem.submit_spinner.hide();
            UI.setFormDisabled(this.elem.form, false);
        }
    }

    /**
     * Handle payment processing errors with detailed user messaging
     * @param {Object} data - Response data from the payment API
     */
    _handlePaymentError(data = {}) {
        let errorMessage = data.error || 'Payment processing failed';

        // Add gateway and card response codes if available
        const codes = [];
        if (data.gateway_response_code) codes.push(`G${data.gateway_response_code}`);
        if (data.card_response_code) codes.push(`C${data.card_response_code}`);
        
        if (codes.length > 0) {
            errorMessage += ` [${codes.join(':')}]`;
        }
        
        const messages = [];
        if (data.card_response_message) messages.push(data.card_response_message);
        
        if (messages.length > 0) {
            errorMessage += ': ' + messages.join('. ');
        }

        errorMessage += '<br><br>Please try again or contact support if this issue persists.';
        UI.showErrorCallout(this.elem.payment_callout, errorMessage);
    }


    _initializeDOMElements(root) {
        if (!root || !root.length) {
            throw new Error('Root element is required for payment manager initialization');
        }
        
        // Core form elements
        this.elem.form = findChild(this.elem.root, jsSelector('ca-p-form'));
        if (!this.elem.form || !this.elem.form.length) {
            throw new Error('Payment form element not found - required for payment processing');
        }
        
        this.elem.loader = findChild(root, jsSelector('loader'));
        this.elem.container = findChild(root, jsSelector('form-container'));
        this.elem.submit_button = findChild(root, jsSelector('submit-payment-button'));

        // Tab elements
        this.elem.credit_card_tab = findChild(root, jsSelector('credit-card-tab'));
        this.elem.credit_card_tab_content = findChild(root, jsSelector('credit-card-tab-content'));
        this.elem.ach_tab = findChild(root, jsSelector('ach-tab'));
        this.elem.ach_tab_content = findChild(root, jsSelector('ach-tab-content'));

        // Payment display elements
        this.elem.payment_total = findChild(root, jsSelector('payment-total'));
        this.elem.credit_card_processing_fee_display = findChild(root, jsSelector('credit-card-processing-fee-display'));
        this.elem.credit_card_fee_label = findChild(root, jsSelector('credit-card-fee-label'));
        this.elem.payment_amount_display = findChild(root, jsSelector('payment-amount-display'));
        this.elem.payment_amount_input = findChild(root, jsSelector('payment_amount'));
        
        // Token input element
        this.elem.card_token_input = findChild(root, jsSelector('card_number'));
        
        // Card brand display element
        this.elem.card_brand_display = findChild(root, jsSelector('card-brand-display'));
        
        // Card brand input element (hidden field to store detected brand)
        this.elem.card_brand_input = findChild(root, jsSelector('card_brand'));
        
        // Submit spinner element
        this.elem.submit_spinner = findChild(root, jsSelector('submit-spinner'));
        if (!this.elem.submit_spinner || !this.elem.submit_spinner.length) {
            console.warn('Submit spinner element not found - loading states may not display properly');
        }
        
        // Payment callout element
        this.elem.payment_callout = findChild(root, jsSelector('payment-callout'));
        if (!this.elem.payment_callout || !this.elem.payment_callout.length) {
            console.warn('Payment callout element not found - error/success messages may not display properly');
        }
        
        this.elem.fill_test_data_button = findChild(root, jsSelector('fill-test-data-button'));
    }

    _setupValidator() {
        if (!this.state.action_url) {
            throw new Error('Action URL not set in payments_data - required for form submission');
        }
        
        if (!this.elem.form || !this.elem.form.length) {
            throw new Error('Form element not found - cannot setup validator');
        }
        
        this.elem.form.attr('action', this.state.action_url);

        this.state.validator = FormValidator.create(this.elem.form, validation_rules, {
            validate_event: true,
            error_event: true,
            excluded: 'input:hidden, select:hidden, textarea:hidden'
        })
            .on('submit', () => {
                if (!this.state.validator.hasErrors()) {
                    const event = new CustomEvent('trigger_recaptcha_execution');
                    document.dispatchEvent(event);
                }
            })
            .on('error', () => {
                if (!this.state.tokenizer.validateCardNumber(this.elem.credit_card_tab_content)) {
                    return false;
                }
            });
    }

    _setupPaymentTabs() {
        UI.renderTabs(this.elem.root);

        // Setup tab click handlers
        this.elem.ach_tab.on('click', () => {
            UI.setActiveTab(this.elem.root,'ach');
            this._updatePaymentDisplays();
        });
        this.elem.credit_card_tab.on('click', () => {
            UI.setActiveTab(this.elem.root, 'credit_card');
            this._updatePaymentDisplays();
        });
    }

    _updatePaymentDisplays() {
        UI.updatePaymentDisplays(this.elem.form, this.elem);
    }

    _clearPaymentDisplays() {
        UI.updateCurrencyDisplay(this.elem.payment_amount_display, 0);
        UI.updateCurrencyDisplay(this.elem.credit_card_processing_fee_display, 0);
        UI.updateCurrencyDisplay(this.elem.payment_total, 0);
        this._updateCardBrandDisplay('');
    }

    /**
     * Update card brand display based on card number input
     * @param {string} cardNumber - The credit card number
     */
    _updateCardBrandDisplay(cardNumber) {
        if (!this.elem.card_brand_display || !this.elem.card_brand_display.length) {
            return;
        }

        if (!cardNumber || cardNumber.trim() === '') {
            // Clear display when input is empty
            this.elem.card_brand_display.text('').removeClass('is-visible');
            // Clear hidden field value
            if (this.elem.card_brand_input && this.elem.card_brand_input.length) {
                this.elem.card_brand_input.val('');
            }
            return;
        }

        try {
            const cardType = getCardType(cardNumber);
            if (cardType && cardType.niceType) {
                this.elem.card_brand_display
                    .text(cardType.niceType)
                    .addClass('is-visible')
                    .removeClass('card-type-visa card-type-mastercard card-type-american-express card-type-discover card-type-jcb card-type-diners-club card-type-maestro card-type-unionpay')
                    .addClass(`card-type-${cardType.type}`);
                    
                // Set the hidden field value with the card brand type
                if (this.elem.card_brand_input && this.elem.card_brand_input.length) {
                    this.elem.card_brand_input.val(cardType.niceType);
                }
            } else {
                // Clear display when card type cannot be determined
                this.elem.card_brand_display.text('').removeClass('is-visible');
                // Clear hidden field value
                if (this.elem.card_brand_input && this.elem.card_brand_input.length) {
                    this.elem.card_brand_input.val('');
                }
            }
        } catch (error) {
            console.warn('Error detecting card brand:', error);
            this.elem.card_brand_display.text('').removeClass('is-visible');
            // Clear hidden field value on error
            if (this.elem.card_brand_input && this.elem.card_brand_input.length) {
                this.elem.card_brand_input.val('');
            }
        }
    }

    _setupPaymentAmountHandling() {
        const updatePaymentDisplays = () => {
            this._updatePaymentDisplays();
        };

        // Initialize currency input
        this.state.payment_amount_input = FormInput.init(this.elem.payment_amount_input, {
            type: NumberInput.Type.CURRENCY,
            right_align: true,
            allow_minus: false
        });

        // Setup payment amount change handlers
        this.elem.payment_amount_input.on('input change', updatePaymentDisplays);

        // Initialize with default values
        updatePaymentDisplays();
    }

    _applyPrefillData() {
        const _prefillField = (field_name, value) => {
            if (!value) return;

            const $field = this.elem.form.find(`[name="${field_name}"]`);
            if ($field.length > 0) {
                // Handle dropdown fields specially (for state selectors)
                if ($field.attr('data-fx-form-input') === 'dropdown') {
                    $field.val(value).trigger('change');
                } else {
                    $field.val(value);
                }
            }
        };

        const prefill_data = window.payments_data?.prefill_data;
        if (!prefill_data) {
            return;
        }

        // Customer information
        if (prefill_data.customer) {
            _prefillField('payer_first_name', prefill_data.customer.first_name);
            _prefillField('payer_last_name', prefill_data.customer.last_name);
            _prefillField('payer_email', prefill_data.customer.email);
            _prefillField('payer_phone', prefill_data.customer.phone);
        }

        // Billing address (for both credit card and ACH tabs)
        if (prefill_data.address) {
            // Credit card billing address
            _prefillField('billing_address', prefill_data.address.billing_address);
            _prefillField('billing_city', prefill_data.address.billing_city);
            _prefillField('billing_state', prefill_data.address.billing_state);
            _prefillField('billing_zip', prefill_data.address.billing_zip);
            
            // ACH billing address (also prefill these)
            _prefillField('ach_billing_address', prefill_data.address.billing_address);
            _prefillField('ach_billing_city', prefill_data.address.billing_city);
            _prefillField('ach_billing_state', prefill_data.address.billing_state);
            _prefillField('ach_billing_zip', prefill_data.address.billing_zip);
        }

        // Payment amount
        if (prefill_data.amount) {
            const amount_in_dollars = parseFloat(prefill_data.amount).toFixed(2);
            this.elem.payment_amount_input.val(amount_in_dollars);
            this._updatePaymentDisplays();
        }

        // Invoice number
        if (prefill_data.invoice_number) {
            _prefillField('invoice_number', prefill_data.invoice_number);
        }
    }

    _applyUIEnhancements(root) {
        UI.applyInputMasks(this.state.validator, root);
        UI.applyBranding(this.elem.root);
        UI.initializeRadioButtons(root, 'ach_account_type');
    }

    _addMetadataFields() {
        // Get URL parameters for metadata
        const urlParams = new URLSearchParams(window.location.search);
        const metadataFields = ['invoice_type', 'bid_id', 'invoice_order'];
        
        metadataFields.forEach(field => {
            const value = urlParams.get(field);
            if (value) {
                // Check if hidden field already exists
                let $hiddenField = this.elem.form.find(`input[name="metadata_${field}"]`);
                
                if ($hiddenField.length === 0) {
                    // Create hidden field with metadata_ prefix
                    $hiddenField = $('<input>')
                        .attr('type', 'hidden')
                        .attr('name', `metadata_${field}`)
                        .attr('value', value);
                    
                    this.elem.form.append($hiddenField);
                } else {
                    // Update existing field value
                    $hiddenField.val(value);
                }
            }
        });
    }

    _initializeIntegrations() {
        this.state.integrations = new PaymentIntegrations(this.state.tokenizer, this.elem);
        this.state.integrations.initialize();
    }

    /**
     * Setup card brand detection event listeners
     */
    _setupCardBrandDetection() {
        if (!this.elem.card_token_input || !this.elem.card_token_input.length) {
            console.warn('Card number input not found - card brand detection will not work');
            return;
        }

        // Set up event listener for card number input changes
        this.elem.card_token_input.on('input keyup paste', (event) => {
            const cardNumber = $(event.target).val() || '';
            this._updateCardBrandDisplay(cardNumber);
        });
    }

    /**
     * Setup test button visibility and functionality
     */
    _setupTestButton() {
        const environment = window.payments_data?.environment || 'PROD';
        const url_params = new URLSearchParams(window.location.search);
        const testing_enabled = url_params.get('testing') === 'true';
        
        if (environment !== 'PROD' && testing_enabled) {
            this.elem.fill_test_data_button.on('click', () => {
                fillTestData(this.elem, this.elem.form);
            });
        } else {
            this.elem.fill_test_data_button.hide();
        }
    }

    boot(root) {
        super.boot(root);
        this.elem.root = root;

        this._initializeDOMElements(root);

        this._setupValidator();
        this._setupPaymentTabs();
        this._setupPaymentAmountHandling();
        this._applyPrefillData();
        this._addMetadataFields(); // Add metadata fields from URL parameters
        this._applyUIEnhancements(root);

        this._initializeIntegrations();
        this._setupCardBrandDetection();
        this._setupTestButton();

        document.addEventListener('recaptcha_token_set', (event) => this.save());
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return manager_tpl({
            states_component: this.elem.billing_state,
            ach_states_component: this.elem.ach_billing_state
        });
    };
}
'use strict';

export const SMALL_LENGTH  = 25;
export const MEDIUM_LENGTH = 50;
export const MAX_LENGTH    = 200;

export const validation_rules = {
    payment_amount: {
        required: true,
        requiredMessage: 'This field is required',
        currency: true,
        title: 'Please enter a valid payment amount'
    },
    payer_first_name: {
        required: true,
        requiredMessage: 'This field is required',
        maxlength: MEDIUM_LENGTH
    },
    payer_last_name: {
        required: true,
        requiredMessage: 'This field is required',
        maxlength: MEDIUM_LENGTH
    },
    payer_email: {
        required: true,
        requiredMessage: 'This field is required',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
        patternMessage: 'Please enter a valid email address',
        maxlength: MEDIUM_LENGTH
    },
    payer_phone: {
        required: true,
        pattern: '^\\(\\d{3}\\) \\d{3}-\\d{4}$',
        patternMessage: 'Please enter a valid phone number in format (*************',
        requiredMessage: 'This field is required'
    },
    invoice_number: {
        maxlength: MEDIUM_LENGTH
    },

    billing_address: {
        required: true,
        requiredMessage: 'This field is required',
        maxlength: MEDIUM_LENGTH
    },
    billing_city: {
        required: true,
        requiredMessage: 'This field is required',
        pattern: '^[A-Za-z\\s]+$',
        patternMessage: 'Please enter a valid city name (letters and spaces only)',
        maxlength: SMALL_LENGTH
    },
    billing_state: {
        required: true,
        requiredMessage: 'This field is required'
    },
    billing_zip: {
        required: true,
        requiredMessage: 'This field is required',
        pattern: '^\\d{5,9}$',
        patternMessage: 'Please enter a valid postal code (5-9 digits)',
        title: 'Please enter a valid postal code (5-9 digits)'
    },

    cardholder_name: {
        required: true,
        requiredMessage: 'This field is required',
        maxlength: MEDIUM_LENGTH,
        title: 'Please enter the cardholder\'s name'
    },
    card_number: {
        required: true,
        requiredMessage: 'This field is required',
        credit: true,
    },
    card_expiry: {
        required: true,
        requiredMessage: 'This field is required',
        expiration: true,
    },
    card_cvv: {
        required: true,
        requiredMessage: 'This field is required',
        cvv: true,
    },

    ach_account_name: {
        required: true,
        requiredMessage: 'This field is required',
        maxlength: MEDIUM_LENGTH
    },
    ach_routing_number: {
        required: true,
        requiredMessage: 'This field is required',
        routing: true,
    },
    ach_account_number: {
        required: true,
        requiredMessage: 'This field is required',
        ach_account: true,
        patternMessage: 'Please enter a valid account number (4-17 digits)'

    },
    ach_account_type: {
        required: true,
        requiredMessage: 'Please select an account type'
    },
    ach_billing_address: {
        required: true,
        requiredMessage: 'This field is required',
        maxlength: MEDIUM_LENGTH
    },
    ach_billing_city: {
        required: true,
        requiredMessage: 'This field is required',
        pattern: '^[A-Za-z\\s]+$',
        patternMessage: 'Please enter a valid city name (letters and spaces only)',
        maxlength: SMALL_LENGTH
    },
    ach_billing_state: {
        required: true,
        requiredMessage: 'This field is required'
    },
    ach_billing_zip: {
        required: true,
        requiredMessage: 'This field is required',
        pattern: '^\\d{5,9}$',
        patternMessage: 'Please enter a valid postal code (5-9 digits)',
        title: 'Please enter a valid postal code (5-9 digits)'
    }
};

export default validation_rules;
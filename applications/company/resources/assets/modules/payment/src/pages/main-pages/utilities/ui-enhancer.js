import { findChild, jsSelector } from '@ca-package/dom';
import Inputmask from "inputmask";
import $ from 'jquery';

/**
 * Build a generic select field
 * @param {string} label - Label for the select field
 * @param {string} name - Name/ID for the select field
 * @param {Object[]} optionGroups - Array of option groups (label and options)
 * @param {boolean} [required=true] - Whether the field is required
 * @returns {string} - Raw HTML for the select field
 */
export function buildSelectField(label, name, optionGroups, required = true) {
    const selectOptions = optionGroups.map(group => {
        const groupOptions = Object.entries(group.options)
            .map(([key, value]) => `<option value="${key}">${value}</option>`)
            .join('');
        return `<optgroup label="${group.label}">${groupOptions}</optgroup>`;
    }).join('');

    return `<div class="form-group f-field">
                <label class="f-f-label" for="${name}">${label}</label>
                <select name="${name}" data-js="${name}" id="${name}" class="form-control f-f-input"${required ? ' required' : ''}>
                    <option value="">Select One</option>
                    ${selectOptions}
                </select>
            </div>`;
}

export function updateCurrencyDisplay(elemDisplay, value, locale = 'en-US', currency = 'USD') {
    const amt = parseFloat(value) || 0;
    elemDisplay.text(
        new Intl.NumberFormat(locale, { style: 'currency', currency }).format(amt)
    );
}

export function applyBranding(rootElem) {
    const colors = window.payments_data?.company_colors || {};
    const logoUrl = window.payments_data?.company_logo?.url;

    const style = document.documentElement.style;
    style.setProperty('--primary-color-background', colors.button_background_color);
    style.setProperty('--primary-color-hover',      colors.hover_background_color);
    style.setProperty('--primary-color-text',       colors.button_text_color);
    style.setProperty('--primary-color-text-hover', colors.hover_text_color);
    if (logoUrl) {
        findChild(rootElem, jsSelector('company-logo')).attr('src', logoUrl);
    }
}

export function renderTabs(rootElem) {
    const settings = window.payments_data?.company_settings || {};
    const $tabs = findChild(rootElem, jsSelector('payment-tabs'));
    const { credit_card_acceptance_enabled: cc, ach_acceptance_enabled : ach } = settings;

    // If neither payment method is enabled, redirect to 404
    if (!cc && !ach) {
        console.error('No payment methods enabled. Redirecting to 404.');
        alert('No payment methods are currently enabled. Please contact support.');
        window.location.href = '/404';
        return;
    }

    if (cc && ach) {
        $tabs.show();
        setActiveTab(rootElem, 'credit_card');
    }
    else if (cc && !ach) {
        $tabs.hide();
        setActiveTab(rootElem, 'credit_card');
    }
    else if (!cc && ach) {
        $tabs.hide();
        setActiveTab(rootElem, 'ach');
    }

    return null;
}

export function setActiveTab(root, type) {
    const is_cc = type === 'credit_card';
    findChild(root, jsSelector('credit-card-tab')).toggleClass('is-active', is_cc);
    findChild(root, jsSelector('credit-card-tab-content')).toggle(is_cc);

    findChild(root, jsSelector('ach-tab')).toggleClass('is-active', !is_cc);
    findChild(root, jsSelector('ach-tab-content')).toggle(!is_cc);
}


/**
 * Apply standard input masks to form fields via validator
 */
export function applyInputMasks(validator, root) {
    const maskConfigs = [
        { selector: 'payer_phone', options: { mask: '(*************' } },
        { selector: 'card_number', regex: '^\\d{0,16}$' },
        { selector: 'card_expiry', options: { mask: '99/99' } },
        { selector: 'card_cvv', regex: '^\\d{0,3}$' },
        { selector: 'billing_zip', regex: '^\\d{0,9}$' },
        { selector: 'ach_billing_zip', regex: '^\\d{0,9}$' },
        { selector: 'ach_account_number', regex: '^\\d{0,17}$' },
        { selector: 'ach_routing_number', regex: `^\\d{0,20}$` },
    ];

    maskConfigs.forEach(({ selector, options, regex }) => {
        const elem = validator.getInputElem(selector);
        if (elem) {
            if (regex) {
                Inputmask({ regex }).mask(elem);
            } else {
                Inputmask(options).mask(elem);
            }
        }
    });
}

/**
 * Initialize radio button visual states and event handlers
 * @param {jQuery} root - Root element to search within
 * @param {string} selector - Data-js selector for radio buttons (e.g., 'ach_account_type')
 */
export function initializeRadioButtons(root, selector) {
    const radio_buttons = findChild(root, jsSelector(selector));
    
    if (!radio_buttons.length) {
        return;
    }

    radio_buttons.on('change', (event) => {
        radio_buttons.removeClass('t-checked');
        $(event.target).addClass('t-checked');
    });

    // Initialize the default checked radio button visual state
    radio_buttons.each((index, radio) => {
        if (radio.checked) {
            $(radio).addClass('t-checked');
        }
    });
}

/**
 * Update payment amount displays and hidden form fields
 * @param {jQuery} form - Form element containing the hidden fields
 * @param {Object} elements - Elements object (typically this.elem from Manager class)
 */
export function updatePaymentDisplays(form, elements) {
    const {
        payment_amount_input,
        payment_amount_display,
        credit_card_processing_fee_display,
        payment_total,
        credit_card_fee_label,
        credit_card_tab_content
    } = elements;

    // Determine if credit card payment method is selected
    const is_credit_card = credit_card_tab_content.is(':visible');
    
    const payment_amount = parseFloat(payment_amount_input.val()) || 0;
    const credit_card_processing_fee_percentage = parseFloat(window.payments_data?.company_settings?.credit_card_processing_fee) || 0;
    
    // Calculate fee only for credit card payments and when fee percentage > 0
    const should_apply_fee = is_credit_card && credit_card_processing_fee_percentage > 0;
    const credit_card_processing_fee_amount = should_apply_fee ? 
        (payment_amount * (credit_card_processing_fee_percentage / 100)) : 0;
    
    const payment_total_amount = payment_amount + credit_card_processing_fee_amount;

    // Update hidden fields for server validation
    let fee_input = form.find('input[name="credit_card_processing_fee"]');
    if (!fee_input.length) {
        fee_input = $('<input type="hidden" name="credit_card_processing_fee">');
        form.append(fee_input);
    }
    fee_input.val(credit_card_processing_fee_amount.toFixed(2));

    let total_input = form.find('input[name="total_amount"]');
    if (!total_input.length) {
        total_input = $('<input type="hidden" name="total_amount">');
        form.append(total_input);
    }
    total_input.val(payment_total_amount.toFixed(2));

    // Update display values
    updateCurrencyDisplay(payment_amount_display, payment_amount);
    updateCurrencyDisplay(credit_card_processing_fee_display, credit_card_processing_fee_amount);
    updateCurrencyDisplay(payment_total, payment_total_amount);
    
    // Update fee label with percentage if applicable
    if (credit_card_fee_label && credit_card_fee_label.length) {
        if (should_apply_fee) {
            // Format percentage as integer if it's a whole number, otherwise show decimal
            const percentage_display = (credit_card_processing_fee_percentage % 1 === 0) 
                ? Math.floor(credit_card_processing_fee_percentage) 
                : credit_card_processing_fee_percentage;
            credit_card_fee_label.text(`Convenience Fee (${percentage_display}%)`);
        } else {
            credit_card_fee_label.text('Convenience Fee');
        }
    }
    
    // Show/hide fee display row based on whether fee should be applied
    const fee_row = credit_card_processing_fee_display.closest('.c-mp-footer__item');
    if (fee_row.length) {
        fee_row.toggle(should_apply_fee);
    }
}

/**
 * Show skeleton loading state
 * @param {jQuery} form_container - Form container element
 * @param {jQuery} skeleton - Skeleton element
 */
export function showSkeletonLoading(form_container, skeleton) {
    form_container.addClass('is-loading').removeClass('is-ready');
    skeleton.removeClass('is-hidden');
}

/**
 * Hide skeleton loading and show ready state
 * @param {jQuery} form_container - Form container element  
 * @param {jQuery} skeleton - Skeleton element
 */
export function hideSkeletonLoading(form_container, skeleton) {
    form_container.addClass('is-ready').removeClass('is-loading');
    skeleton.addClass('is-hidden');
}

/**
 * Show success callout message
 * @param {jQuery} callout_element - Callout element to show success message
 * @param {string} message - Success message to display
 */
export function showSuccessCallout(callout_element, message) {
    hideCallout(callout_element);
    callout_element.removeClass('is-error').addClass('is-success').text(message);
    
    // Auto-hide success message after 10 seconds
    setTimeout(() => {
        hideCallout(callout_element);
    }, 10000);
}

/**
 * Show error callout message
 * @param {jQuery} callout_element - Callout element to show error message
 * @param {string} message - Error message to display
 */
export function showErrorCallout(callout_element, message) {
    hideCallout(callout_element);
    callout_element.removeClass('is-success').addClass('is-error').html(message);
}

/**
 * Hide callout message
 * @param {jQuery} callout_element - Callout element to hide
 */
export function hideCallout(callout_element) {
    callout_element.removeClass('is-success is-error').text('');
}

/**
 * Set form disabled state for loading/processing states
 * @param {jQuery} form_element - Form element to disable/enable
 * @param {boolean} disabled - Whether to disable form elements
 */
export function setFormDisabled(form_element, disabled) {
    if (!form_element || !form_element.length) {
        console.warn('Cannot set form disabled state - form element not available');
        return;
    }
    
    form_element.find('input, button, select, textarea').prop('disabled', disabled);
    form_element.attr('aria-busy', disabled.toString());
    
    // Update visual state
    form_element.toggleClass('is-processing', disabled);
}
import $ from 'jquery';

/**
 * Testing utilities for payment form
 * TODO: Remove this entire file when testing is complete
 */

/**
 * Fill form with test data for development/testing purposes
 * @param {Object} elem - DOM elements object from manager
 * @param {jQuery} form - Form element
 */
export function fillTestData(elem, form) {
    const randomNum = Math.floor(Math.random() * 9000) + 1000; // 4-digit random number
    const firstNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
    const lastNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
    
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const fullName = `${firstName} ${lastName}`;
    
    // Generate random phone number
    const phoneArea = Math.floor(Math.random() * 900) + 100;
    const phoneExchange = Math.floor(Math.random() * 900) + 100;
    const phoneNumber = Math.floor(Math.random() * 9000) + 1000;
    const formattedPhone = `(${phoneArea}) ${phoneExchange}-${phoneNumber}`;
    
    // Helper method to fill a field and trigger appropriate events
    const fillField = (fieldName, value) => {
        const $field = form.find(`[name="${fieldName}"]`);
        if ($field.length > 0) {
            $field.val(value).trigger('input').trigger('change');
        }
    };
    
    // Helper method to select state from dropdown
    const selectStateField = (fieldName, stateCode) => {
        const $field = form.find(`[name="${fieldName}"]`);
        if ($field.length > 0) {
            $field.val(stateCode).trigger('change');
        }
    };
    
    // Helper method to fill credit card specific test data
    const fillCreditCardTestData = (fullName) => {
        fillField('cardholder_name', fullName);
        fillField('card_number', '****************');
        fillField('card_expiry', '12/25');
        fillField('card_cvv', '123');
        fillField('billing_address', '123 Test Street');
        fillField('billing_city', 'New York');
        selectStateField('billing_state', 'NY');
        fillField('billing_zip', '10001');
    };
    
    // Helper method to fill ACH specific test data
    const fillAchTestData = (fullName) => {
        fillField('ach_account_name', fullName);
        
        // Select checking account type (default)
        const checkingRadio = form.find('input[name="ach_account_type"][value="checking"]');
        if (checkingRadio.length) {
            checkingRadio.prop('checked', true).trigger('change');
        }
        
        // Generate random account number (10 digits)
        const accountNumber = Math.floor(Math.random() * **********) + **********;
        fillField('ach_account_number', accountNumber.toString());
        fillField('ach_routing_number', '*********'); // Valid Chase routing number
        fillField('ach_billing_address', '123 Test Street');
        fillField('ach_billing_city', 'New York');
        selectStateField('ach_billing_state', 'NY');
        fillField('ach_billing_zip', '10001');
    };
    
    // Fill common fields
    fillField('payer_first_name', firstName);
    fillField('payer_last_name', lastName);
    fillField('payer_email', `test${randomNum}@example.com`);
    fillField('payer_phone', formattedPhone);
    fillField('payment_amount', '100.00');
    fillField('invoice_number', `INV-${randomNum}`);
    
    // Fill payment method specific fields
    fillCreditCardTestData(fullName);
    fillAchTestData(fullName);

    // Trigger payment display updates
    elem.payment_amount_input.trigger('change');
}
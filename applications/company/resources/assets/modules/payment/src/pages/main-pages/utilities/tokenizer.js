import $ from 'jquery';

export class CardConnectTokenizer {
    constructor(container_id = 'iframe-container', card_number_input_id = 'card_number') {
        this.container_id = container_id;
        this.card_number_input_id = card_number_input_id;
        this.error_container_id = 'card-number-error';
        this.base_url = "https://fts-uat.cardconnect.com/itoke/ajax-tokenizer.html";
        
        // Bind methods to preserve context
        this.handleMessage = this.handleMessage.bind(this);
        this.showError = this.showError.bind(this);
        this.clearError = this.clearError.bind(this);
        this.setupMessageListener();
    }

    /**
     * Initialize the iframe tokenizer
     * @param {string} reference_element_id - ID of element to match dimensions
     */
    initialize(reference_element_id = 'cardholder_name') {
        const reference_element = $(`#${reference_element_id}`);
        if (!reference_element.length) {
            console.error('Reference element not found for tokenizer sizing');
            return;
        }

        const iframe_config = {
            width: reference_element.outerWidth().toString(),
            height: reference_element.outerHeight().toString(),
        };

        const css = this.buildCSS(iframe_config);
        
        // Query parameters
        const params = {
            css: css,
            formatinput: "true",
            cardinputmaxlength: "19",
            cardnumbernumericonly: "true",
            enhancedresponse: "true",
            invalidcreditcardevent: "true",
            invalidinputevent: "true",
            tokenizewheninactive: "true",
        };

        const iframe_url = this.buildIframeUrl(params);

        // Create and inject iframe
        const $iframe = this.createIframe(iframe_config, iframe_url);
        $(`#${this.container_id}`).empty().append($iframe);

        // Listen for iframe load event to emit ready event
        $iframe.on('load', () => {
            document.dispatchEvent(new CustomEvent('iframe-ready'));
        });

    }

    /**
     * Build CSS styles for the iframe input
     * @param {Object} iframe_config - iframe dimensions
     * @returns {string} CSS string
     */
    buildCSS(iframe_config) {
        // Context: CardPointe tokenizer accepts a CSS string to style the input field.
        // This attempts to mtch th style of the existing input fields in the application.
        return `
          body {
            margin: 0;
            padding: 0;
          }
          input {
            border-radius: 0.1875rem;
            border: 0.0625rem #BBCBDD solid;
            box-shadow: none;
            color: #1F252C;
            padding: 0 0.5rem;
            line-height: 1.25rem;
            display: block;
            height: ${iframe_config.height}px;
            width: ${iframe_config.width}px;
            box-sizing: border-box;
          }
          input:focus {
            outline: none;
            color: #1F252C;
            background-color: #ffffff;
            border-color: #006EFF;
            box-shadow: 0 0 0 0.25rem #E8F2FF;
          }
        `;
    }

    /**
     * Build iframe URL with parameters
     * @param {Object} params - URL parameters
     * @returns {string} Complete iframe URL
     */
    buildIframeUrl(params) {
        const query = Object.entries(params)
            .map(([key, val]) => `${key}=${encodeURIComponent(val)}`)
            .join("&");
        return `${this.base_url}?${query}`;
    }

    /**
     * Create iframe element
     * @param {Object} iframe_config - iframe dimensions
     * @param {string} iframe_url - iframe source URL
     * @returns {jQuery} iframe element
     */
    createIframe(iframe_config, iframe_url) {
        return $("<iframe>", {
            id: "tokenframe",
            name: "tokenframe",
            src: iframe_url,
            scrolling: "no",
            width: iframe_config.width,
            height: iframe_config.height,
            frameborder: "0",
            css: {
                border: "none",
                display: "block"
            }
        });
    }

    /**
     * Set up message listener for iframe communication
     */
    setupMessageListener() {
        window.addEventListener('message', this.handleMessage);
    }

    /**
     * Handle iframe messages
     * @param {MessageEvent} e - Message event
     */
    handleMessage(e) {
        // Handle messages from CardConnect iframe
        if (e.origin === 'https://fts-uat.cardconnect.com' && e.data) {
            const data = JSON.parse(e.data);
            
            // Clear existing errors first
            this.clearError();

            if (data.validationError && data.errorMessage) {
                this.showError(data.errorMessage);
            }
            
            // Handle successful tokenization
            if (data.token && !data.validationError) {
                $(`#${this.card_number_input_id}`).val(data.token);
            }
        }
    }

    /**
     * Show validation error
     * @param {string} error_message - Error message to display
     */
    showError(error_message) {
        this.clearError();
        
        const error_container = $(`
            <div class="f-f-errors filled" id="${this.error_container_id}">
                <div class="f-fe-error parsley-required">${error_message}</div>
            </div>
        `);
        
        $(`#${this.container_id}`).after(error_container);
    }

    /**
     * Clear validation error
     */
    clearError() {
        $(`#${this.error_container_id}`).remove();
    }

    /**
     * Validate card number field
     * @param {jQuery} credit_card_tab_content - Credit card tab content element
     * @returns {boolean} Validation result
     */
    validateCardNumber(credit_card_tab_content) {
        const credit_card_tab_active = credit_card_tab_content.is(':visible');
        
        if (credit_card_tab_active) {
            const card_number_value = $(`#${this.card_number_input_id}`).val();

            if (!card_number_value || card_number_value.trim() === '') {
                this.showError('This field is required');
                return false;
            }
        }
        
        // Clear any existing errors if validation passes
        this.clearError();
        return true;
    }

    /**
     * Clean up event listeners
     */
    destroy() {
        window.removeEventListener('message', this.handleMessage);
    }
}
/**
 * @module Website leads form
 */

'use strict';

import Router from '@ca-package/router';

import {MainPage} from './pages/main';

/**
 * Main controller for Hosted Payment module.
 *
 * @memberof module:Hosted Payment module.
 */
export class Controller {
    /**
     * Hosted Payments form constructor
     *
     * @param {jQuery} root - jQuery element that contains the custom report module
     *
     */
    constructor(root) {
        this.elem = {root};
        this.state = {};
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.router = new Router(MainPage, {
            base_path: '/payments',

        });
        this.state.router.boot(this.elem.root);
    };
}

/**
 * @module Drawing/Modals/Drawing/Node
 */

'use strict';

const FormValidator = require('@ca-submodule/validator');

const Modal = require('@ca-submodule/modal').Base;
const FormInput = require('@ca-submodule/form-input');
const NumberSpinner = require('@ca-submodule/form-input/src/number_spinner');
FormInput.use(NumberSpinner);

const modal_tpl = require('@cam-drawing-tpl/modals/drawing/node/elevation_reading.hbs');

/**
 * @memberof module:Drawing/Modals/Drawing/Node
 */
class ElevationReading extends Modal {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Tools.ElevationReading} tool
     */
    constructor(tool) {
        let content = modal_tpl(
        );
        super(content, {
            size: Modal.Size.TINY,
            closable: false,
            classes: ['t-drawing-node-elevation']
        });
        Object.assign(this.state, {
            tool
        });
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.addAction({
            type: Modal.Action.SAVE,
            handler: () => this.elem.form.submit()
        });

        this.elem.form = this.elem.content.fxFind('form');
        this.elem.input = {};
        for (let name of ['elevation']) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        FormInput.init(this.elem.input.elevation, {
            type: NumberSpinner.Type.INCHES,
            step_increment: .1,
            decimals: 1
        });

        this.state.form = FormValidator.init(this.elem.form)
            .on('form:submit', () => {
                this.save();
                return false;
            });
        let fields = {
            elevation: {
                required: true
            }
        };
        this.state.field = {};
        for (let name in fields) {
            fields[name].requiredMessage = 'Required';
            this.state.field[name] = this.elem.input[name].parsley(fields[name]);
        }

        this.on('close', () => {
            this.reset();
        });
    };

    /**
     * Open modal
     */
    open() {
        let node = this.state.tool.node,
            title;
        if (node !== null) {
            title = 'Edit Elevation Reading';
            this.elem.input.elevation.val(node.elevation);
        } else {
            title = 'Add Elevation Reading';
            this.elem.input.elevation.val(0.0);
        }
        this.setTitle(title);
        super.open();
    };

    /**
     * Save changes
     */
    save() {
        let value = this.elem.input.elevation.val();
        value = parseFloat(value).toFixed(1);

        let color = value >= 0 ? '#000000' : '#ff0000';
        let data = {
            elevation: value,
            color: color,
        };
        let node = this.state.tool.node;
        if (node === null) {
            this.state.tool.createNode(data);
        } else {
            this.state.tool.modifyNode((node) => node.update(data));
        }
        this.close();
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.state.form.reset();
        this.elem.form[0].reset();
    };

    /**
     * Close modal and clear out selected tool
     */
    close() {
        super.close();
        this.state.tool.controller.clearTool();
    }
}

module.exports = ElevationReading;

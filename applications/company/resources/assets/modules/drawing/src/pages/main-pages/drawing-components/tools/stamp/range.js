'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Range extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Range.Type.RANGE);
        Object.assign(this.state, {
            label: 'Range',
            icon: 'module--drawing--tools--range',
            node_type: Node.Entity.Type.RANGE
        });
    };
}

module.exports = Range;

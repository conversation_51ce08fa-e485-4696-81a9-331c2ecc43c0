'use strict';

const ps = require('paper');
const Angle = require('../utils/angle');

const Line = require('./line');

const Sides = {
    top: {angle: 270, points: ['top_left', 'top_right'], opposite: 'bottom', dimension: 'height'},
    right: {angle: 0, points: ['top_right', 'bottom_right'], opposite: 'left', dimension: 'width'},
    bottom: {angle: 90, points: ['bottom_right', 'bottom_left'], opposite: 'top', dimension: 'height'},
    left: {angle: 180, points: ['bottom_left', 'top_left'], opposite: 'right', dimension: 'width'}
};

const HandleTypes = {
    CORNER: 1,
    SIDE: 2
};

const Handles = {
    top_left: {
        type: HandleTypes.CORNER,
        relations: [
            {point: 'top_right', side: 'right'},
            {point: 'bottom_left', side: 'bottom'}
        ],
        reset: ['top_center', 'left_center', 'center'],
        opposite: 'bottom_right'
    },
    top_center: {
        type: HandleTypes.SIDE,
        side: 'top',
        reset: ['top_center', 'left_center', 'right_center', 'center'],
        opposite: 'bottom_right'
    },
    top_right: {
        type: HandleTypes.CORNER,
        relations: [
            {point: 'top_left', side: 'left'},
            {point: 'bottom_right', side: 'bottom'}
        ],
        reset: ['top_center', 'right_center', 'center'],
        opposite: 'bottom_left'
    },
    right_center: {
        type: HandleTypes.SIDE,
        side: 'right',
        reset: ['right_center', 'top_center', 'bottom_center', 'center'],
        opposite: 'top_left'
    },
    bottom_right: {
        type: HandleTypes.CORNER,
        relations: [
            {point: 'top_right', side: 'top'},
            {point: 'bottom_left', side: 'left'}
        ],
        reset: ['right_center', 'bottom_center', 'center'],
        opposite: 'top_left'
    },
    bottom_center: {
        type: HandleTypes.SIDE,
        side: 'bottom',
        reset: ['bottom_center', 'left_center', 'right_center', 'center'],
        opposite: 'top_left'
    },
    bottom_left: {
        type: HandleTypes.CORNER,
        relations: [
            {point: 'top_left', side: 'top'},
            {point: 'bottom_right', side: 'right'}
        ],
        reset: ['bottom_center', 'left_center', 'center'],
        opposite: 'top_right'
    },
    left_center: {
        type: HandleTypes.SIDE,
        side: 'left',
        reset: ['left_center', 'top_center', 'bottom_center', 'center'],
        opposite: 'top_right'
    }
};

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Paper
 */
class Rectangle {
    /**
     * Constructor
     *
     * @param {object} config
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} config.point
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} config.size
     * @param {?module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} config.min_size
     * @param {?boolean} config.proportional_scale
     * @param {number} [config.rotation=0]
     * @param {string} [config.corner_handle=top_left] - corner from which to size rectangle
     */
    constructor(config) {
        config = Object.assign({
            rotation: 0,
            corner_handle: 'top_left'
        }, config);
        this.state = {
            points: {
                [config.corner_handle]: config.point
            },
            rotation: null,
            size: null,
            min_size: config.min_size || null,
            proportional_scale: config.proportional_scale || false
        };
        this.setRotation(config.rotation);
        this.setSize(config.size, config.corner_handle, false);
    };

    /**
     * Get available sides
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Side() {
        return Sides;
    };

    /**
     * Get available handles
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Handle() {
        return Handles;
    };

    /**
     * Get available handle types
     *
     * @readonly
     *
     * @returns {object}
     */
    static get HandleType() {
        return HandleTypes;
    };

    /**
     * Get top left point
     *
     * @readonly
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    get point() {
        return this.getPoint('top_left');
    };

    /**
     * Get rotation
     *
     * @returns {number}
     */
    getRotation() {
        return this.state.rotation;
    };

    /**
     * Get rotation
     *
     * @returns {number}
     */
    get rotation() {
        return this.getRotation();
    };

    /**
     * Set rotation
     *
     * If rotation already defined, this will find the difference and rotate the top_left point using the center as a
     * pivot.
     *
     * @param {number} rotation
     */
    setRotation(rotation) {
        let set_size = false;
        if (this.state.rotation !== null) {
            if (rotation === this.state.rotation) {
                return;
            }
            let diff = rotation - this.state.rotation;
            this.state.points.top_left = this.state.points.top_left.rotate(diff, this.getPoint('center'));
            set_size = true;
        }
        let order = ['top_left', 'top_right', 'bottom_right', 'bottom_left'],
            angles = {},
            last = rotation;
        for (let point of order) {
            angles[point] = last;
            last = Angle.add(last, 90);
        }
        this.state.rotation = rotation;
        this.state.angles = angles;
        if (set_size) {
            this.setSize(this.getSize());
        }
    };

    /**
     * Set rotation
     *
     * @param {number} rotation
     */
    set rotation(rotation) {
        this.setRotation(rotation);
    };

    /**
     * Get point by name
     *
     * @param {string} name
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    getPoint(name) {
        let point = this.state.points[name];
        if (point === undefined) {
            switch (name) {
                case 'top_center':
                case 'left_center':
                case 'bottom_center':
                case 'right_center':
                    let points = Sides[Handles[name].side].points,
                        start_point = this.getPoint(points[1]),
                        vector1 = this.getPoint(points[0]).subtract(start_point);
                    point = start_point.add(vector1.normalize(vector1.length / 2));
                    break;
                case 'center':
                    let top_left = this.getPoint('top_left'),
                        vector2 = this.getPoint('bottom_right').subtract(top_left);
                    point = top_left.add(vector2.normalize(vector2.length / 2));
                    break;
                default:
                    throw new Error(`Unable to find point: ${name}`);
            }
            this.state.points[name] = point;
        }
        return point;
    };

    /**
     * Reset points by name
     *
     * @param {string[]} names
     */
    resetPoints(names) {
        for (let name of names) {
            this.state.points[name] = undefined;
        }
    };

    /**
     * Get side by name
     *
     * @param {string} name
     * @returns {{from: module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point, to: module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}}
     */
    getSide(name) {
        let [from, to] = Sides[name].points;
        return {
            from: this.getPoint(from),
            to: this.getPoint(to)
        };
    };

    /**
     * Get size
     *
     * If not set (due to update, etc), will be calculated using corner points.
     *
     * @param {boolean} [force=false]
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size}
     */
    getSize(force = false) {
        if (this.state.size === null || force) {
            let top_left = this.getPoint('top_left');
            this.state.size = new ps.Size(
                this.getPoint('top_right').subtract(top_left).length,
                this.getPoint('bottom_left').subtract(top_left).length
            );
        }
        return this.state.size;
    };

    /**
     * Get size
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size}
     */
    get size() {
        return this.getSize();
    };

    /**
     * Set size
     *
     * Size will be applied from the defined corner handle so any point can be automatically adjusted based on a
     * passed size.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} size
     * @param {string} [corner_handle=top_left] - anchor handle name
     * @param {boolean} [reset=true]
     */
    setSize(size, corner_handle = 'top_left', reset = true) {
        let order = ['top_left', 'top_right', 'bottom_right', 'bottom_left'],
            dimensions = {
                top_left: 'width',
                top_right: 'height',
                bottom_right: 'width',
                bottom_left: 'height'
            },
            index = order.indexOf(corner_handle);
        order = order.slice(index, order.length).concat(order.slice(0, index));
        let point = this.getPoint(order[0]);
        for (let i = 0; i < order.length; i++) {
            let handle = order[i],
                next = i + 1;
            if (order[next] === undefined) {
                break;
            }
            point = point.add(new ps.Point({
                angle: this.state.angles[handle],
                length: size[dimensions[handle]]
            }));
            this.state.points[order[next]] = point;
        }
        this.state.size = size;
        if (reset) {
            this.resetPoints(['top_center', 'right_center', 'left_center', 'bottom_center', 'center']);
        }
    };

    /**
     * Check size meets requirements and force update if necessary
     *
     * @protected
     *
     * @param {string} handle
     */
    checkSize(handle) {
        if (this.state.min_size === null) {
            return;
        }
        let size = this.getSize(),
            update = false;
        for (let dimension of ['width', 'height']) {
            if (size[dimension] >= this.state.min_size[dimension]) {
                continue;
            }
            size[dimension] = this.state.min_size[dimension];
            update = true;
        }
        if (update) {
            this.setSize(size, Handles[handle].opposite);
        }
    };

    /**
     * Set size
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} size
     */
    set size(size) {
        this.setSize(size);
    };

    /**
     * Move handle based on point
     *
     * @param {string} handle
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} new_point
     */
    setHandlePoint(handle, new_point) {
        let config = Handles[handle];
        if (config.type === HandleTypes.CORNER) {
            if (this.state.proportional_scale) {
                let angle = Angle.add(this.state.angles[handle], -45),
                    line = {
                        from: this.getPoint(handle)
                    };
                line.to = line.from.add(new ps.Point({
                    angle,
                    length: 10
                }));
                let closest = Line.getClosestPointOnLine(line, new_point),
                    vector = new_point.subtract(closest);
                new_point = line.from.add(vector);
            }
            for (let {point, side} of config.relations) {
                let line = this.getSide(side);
                this.state.points[point] = new ps.Point(Line.getClosestPointOnLine(line, new_point));
            }
            this.state.points[handle] = new_point;
        } else if (config.type === HandleTypes.SIDE && !this.state.proportional_scale) {
            let line = this.getSide(config.side),
                closest = Line.getClosestPointOnLine(line, new_point),
                vector = new_point.subtract(closest);
            for (let point of Sides[config.side].points) {
                this.state.points[point] = this.getPoint(point).add(vector);
            }
        } else {
            return;
        }
        this.state.size = null; // reset size since points changed
        this.resetPoints(config.reset);
        this.checkSize(handle);
    };

    /**
     * Move handle point by vector
     *
     * @param {string} handle
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Vector} vector
     */
    moveHandlePoint(handle, vector) {
        this.setHandlePoint(handle, this.getPoint(handle).add(vector));
    };

    /**
     * Override size when interacting with defined handle
     *
     * Used for snapping size to specific dimensions, leaves object alone and generates new rectangle with specified
     * sized. This is done since the new size can change the main 'top_left' anchor point.
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} size
     * @param {string} handle
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Rectangle}
     */
    overrideSize(size, handle) {
        handle = Handles[handle].opposite;
        return new this.constructor({
            point: this.getPoint(handle),
            size, rotation:
            this.rotation,
            corner_handle: handle
        });
    };

    /**
     * Clone rectangle
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Rectangle}
     */
    clone() {
        return new this.constructor({
            point: this.point,
            size: this.size,
            rotation: this.rotation
        });
    };
}

module.exports = Rectangle;

'use strict';

const Paper = require('../../paper');
const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Sink extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Sink.Entity.Type.SINK,
            tool_type: Tool.Type.SINK,
            layer: Paper.Layer.BACKGROUND_STAMP,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                rotate: true
            }),
            sizes: {
                template: new paper.ps.Size([50, 50]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({inches: 36}, {inches: 24})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let group = new this.paper.ps.Group({
            position: this.state.point,
            parent: this.getLayer(this.selected ? 'selected' : 'default'),
            visible: this.valid,
            applyMatrix: false
        });
        new this.paper.ps.Path({
            strokeWidth: 1,
            strokeColor: '#000',
            size: this.properties.sizes.template,
            fillColor: '#fff',
            applyMatrix: true,
            closed: true,
            segments: [
                [
                    1.1,
                    10
                ],
                [
                    48.9,
                    10
                ],
                [
                    48.9,
                    39.9
                ],
                [
                    1.1,
                    39.9
                ]
            ],
            parent: group
        });
        new this.paper.ps.Path.Ellipse({
            matrix: [
                1,
                0,
                0,
                1,
                25,
                25
            ],
            size: [25.2, 15.4],
            strokeWidth: 1,
            strokeColor: '#000',
            fillColor: '#fff',
            radius: [12.6, 7.7],
            parent: group
        });

        return new this.paper.ps.Group({
            children: [group]
        });
    };
}

module.exports = Sink;

'use strict';

const Entry = require('../entry');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineOpening/Entry
 */
class DoubleCantilever extends Entry {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: DoubleCantilever.Entity.Type.DOUBLE_CANTILEVER,
            tool_type: Tool.Type.DOUBLE_CANTILEVER,
            width: paper.getPixelsFromUnit({feet: 3}),
            gate_thickness: Math.ceil(paper.getPixelsFromUnit({inches: 3}))
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: this.paper.getPixelsFromUnit({feet: 3})
        });
        return state;
    };

    /**
     * Calculate node offset based on line and paper.js path info
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} line
     * @param {object} path - paper.js path
     * @returns {number}
     */
    calculateNodeOffset(line, path) {
        return Math.floor(path.bounds.height / 2) + line.half_width;
    };

    /**
     * Create paper.js path
     *
     * @returns {*} - paper.js item
     */
    getPath() {
        let gate_color = this.line !== null ? this.line.color : '#000000',
            gate_size = {
                width: this.state.width,
                half_width: this.state.width / 2,
                thickness: this.properties.gate_thickness
            },
            width_placeholder = new this.paper.ps.Path.Rectangle({
                point: [-1, 1],
                size: [this.state.width, gate_size.thickness - 2],
                strokeColor: this.selected ? this.properties.selected_color : '#b4b4b4',
                strokeWidth: 1,
                insert: false,
                dashArray: [8, 2]
            }),
            gate1 = new this.paper.ps.Path.Rectangle({
                point: [-18, 0],
                size: [gate_size.half_width, gate_size.thickness],
                insert: false,
                fillColor: this.selected ? this.properties.selected_color : gate_color,
                strokeWidth: 0
            }),
            gate2 = new this.paper.ps.Path.Rectangle({
                point: [gate_size.half_width, 0],
                size: [gate_size.half_width, gate_size.thickness],
                insert: false,
                fillColor: this.selected ? this.properties.selected_color : gate_color,
                strokeWidth: 0
            });

        return new this.paper.ps.Group({
            position: [0, 0],
            children: [width_placeholder, gate1, gate2],
            applyMatrix: false
        });
    };
}

module.exports = DoubleCantilever;

'use strict';

const LineStamp = require('../line_stamp');
const Tool = require('../../tools/base');
const Size = require('../../utils/size');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineStamp
 */
class WallBrace extends LineStamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: WallBrace.Entity.Type.WALL_BRACE,
            tool_type: Tool.Type.WALL_BRACE,
            sizes: {
                template: Size.of([32, 50])
            }
        });
        let {size, scale} = Size.scaleTo(this.properties.sizes.template, paper.getPixelsFromUnit({inches: 6}));
        this.properties.sizes.actual = size;
        this.properties.scale = scale;
        this.properties.dimension_stem_offset = size.height;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let path = new this.paper.ps.Path({
                segments: [[32, 0], [32, 4], [18, 4], [18, 46], [32, 46], [32, 50], [0, 50], [0, 46], [14, 46], [14, 4], [0, 4], [0, 0]],
                fillColor: '#000',
                closed: true,
                insert: false
            }),
            group = new this.paper.ps.Group({
                children: [path]
            });

        group.scale(this.properties.scale.width, this.properties.scale.height);
        group.applyMatrix = false;

        return group;
    };

    /**
     * Modify paper.js path after copy from source template
     *
     * @param {object} path - paper.js path item
     */
    modifyStamp(path) {
        path.firstChild.fillColor = this.selected ? this.properties.selected_color : '#000000';
    };
}

module.exports = WallBrace;

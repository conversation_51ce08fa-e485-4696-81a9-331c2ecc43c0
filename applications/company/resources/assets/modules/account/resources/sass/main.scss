@use '~@cac-sass/base';
@use '~@cac-sass/app/highlight';
@use '~@cas-layout-sass/layout';
@use '~@cas-table-sass/table';
@use '~@cas-validator-sass/validator';
@use '~@cas-modal-sass/modal';
@use 'sass:color' as sass-color;

@use 'sections/subscription';
@use 'sections/payment_methods';

$background-color: #f7f7f7;

body {
    background-color: #fff;
}

.acct-wrapper {
    width: 100%;
    height: 100%;
}

.acct-module {
    width: 100%;
    height: 100%;
    padding: 0;
    .row.expanded {
        width: 100%;
        height: 100%;
    }
}

.side-menu {
    width:20%;
    background-color: $background-color;
    height: 1000px;
    position: fixed;
    .project-title {
        padding: 0 .95rem 0 .95rem;
    }
    ul {
        margin-left: 0;
        overflow: auto;
        li {
            list-style: none;
            a {
                color: #000000;
                font-size: 1rem;
                padding: base.unit-rem-calc(12px) base.unit-rem-calc(8px) base.unit-rem-calc(12px) 1.25rem;
                display: block;
                width:100%;
                &:hover {
                    background: sass-color.adjust($background-color, $lightness: -5%);
                }
            }
            &.active a {
                background: sass-color.adjust($background-color, $lightness: -5%);
                color: #005ad0;
            }
        }
    }
}

.sections {
    position: relative;
    left: 20%;
    width:80%;
    min-height:95%;
    margin-bottom: 3rem;
    .section-title {
        font-weight:bold;
        margin-bottom: 0;
    }
    .sub-section-title {
        font-weight:bold;
        margin-bottom: 0;
        margin-top: base.unit-rem-calc(8px);
    }
    .section {
        width: 100%;
        .content {
            width: 100%;
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(32px);
            .invoice-wrapper {
                margin: base.unit-rem-calc(16px);
            }
            .mce-btn.mce-active, .mce-btn.mce-active:hover {
                background-color: #dbdbdb;
                border-color: #ccc;
            }
        }
    }
}

div.loading-image {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(#eee, 0.5);
    text-align: center;
    display: none;
    > img {
        position: absolute;
        top: 40%;
    }
}

.modal-loading {
    display: none;
    width: base.unit-rem-calc(32px);
    height: base.unit-rem-calc(32px);
    margin: {
        right: base.unit-rem-calc(6px);
    }
    vertical-align: top;
    background-image: url('~@cac-public/images/loading_small.svg');
    background-size: contain;
    position: absolute;
    left: 26px;
    &.show {
        display: inline-block;
    }
}

.button-loading {
    display: none;
    width: base.unit-rem-calc(32px);
    height: base.unit-rem-calc(32px);
    margin: {
        right: base.unit-rem-calc(6px);
    }
    vertical-align: top;
    background-image: url('~@cac-public/images/loading_small.svg');
    background-size: contain;
    position: absolute;
    left: 18px;
    &.settings {
        left: 42px;
    }
    &.sync {
        left: 75px;
    }
    &.show {
        display: inline-block;
    }
}

.parsley-errors-list {
    &.filled {
        margin: .3rem 0 1rem 0;
        li {
            color: #B94A48;
            font-size: 12.6px;
            text-align: left;
        }
    }
}

<div class="c-i--p-page">
    <div class="m-page-header t-section m-wt-button-section">
        <h4 class="c-ph-title" data-title>mySalesman</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-text-icon t-hidden" data-js="settings">
                <div data-text>Settings</div>
                <svg data-icon><use xlink:href="#remix-icon--system--settings-3-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="m-integrations">
        <div class="m-wt-integrations-section t-hidden" data-js="mysalesman-teaser-content">
            <p>
                We're excited to announce an integration with
                <a target="_blank" href="https://cxlratr.to/mysalesman">mySalesman</a> is coming to Contractor Accelerator!
                It's the top lead qualifying tool in the fence industry, and soon you'll be able to connect it directly
                to your Contractor Accelerator account, making it easier than ever to manage your sales pipeline all in
                one place.
                <br><br>
                Already using mySalesman? You'll be ready to connect as soon as the integration goes live.
                <br><br>
                Not signed up yet? Their brand-new mySalesman 3.0 just launched with a sleek new interface and upgraded
                features. Now's the perfect time to get started — beat the rush, start pre-qualifying leads today, and
                be ready to hit the ground running when the integration goes live.
                <a target="_blank" href="https://cxlratr.to/hc-mysalesman">Click here</a> to learn more.
                <br><br>
                Stay tuned — more integration details coming soon!
            </p>
        </div>
        <div class="m-wt-integrations-section t-hidden" data-js="mysalesman-feature-content">
            <p>
                Configure your account to pull in leads from mySalesman. Go to your mySalesman account, click myBusiness,
                choose the Integrations tab, and copy the OpenAPI primary key. This key will be used to send leads
                to Contractor Accelerator from mySalesman.
            </p>
            <div class="c-cs-wrapper t-flex-row">
                <div class="c-csw-content-row ">
                    <h4 class="c-cswc-title">
                        Integration Enabled
                        <span data-tooltip data-type="info">
                            Automatically pull leads in from mySalesman
                        </span>
                    </h4>
                </div>
                <div class="c-csw-content-row">
                    <div class="c-cswc-content" data-js="integration-enabled"></div>
                </div>
            </div>

            <div class="c-cs-wrapper">
                <div class="c-csw-content ">
                    <h4 class="c-cswc-title">
                        Default Assigned To
                        <span data-tooltip data-type="info">
                            Assign a default user to automatically receive new leads from mySalesman. This user can reassign, convert, or edit the lead as needed
                        </span>
                    </h4>
                    <div class="c-cswc-content" data-js="default-assigned-to-name"></div>
                </div>
                <div class="c-csw-content ">
                    <h4 class="c-cswc-title">
                        Capture Dropoffs
                        <span data-tooltip data-type="info">
                            Allow dropoffs to be created as a lead in Contractor Accelerator
                        </span>
                    </h4>
                    <div class="c-cswc-content" data-js="capture-dropoffs"></div>
                </div>

                <div class="c-csw-content ">
                    <h4 class="c-cswc-title">
                        Send Welcome Email
                        <span data-tooltip data-type="info">
                            Automatically send the welcome email to new leads that are added from mySalesman
                        </span>
                    </h4>
                    <div class="c-cswc-content" data-js="send-notifications"></div>
                </div>
                <div class="c-csw-content ">
                    <h4 class="c-cswc-title">
                        Additional Lead Email Recipients
                        <span data-tooltip data-type="info">
                            Additional Lead Email Addresses to receive notifications when a lead is added
                        </span>
                    </h4>
                    <div class="c-cswc-content" data-js="additional-email-recipients"></div>
                </div>
            </div>

{{!--             <div class="m-wt-integrations-section"> --}}
{{!--                 <div> --}}
{{!--                     <div class="t-bottom-gap t-hidden" data-js="api-key-section"> --}}
{{!--                         <div class="c-sync"> --}}
{{!--                             <div class="c-s-content"> --}}
{{!--                                 <div class="c-sc-group f-field"> --}}
{{!--                                     <div class="c-scg-input f-f-input"><input data-js="api-key" id="api-key" type="text" readonly></div> --}}
{{!--                                     <div class="c-scg-button"> --}}
{{!--                                         <button class="b-text t-primary c-copy-btn" data-js="api-key-copy-btn" data-clipboard-target="#api-key"> --}}
{{!--                                             Copy --}}
{{!--                                             <svg data-icon><use xlink:href="#remix-icon--document--file-copy-line"></use></svg> --}}
{{!--                                         </button> --}}
{{!--                                     </div> --}}
{{!--                                 </div> --}}
{{!--                             </div> --}}
{{!--                         </div> --}}
{{!--                     </div> --}}
{{!--                 </div> --}}
{{!--             </div> --}}
        </div>
    </div>
</div>
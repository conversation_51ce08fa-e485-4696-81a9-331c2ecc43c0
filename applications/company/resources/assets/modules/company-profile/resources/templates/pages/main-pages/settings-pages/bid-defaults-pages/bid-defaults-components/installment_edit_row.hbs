<div class="c-bdtb-row" data-index="{{index}}">
    <div class="f-field t-name">
        <label class="f-f-label">
            Name
        </label>
        <input class="f-f-input" type="text" value="{{name}}" data-parsley-required-message=""
               required data-js="name" />
    </div>
    <div class="f-field t-payment">
        <label class="f-f-label">
            Payment Due
        </label>
        <select class="f-f-input" data-js="due-time-frame" placeholder="-- Select One --" required
                data-parsley-required-message="">
            {{#each due_time_frames}}
                <option value="{{@key}}">{{this}}</option>
            {{/each}}
        </select>
    </div>
    <div class="f-field t-type">
        <label class="f-f-label">
            Type
        </label>
        <select class="f-f-input" data-fx-form-input="button-group" required data-js="amount-type">
            {{#eachInMap amount_types}}
                <option value="{{key}}"{{#ifeq key ../amount_type}} selected{{/ifeq}}>{{value.label}}</option>
            {{/eachInMap}}
        </select>
    </div>
    <div class="f-field t-amount">
        <label class="f-f-label">
            Amount
        </label>
        <input class="f-f-input" type="text" value="{{amount}}" data-parsley-required-message=""
               required data-js="amount" data-fx-form-input="number" />
    </div>
    <div class="f-field t-remove">
        <div class="t-button-remove">
            <button class="f-f-button-remove" data-js="remove" data-index="{{index}}">
                <div data-text>Delete Row</div>
                <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
            </button>
        </div>
    </div>
</div>
<div class="c-i--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>Payment Settings</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="cancel" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-integrations p-payments-settings">
        <div class="c-i-error" data-js="error"></div>
        <p>
            <strong>Associated Fees</strong><br>
            {{brand_name}}, in partnership with Propelr and CardPointe, charges processing fees listed below for this
            service. You may also set your own convenience fee to cover your costs, provided they comply with local,
            state and federal regulations. {{brand_name}} is not responsible for any compliance issues resulting from
            how you choose to apply or pass through these costs. <a target="_blank" href="https://cxlratr.to/hc-payment-processing-fees">Learn more</a>
         </p>
        <ul>
            <li>Credit Card Payments: 2.8% + $.10/transaction</li>
            <li>ACH Bank Transfer: $2.00/transaction</li>
        </ul>
        <form class="c-i-form" data-js="form">
            <div class="c-if-switch-wrapper">
                <div class="c-ifsw-group t-status-group">
                    <div class="f-field t-switch">
                        <input class="f-f-input" type="checkbox" id="is_payments_active"
                               data-fx-form-input="switch" data-js="is-payments-active">
                        <label class="f-f-label t-label" for="is_payments_active">
                            Enable Payments
                            <span data-tooltip data-type="info">
                                Show the “Pay Now” button on invoices and enable the dedicated customer payments page
                            </span>
                        </label>
                        <div data-js="status-tag">Active</div>
                    </div>
                </div>
            </div>
            <div class="c-if-switch-wrapper">
                <div class="c-ifsw-group">
                    <div class="f-field t-switch">
                        <input class="f-f-input" type="checkbox" id="payment_credit_card_acceptance_enabled"
                               data-fx-form-input="switch" data-js="payment-credit-card-acceptance-enabled">
                        <label class="f-f-label t-label" for="payment_credit_card_acceptance_enabled">
                            Allow Credit Card Payments
                            <span data-tooltip data-type="info">
                                Allow customers to pay with credit cards
                            </span>
                        </label>
                    </div>
                </div>

                <div class="c-ifsw-group">
                    <div class="f-field t-switch">
                        <input class="f-f-input" type="checkbox" id="payment_ach_acceptance_enabled"
                               data-fx-form-input="switch" data-js="payment-ach-acceptance-enabled">
                        <label class="f-f-label t-label" for="payment_ach_acceptance_enabled">
                            Allow ACH Bank Transfers
                            <span data-tooltip data-type="info">
                                Allow customers to pay directly from their bank account
                            </span>
                        </label>
                    </div>
                </div>

                <div class="c-ifsw-group">
                    <div class="f-field">
                        <label class="f-f-label" for="credit_card_processing_fee">
                            Convenience Fee (%) <a target="_blank" href="https://cxlratr.to/hc-payment-processing-fees">Learn more</a>
                            <span data-tooltip data-type="info">
                                The fee percentage you charge for credit card transactions - see message above
                            </span>
                        </label>
                        <input class="f-f-input" type="number" id="credit_card_processing_fee"
                               data-js="credit-card-processing-fee" min="0" max="3" step="0.1" placeholder="0.0">
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="c-i--p-page">
    <div class="m-page-header t-section e-payments-header">
      <h4 class="c-ph-title" data-title>Payments</h4>

      <div class="c-ph-actions" data-button-right>
          <a class="c-pha-primary t-hidden" data-js="get-started-button">Get started</a>
          <button class="c-pha-tertiary t-text-icon t-hidden" data-js="settings-button">
            <div data-text>Settings</div>
            <svg data-icon><use xlink:href="#remix-icon--system--settings-3-line"></use></svg>
          </button>
      </div>
    </div>

    <div class="m-integrations m-integrations-payments">
      <div class="m-integrations-signup-section" data-js="signup-section">
          <p>
              Accept credit card and ACH payments directly through your {{brand_name}} account. Start by applying to
              become a merchant, then select your settings for the payment button, hosted payments page, and processing
              tools.
              <br><br>
                  <strong>Associated Fees</strong><br>
                  {{brand_name}}, in partnership with Propelr and CardPointe, charges processing fees listed below for this
                  service. You may also set your own convenience fee to cover your costs, provided they comply with local,
                  state and federal regulations. {{brand_name}} is not responsible for any compliance issues resulting from
                  how you choose to apply or pass through these costs. <a target="_blank" href="https://cxlratr.to/hc-payment-processing-fees">Learn more</a>
               </p>
                  <ul>
                      <li>Credit Card Payments: 2.8% + $.10/transaction</li>
                      <li>ACH Bank Transfer: $2.00/transaction</li>
                  </ul>

      </div>


      <div class="m-integrations-status-section t-hidden" data-js="status-section">
        <p>
            <strong>Associated Fees</strong><br>
            {{brand_name}}, in partnership with Propelr and CardPointe, charges processing fees listed below for this
            service. You may also set your own convenience fee to cover your costs, provided they comply with local,
            state and federal regulations. {{brand_name}} is not responsible for any compliance issues resulting from
            how you choose to apply or pass through these costs. <a target="_blank" href="https://cxlratr.to/hc-payment-processing-fees">Learn more</a>
         </p>
        <ul>
            <li>Credit Card Payments: 2.8% + $.10/transaction</li>
            <li>ACH Bank Transfer: $2.00/transaction</li>
        </ul>

        <div class="c-status-wrapper t-flex-row">
        <div class="c-sw-row">
            <h4 class="c-swr-title">
              Enable Payments
              <span data-tooltip data-type="info">
                  Show the “Pay Now” button on invoices and enable the dedicated customer payments page
              </span>
            </h4>
            <div class="c-swr-content" data-js="is-payments-active"></div>
        </div>
          <div class="c-sw-row">
            <h4 class="c-swr-title">
              Allow Credit Card Payments
              <span data-tooltip data-type="info">
                  Allow customers to pay with credit cards
              </span>
            </h4>
            <div class="c-swr-content" data-js="accept-credit-card-status"></div>
          </div>
          <div class="c-sw-row">
            <h4 class="c-swr-title">
              Allow ACH Bank Transfers
              <span data-tooltip data-type="info">
                  Allow customers to pay directly from their bank account
              </span>
            </h4>
            <div class="c-swr-content" data-js="accept-ach-status"></div>
          </div>
          <div class="c-sw-row">
            <h4 class="c-swr-title">
              Convenience Fee <a target="_blank" href="https://cxlratr.to/hc-payment-processing-fees">Learn more</a>
              <span data-tooltip data-type="info">
                  The fee percentage you charge for credit card transactions - see message above
              </span>
            </h4>
            <div class="c-swr-content" data-js="credit-card-processing-fee">N/A</div>
          </div>
        </div>

        <div class="c-status-wrapper t-flex-row">
          <div class="c-sw-row t-flex">
            <h4 class="c-swr-title">
              Merchant Application Status
              <span data-tooltip data-type="info">
                  Status of your merchant application. Learn more about the statuses in our Help Center under Payments
              </span>
            </h4>
            <div class="c-swr-content" data-js="payments-application-status"></div>
          </div>
          <div class="c-sw-row t-flex">
            <h4 class="c-swr-title">
              Gateway Connection
              <span data-tooltip data-type="info">
                  Status of your connection to the payment gateway
              </span>
            </h4>
            <div class="c-swr-content" data-js="payments-gateway-status"></div>
          </div>
            <div class="c-sw-row t-flex">
                <h4 class="c-swr-title">
                    Ready To Process
                    <span data-tooltip data-type="info" data-js="ready-to-process-tooltip"></span>
                </h4>
                <div class="c-swr-content" data-js="payments-ready-to-process-status"></div>
            </div>
        </div>

        <div class="t-hidden" data-js="signature-section">
          <div class="t-flex-row">
              <p class="no-margin">
               Your merchant application requires a digital signature to proceed. Please click the button below to open the signature page in a new tab.
              <br><br>
              Note: It may take up to 5 minutes after signing for {{brand_name}} to update your application status.
              </p>
          </div>
          <div class="t-bottom-gap">
            <div class="c-sync">
              <div class="c-s-content">
                <div class="c-sc-group f-field">
                    <div class="c-scg-button">
                    <button class="button c-copy-btn" data-js="signature-button">Sign & Complete</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="t-hidden" data-js="processing-section">
          <div class="t-flex-row">
              <p class="no-margin">
                  <span><b>Your application was submitted and may take up to 5 days to process.</b></span>
                  <br><p> If additional information is required, the Propelr underwriting team will email the applicant. If you have questions about your application status, please contact support at
                  <b>(877) 374-3588</b> or <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
              </p>
          </div>
        </div>

        <div class="t-hidden" data-js="payment-link-section">
          <div class="t-flex-row">
              <p class="no-margin">
               The Pay Now button will appear on invoices when payments are enabled and will automatically fill in
               customer and invoice details. You can also copy the hosted payments link below. With this hosted link,
               customers enter their own details to complete a payment.
                <br></br>
               <strong>Hosted Payments Page Link</strong></br>
               Use the link to create a QR code or share the link by email, text, or on your website. Add a hyperlink in
               the payment section of your bids under <a data-navigate="{{bid_customization_route}}">Company Profile > Bid Customization.</a>
              </p>
          </div>
          <div class="t-bottom-gap">
            <div class="c-sync">
              <div class="c-s-content">
                <div class="c-sc-group f-field">
                  <div class="c-scg-input f-f-input">
                    <input data-js="payment-link" id="payment-link-url" type="text" readonly>
                  </div>
                  <div class="c-scg-button">
                    <button class="b-text t-primary" data-js="copy-payment-link-button" data-clipboard-target="#payment-link-url">Copy</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</div>

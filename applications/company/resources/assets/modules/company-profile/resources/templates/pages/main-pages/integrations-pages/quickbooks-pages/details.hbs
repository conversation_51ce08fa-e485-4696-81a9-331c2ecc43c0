<div class="c-i--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>QuickBooks Online</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-text-icon t-hidden" data-js="sync-customers">
                <div data-text>Sync Customers</div>
                <svg data-icon><use xlink:href="#remix-icon--system--refresh-line"></use></svg>
            </button>
            <button class="c-pha-primary t-icon t-hidden" data-js="settings">
                <svg data-icon><use xlink:href="#remix-icon--system--settings-3-line"></use></svg>
            </button>
            <a class="c-pha-primary t-hidden" data-js="connect" href="{{connect_link}}">Connect</a>
        </div>
    </div>
    <div class="m-integrations">
        <div class="c-i-wrapper t-instructions" data-js="disconnected-text">
            Click <strong>Connect</strong> and you will be redirected to QuickBooks to authorize access to your account.
        </div>
        <div class="c-i-wrapper t-quickbooks-settings" data-js="connected-settings">
            <div class="c-iw-content">
                <h4 class="c-iwc-title">
                    Default Service For QB Invoice
                    <span data-tooltip data-type="info">
                        The Product/Service type to be used for the QuickBooks Invoice.
                    </span>
                </h4>
                <div class="c-iwc-content" data-js="default-service-for-quickbooks-invoice"></div>
            </div>

            <div class="c-iw-content">
                <h4 class="c-iwc-title">
                    Use QuickBooks Invoice
                    <span data-tooltip data-type="info">
                        Turn this setting off to use the software's invoices rather than QuickBooks.<br>
                        Note: Invoices will still be created in QuickBooks.
                    </span>
                </h4>
                <div class="c-iwc-content" data-js="use-quickbooks-invoice"></div>
            </div>

            <div class="c-iw-content">
                <h4 class="c-iwc-title">
                    Show QuickBooks ACH Link
                    <span data-tooltip data-type="info">
                        Shows the ACH payment option you’ve set up in QuickBooks
                    </span>
                </h4>
                <div class="c-iwc-content" data-js="allow-online-ach-payment"></div>
            </div>

            <div class="c-iw-content">
                <h4 class="c-iwc-title">
                    Show QuickBooks Credit Card Link
                    <span data-tooltip data-type="info">
                         Shows the credit card payment option you’ve set up in QuickBooks
                    </span>
                </h4>
                <div class="c-iwc-content" data-js="allow-online-cc-payment"></div>
            </div>
        </div>
        <div class="c-i-wrapper t-instructions" data-js="connected-text">
            Please verify the "Custom Transaction Numbers" setting is disabled in your QuickBooks Online account.
            <span data-tooltip data-type="info">
                This can be found in Settings > Company Settings > Sales > Custom Transaction Numbers > Off
            </span><br>
            Note: If this setting is not disabled (Off), the invoices sent to your QuickBooks account will not include invoice numbers.
        </div>
        <div class="c-i-wrapper t-status" data-js="status">
            <div class="c-iw-content t-flex-row">
                <div class="c-iwc-content t-bold">
                    QuickBooks Status
                    <div class="c-iwcc-status">
                        <svg data-icon><use xlink:href="#remix-icon--system--checkbox-circle-fill"></use></svg>
                    </div>
                </div>
                <a class="c-iwc-disconnect" href="{{disconnect_link}}" data-js="disconnect-link">
                    <div data-text>Disconnect</div>
                    <svg data-icon><use xlink:href="#remix-icon--editor--link-unlink-m"></use></svg>
                </a>
            </div>
        </div>
    </div>
</div>
<div class="c-m--p-page">
    <div class="m-page-header">
        <h3 class="c-ph-title" data-title>{{title}}</h3>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-text-icon" data-js="preview">
                <div data-text>Preview</div>
                <svg data-icon><use xlink:href="#remix-icon--system--eye-line"></use></svg>
            </button>
            <button class="c-pha-tertiary-negate-grey" data-js="button" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <form class="m-media-form" data-js="form">
        <div class="c-mf-error" data-js="error"></div>
        <div class="c-mf-row t-name">
            <div class="f-field">
                <label class="f-f-label">Name</label>
                <input class="f-f-input" type="text" name="name" data-js="name" />
            </div>
            <div class="c-mfr-file-type" data-js="file-type"></div>
        </div>
        <div class="c-mf-row">
            <div class="f-field">
                <label class="f-f-label">
                    File
                    <span data-tooltip data-type="info">
                        Allowed file types: audio/mpeg3, audio/m4a, audio/ogg, audio/wav, image/jpeg, image/png,
                        image/gif, video/mp4, video/m4v, video/quicktime, video/x-ms-wmv, video/x-msvideo, video/mpeg,
                        video/ogg, video/webm, application/pdf, application/word, application/excel,
                        application/powerpoint, text/csv, text/plain
                    </span>
                </label>
                <div class="c-mf-button-wrapper">
                    <a class="c-mf-button" data-js="upload">
                        <svg data-icon><use xlink:href="#remix-icon--system--upload-cloud-2-line"></use></svg>
                        <div data-text>Upload</div>
                    </a>
                </div>
            </div>
        </div>
        <div class="c-mf-row">
            <div class="f-field c-m-image-flex-block">
                <div class="c-m-file">
                    <input type="file" name="file" data-js="file">
                </div>
                <div class="c-m-thumbnail t-hidden" data-js="thumbnail-block">
                    <div class="c-mf-image-wrapper">
                        <a class="c-mf-delete" data-js="delete-image">
                            <svg data-icon><use xlink:href="#remix-icon--system--close-line"></use></svg>
                        </a>
                        <img class="c-mf-img" src="" data-js="thumbnail-image" />
                    </div>
                </div>
                <span class="c-mf-file-filename" data-js="filename"></span>
            </div>
        </div>
        <div class="c-mf-row">
            <div class="c-pf-description">
                <textarea class="f-f-input" data-js="description" data-fx-form-input="hidden-textarea"></textarea>
            </div>
        </div>
        <div class="c-mf-settings">
            <h4>Settings <span class="f-fl-optional">(Optional)</span></h4>
            <div class="c-mfs-toggles">
                <div class="f-field">
                    <input class="f-f-input" type="checkbox" id="is-bid-media" data-fx-form-input="switch" data-js="is_bid_media">
                    <label for="is-bid-media" class="f-f-label">
                        Include in Bid Media
                        <span data-tooltip data-type="info">
                                Allow media to show in bid creator media selection
                            </span>
                    </label>
                </div>
                <div class="f-field">
                    <input class="f-f-input" type="checkbox" id="is-bid-default" data-fx-form-input="switch" data-js="is_bid_default">
                    <label for="is-bid-default" class="f-f-label">
                        Bid Default
                        <span data-tooltip data-type="info">
                                Automatically add media when creating a bid
                            </span>
                    </label>
                </div>
            </div>
        </div>
    </form>
</div>
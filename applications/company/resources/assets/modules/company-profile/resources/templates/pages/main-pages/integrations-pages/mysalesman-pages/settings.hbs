<div class="c-i--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>mySalesman Settings</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="cancel" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-integrations">
        <p>
            Configure your account to pull in leads from mySalesman. Go to your mySalesman account, click myBusiness,
            choose the Integrations tab, and copy the OpenAPI primary key. This key will be used to send leads
            to Contractor Accelerator from mySalesman.
        </p>
        <div class="c-i-error" data-js="error"></div>
        <form class="c-i-form" data-js="form">
            <div class="c-if-switch-wrapper">
                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="status" data-fx-form-input="switch" data-js="status">
                        <label class="f-f-label t-label" for="status">
                            Integration Enabled
                            <span data-tooltip data-type="info">
                                Automatically pull leads in from mySalesman
                            </span>
                        </label>
                    </div>
                </div>
                <div class="c-csw-content-row">
                    <div class="c-cswc-content" data-js="status-cell">
                        <span class="h-text t-grey">Disabled</span>
                    </div>
                </div>
            </div>
            <div class="c-i-input-group t-flex t-extra-margin">
                <div class="c-iig-item">
                    <div class="f-field">
                        <label class="f-f-label">
                            mySalesman API Key
                            <span data-tooltip data-type="info">
                                Go to your mySalesman account, click myBusiness, choose the Integrations tab, and copy
                                the OpenAPI primary key
                            </span>
                        </label>
                        <input type="text" class="f-f-input" data-js="my_salesman_api_key"/>
                    </div>
                </div>
                <div class="c-iig-item">
                    <div class="f-field">
                        <label class="f-f-label">
                            Default Assigned To
                            <span data-tooltip data-type="info">
                                Assign a default user to automatically receive new leads from mySalesman. This user can reassign, convert, or edit the lead as needed
                            </span>
                        </label>
                        <select class="f-f-input" data-fx-form-input="static-dropdown" data-js="default_assigned_to"></select>
                    </div>
                </div>
            </div>
            <div class="c-i-input-group t-negative-margin">
                <div class="c-iig-item">
                    <div class="f-field">
                        <label class="f-f-label">
                            Additional Lead Email Recipients
                            <span class="f-fl-optional">(Optional)</span>
                            <span data-tooltip data-type="info">
                                Add additional Lead Email Addresses to receive notifications when a lead is submitted. Separate multiple email addresses with a comma.
                            </span>
                        </label>
                        <textarea class="f-f-input" name="additional_email_recipients" class="f-f-input" data-js="additional_email_recipients" id="additional_email_recipients" rows="3" placeholder="Enter Lead Email Addresses here..."></textarea>
                    </div>
                </div>
            </div>
            <div class="c-if-switch-wrapper">
                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="capture_dropoffs" data-fx-form-input="switch" data-js="capture_dropoffs">
                        <label class="f-f-label t-label" for="capture_dropoffs">
                            Capture Dropoffs
                            <span data-tooltip data-type="info">
                                Allow dropoffs to be created as a lead in Contractor Accelerator
                            </span>
                        </label>
                    </div>
                </div>
                <div class="c-ifsw-group">
                    <div class="f-field switch">
                        <input class="f-f-input" type="checkbox" id="send_welcome_email" data-fx-form-input="switch" data-js="send_welcome_email">
                        <label class="f-f-label t-label" for="send_welcome_email">
                            Send Welcome Email
                            <span data-tooltip data-type="info">
                                Automatically send the welcome email to new leads that are added from mySalesman
                            </span>
                        </label>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>



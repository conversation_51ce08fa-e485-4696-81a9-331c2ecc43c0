@use '~@cac-sass/base';

.m-products {
    .c-p--pages {
        height: 100%;
        position: relative;
    }
        .c-p--p-page {
            position: relative;
            height: 100%;
            &.t-hidden {
                display: none;
            }
        }
}

.h-text-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: base.unit-rem-calc(24px);
    height: base.unit-rem-calc(24px);
    border-radius: 50%;
    font-size: base.unit-rem-calc(12px);
    padding: 0;
}

.c-import-content {
    position: relative;
    @include base.full-width-height;
    padding: 0;

    .c-i-loader {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.20) url('~@cac-public/images/loading_blue.svg') no-repeat center;
        background-size: base.unit-rem-calc(100px) base.unit-rem-calc(100px);
        z-index: 244;
        display: none;
    }

}



.c-import-step {
    height: calc(100% - 57px);
    padding: base.unit-rem-calc(12px) base.unit-rem-calc(12px) 0;
    overflow: auto;

    .c-import-step-content {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        height: 100%;

        .final-errors-count {
            color: inherit;
            font-style: normal;
            font-size: base.unit-rem-calc(14px);
        }

        .t-error{
            color: base.$color-red-default;
            font-style: normal;
            margin: base.unit-rem-calc(-8px) 0 base.unit-rem-calc(12px);
        }

        .c-isc-instructions {
            flex: 0 0 auto;

            .c-isci-title {
                display: block;
                font-size: base.unit-rem-calc(14px);
                padding: base.unit-rem-calc(12px) base.unit-rem-calc(12px) 0;
                @include base.respond-to('<small') {
                    padding: base.unit-rem-calc(4px) base.unit-rem-calc(4px) 0;
                }
            }

            .c-isci-list {
                padding-left: base.unit-rem-calc(12px);
            }
        }

        h4 {
            font-size: base.unit-rem-calc(16px);
            line-height: base.unit-rem-calc(22px);
            margin-bottom: base.unit-rem-calc(4px);
        }

        .c-import-header-row {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            margin-bottom: base.unit-rem-calc(12px);

            h4 {
                margin: 0 base.unit-rem-calc(8px) base.unit-rem-calc(4px) 0;
                padding: 0;
                font-size: base.unit-rem-calc(16px);
            }

            .h-text {
                white-space: nowrap;
            }
        }

        .m-product-uploader {
            display: flex;
            justify-content: center;
            flex: 1 1 auto;
            min-height: base.unit-rem-calc(150px);
            padding: 0 base.unit-rem-calc(12px) base.unit-rem-calc(24px);
            @include base.respond-to('<small') {
                padding: 0 base.unit-rem-calc(4px) base.unit-rem-calc(24px);
            }

            .uppy-Root {
                height: 100%;
                width: 100%;
            }

            .uppy-DashboardContent-bar,
            .uppy-Dashboard-progressindicators,
            .uppy-Dashboard-files {
                display: none !important;
            }
            .uppy-Dashboard {
                margin: 0 auto;
                height: 100%;
                width: 100%;
            }

            .uppy-Dashboard .uppy-Dashboard-inner::before {
                display: none;
            }
            .uppy-Dashboard .uppy-Dashboard-inner .uppy-Dashboard-innerWrap {
                border: none;
                width: 100%;
                .uppy-Dashboard-dropFilesHereHint {
                    border: none;
                }
            }
            .uppy-Dashboard-inner {
                display: flex;
                flex: 1 0 auto;
                justify-content: center;
                align-items: center;
                width: 100% !important;
                height: 100% !important;
                max-width: unset;
                max-height: unset;
                border: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;
                background: base.$color-background-edit;
                @include base.respond-to('<small') {
                    border-radius: base.unit-rem-calc(12px);
                }
            }

            .uppy-Dashboard.uppy-Dashboard--isDraggingOver .uppy-Dashboard-inner {
                background: rgba(255, 255, 255, 0.7);
                border: base.unit-rem-calc(1px) dashed base.$color-primary-light-2;
            }
            @keyframes iconBounce {
                0%, 100% {
                    background-position: center calc(50% + 0px);
                }
                50% {
                    background-position: center calc(50% - 8px);
                }
            }

            .uppy-Dashboard.uppy-Dashboard--isDraggingOver .uppy-Dashboard-dropFilesHereHint {
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23005AD0'%3E%3Cpath d='M12 2C17.52 2 22 6.48 22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2ZM12 20C16.42 20 20 16.42 20 12C20 7.58 16.42 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20ZM13 12H16L12 16L8 12H11V8H13V12Z'%3E%3C/path%3E%3C/svg%3E");
                background-color: base.$color-primary-light-4;
                color: base.$color-primary-default;
                outline: base.unit-rem-calc(8px) solid base.$color-primary-light-4;
                background-size: base.unit-rem-calc(64px) base.unit-rem-calc(64px);
                background-repeat: no-repeat;
                background-position: center;
                font-weight: bold;
                animation: iconBounce 1s ease-in-out infinite;
            }

            .uppy-Dashboard.uppy-Dashboard--isDraggingOver .uppy-Dashboard-AddFiles {
                opacity: 0;
            }
        }
    }


    .c-pi-components {
        h4 {
            margin-bottom: 0;
            font-size: base.unit-rem-calc(16px);
        }

        .c-pfc-input-container {
            display: grid;
            grid-template-columns: repeat(4, minmax(0, 1fr));
            width: 100%;
            row-gap: base.unit-rem-calc(8px);
            column-gap: base.unit-rem-calc(16px);

            @include base.respond-to('<medium') {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }

            @include base.respond-to('<small') {
                grid-template-columns: 1fr;
            }

            .f-f-label {
                font-size: base.unit-rem-calc(14px);
            }

            .f-f-input {
                width: 100%;
            }
        }
    }

    .c-pi-mapping {
        background-color: base.$color-white-default;
        border:none;
        margin-bottom: base.unit-rem-calc(32px);
    }
}

.m-products-import {

    .m-page-header{
        .t-cancel{
            color: base.$color-grey-dark-1;
            fill: currentColor;
        }
    }

    .c-cl-wrapper {
        margin: base.unit-rem-calc(12px);
     }

    .c-pi-table{
        @include base.full-width-height;

        @include base.respond-to('<small') {
            .c-t-footer {
                margin: 0 base.unit-rem-calc(-12px);
            }
        }

        .c-t-table-wrapper {
            height: calc(100% - 56px);

            @include base.respond-to('<small') {
                margin: 0 base.unit-rem-calc(-12px);
            }
        }

        .m-table.t-has-row-edit .c-ttw-table tbody td:first-child {
            overflow: unset;
        }
    }

    .m-table{
        container-type: inline-size;
    }

    .m-table-edit{

        .c-f-row {

            margin-bottom: base.unit-rem-calc(16px);

            &.t-name-price-unit {
                display: flex;
                gap: base.unit-rem-calc(16px);

                > .c-fr-field {
                    flex: 1;
                    min-width: 0; // Prevents flex items from overflowing
                }

                @include base.respond-to('<medium') {
                    flex-direction: column;
                    gap: base.unit-rem-calc(16px);

                    > .c-fr-field {
                        width: 100%;
                    }
                }
            }
        }


    }
}

.t-pi-table-columns {

    .id-column {
        max-width: base.unit-rem-calc(20px) !important;
        min-width: unset !important;
    }

    .unit-column {
        max-width: base.unit-rem-calc(55px) !important;
        min-width: unset !important;
    }

    .errors-column {
        max-width: base.unit-rem-calc(10px) !important;
        min-width: unset !important;
    }
}

.t-pi-preview-table {
    height: calc(100% - 180px) !important;

    //accounts for the js function this.state.mobile and sets height to auto.
    @media (max-width: 599px), (max-height: 749px) {
        height: auto;
        padding-bottom: base.unit-rem-calc(32px);
    }

        .c-t-table-wrapper {
        height: calc(100% - 56px);

        @include base.respond-to('<small') {
            border-radius: base.unit-rem-calc(8px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        }
    }
}

.m-table.t-has-row-edit .dataTables_scrollHead {
    .errors-column.sorting_asc:after,
    .errors-column.sorting_desc:after,
    .errors-column.sorting:after {
        margin-left: base.unit-rem-calc(-1px);
    }
}

.m-product-form {
    position: relative;
    overflow: auto;
    height: calc(100% - 57px);
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(40px);
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(32px);
    justify-content: flex-start;
    @include base.respond-to('<small') {
        border-bottom: none;
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    .c-pf-error {
        width: 100%;
        display: none;
        @include base.callout-error;
        &.t-show {
            display: inline-flex;
        }
    }
    .c-pf-instructions {
        width: 100%;
        display: none;
        @include base.callout-info;
        &.t-show {
            display: inline-flex;
        }
    }
    .c-pf-name {
        width: calc(50% - 8px);
        @include base.respond-to('<small') {
            width: 100%;
        }
    }
    .c-pf-type {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(8px);
        .f-field {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            height: base.unit-rem-calc(20px);
            &:first-child {
                height: base.unit-rem-calc(32px);
            }
        }
    }
    .c-pf-price-unit {
        display: flex;
        gap: base.unit-rem-calc(16px);
        @include base.respond-to('<small') {
            flex-wrap: wrap;
        }
        > div {
            width: 50%;
            @include base.respond-to('<small') {
                width: 100%;
            }
        }
    }
    .c-pf-volume-discount {
        width: 100%;
        &.t-show {
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            background-color: base.$color-background-edit;
            border-radius: base.unit-rem-calc(8px);
            margin-top: base.unit-rem-calc(8px);
            .f-field {
                &.t-optional {
                    display: flex;
                    flex-wrap: wrap;
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(15px);
                    @include base.respond-to('<medium') {
                        padding-left: base.unit-rem-calc(8px);
                    }
                    .t-optional {
                        display: none;
                    }
                    > h5 {
                        flex: 1;
                        display: flex;
                        align-items: center;
                        gap: base.unit-rem-calc(4px);
                    }
                }
            }
            .f-f-button-remove {
                display: inline-flex;
            }
            .f-f-button-add {
                display: none;
            }
            .m-volume-discount {
                display: block;
            }
        }
        .f-field {
            &.t-optional {
                > h5 {
                    display: none;
                    @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
                    margin-bottom: 0;
                }
            }
        }
    }
    .m-volume-discount {
        width: 100%;
        display: none;
        padding-top: base.unit-rem-calc(8px);
        @include base.respond-to('<medium') {
            padding: 0;
        }

        .c-vd-error {
            display: none;
            width: calc(100% - 1rem);
            margin: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            @include base.callout-error;
            &.t-show {
                display: inline-flex;
            }
        }
        .c-vd-header {
            display: flex;
            align-items: center;
            border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            padding: 0 base.unit-rem-calc(8px);
            @include base.respond-to('<medium') {
                display: none;
            }
            .f-field {
                width: 25%;
                padding-left: base.unit-rem-calc(8px);
                &.t-min-count {}
                &.t-max-count {}
                &.t-adjustment {
                    margin: 0 base.unit-rem-calc(8px);
                }
                &.t-price {}
            }
        }
        .c-vd-body {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                padding: 0;
            }
        }
            .c-vdb-row {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                @include base.respond-to('<medium') {
                    display: grid;
                    column-gap: base.unit-rem-calc(16px);
                    grid-template-columns: repeat(2, 1fr);
                    padding: 0 base.unit-rem-calc(8px);
                    align-items: end;
                }
                .f-field {
                    width: 25%;
                    @include base.respond-to('<medium') {
                        width: 100%;
                    }
                    &.t-min-count {
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;
                        .f-f-input-number {
                            flex: 1;
                            margin-right: base.unit-rem-calc(8px);
                        }
                        @include base.respond-to('<medium') {
                            display: grid;
                            grid-template-rows: repeat(2, 1fr);
                            grid-template-columns: 1fr 1fr 0;
                            .f-f-label {
                                grid-column: span 3 / auto;
                            }
                            .f-f-input-number {
                                grid-column: span 2 / auto;
                                margin-right: 0;
                            }
                            span {
                                margin-left: base.unit-rem-calc(8px);
                            }
                        }
                    }
                    &.t-max-count {
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        .f-f-input-number {
                            flex: 1;
                        }
                        @include base.respond-to('<medium') {
                            display: flex;
                            flex-direction: column;
                            align-items: start;
                            width: 100%;
                            gap: 0;
                            .f-f-input-number {
                                width: 100%;
                            }
                        }
                    }
                    &.t-adjustment {
                        display: flex;
                        align-items: center;
                        gap: base.unit-rem-calc(8px);
                        margin: 0 base.unit-rem-calc(8px);
                        .f-f-button-group {
                            flex: 0 0 auto;
                        }
                            .f-fbg-button {
                                width: base.unit-rem-calc(26px);
                            }
                        .f-f-input-number {
                            flex: 1;
                        }
                        @include base.respond-to('<medium') {
                            display: grid;
                            grid-template-rows: repeat(2, 1fr);
                            grid-template-columns: 1fr base.unit-rem-calc(58px);
                            gap: 0;
                            grid-column: span 2 / auto;
                            margin: 0;
                            &.t-components {
                                .f-f-input-number {
                                    grid-column: span 2/ auto;
                                    margin-right: 0;
                                }
                            }
                            .f-f-label {
                                grid-column: span 2 / auto;
                            }
                            .f-f-button-group {
                                order: 2;
                            }
                            .f-f-input-number {
                                margin-right: base.unit-rem-calc(16px);
                            }
                        }
                    }
                    &.t-price {
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        @include base.respond-to('<medium') {
                            display: flex;
                            flex-direction: column;
                            align-items: start;
                            grid-column: span 2 / auto;
                            gap: 0;
                            .t-currency {
                                width: 100%;
                            }
                        }
                        .f-f-input-number {
                            flex: 1;
                        }
                        .t-button-remove {
                            display: flex;
                            justify-content: center;
                            @include base.respond-to('<medium') {
                                width: calc(100% + 16px);
                                border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                                padding: base.unit-rem-calc(8px) 0;
                                margin-left: base.unit-rem-calc(-8px);
                            }
                            .f-f-button-remove {
                                > [data-icon] {
                                    margin-right: base.unit-rem-calc(7px) !important;
                                    @include base.respond-to('<medium') {
                                        display: none;
                                    }
                                }
                                > [data-text] {
                                    display: none;
                                    @include base.respond-to('<medium') {
                                        display: inline;
                                    }
                                }
                            }
                        }
                    }
                    .f-f-label {
                        display: none;
                        @include base.respond-to('<medium') {
                            display: inline;
                        }
                    }
                }
            }
        .c-vd-footer {
            display: flex;
            justify-content: flex-end;
            padding: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
            }
        }
            .c-vdf-add {
                @include base.button-text-icon-tertiary;
                padding-right: base.unit-rem-calc(7px) !important;
            }
    }

    .c-pf-components {
        &.t-show {
            display: block;
        }
        display: none;
        .c-pfc-header {
            display: flex;
            align-items: center;
            height: base.unit-rem-calc(32px);
            > h4 {
                margin-bottom: 0;
                @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
            }
        }
        .c-pfc-materials {
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            background-color: base.$color-background-edit;
            border-radius: base.unit-rem-calc(8px);
            margin-top: base.unit-rem-calc(4px);
            .f-field {
                &.t-materials-title {
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px) 0;
                    @include base.respond-to('<medium') {
                        padding-left: base.unit-rem-calc(8px);
                    }
                    .f-f-label {
                        @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(18px));
                    }
                }
            }
        }
            .c-pfcm-paragraph {
                @include base.typo-paragraph;
                margin-bottom: 0;
                a {
                    color: base.$color-primary-default;
                    @include base.respond-to('hover') {
                        &:hover {
                            color: base.$color-primary-light-1;
                        }
                        &:active {
                            color: base.$color-primary-dark-1;
                        }
                    }
                }
            }
    }

    .m-component-materials {
        width: 100%;
        padding-top: base.unit-rem-calc(16px);
        @include base.respond-to('<medium') {
            padding: 0;
        }

        .c-cm-error {
            display: none;
            width: calc(100% - 1rem);
            margin: 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px);
            @include base.callout-error;
            &.t-show {
                display: inline-flex;
            }
        }
        .c-cm-header {
            display: flex;
            align-items: center;
            border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            gap: base.unit-rem-calc(16px);
            padding-left: base.unit-rem-calc(16px);
            padding-right: base.unit-rem-calc(8px);
            @include base.respond-to('<medium') {
                display: none;
            }
            .f-field {
                &.t-material {
                    flex: 1;
                }
                &.t-cost {
                    width: base.unit-rem-calc(144px);
                }
                &.t-quantity {
                    width:  base.unit-rem-calc(96px);
                }
                &.t-markup {
                    width:  base.unit-rem-calc(96px);
                }
                &.t-total {
                    flex: 1;
                }
            }
        }
        .c-cm-body {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                padding: 0;
                border-bottom: none;
            }
        }
            .c-cmb-row {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(16px);
                @include base.respond-to('<medium') {
                    display: grid;
                    grid-template-columns: 1fr base.unit-rem-calc(96px) base.unit-rem-calc(96px);
                    row-gap: base.unit-rem-calc(8px);
                    column-gap: base.unit-rem-calc(16px);
                    padding: 0 base.unit-rem-calc(8px);
                }
                @include base.respond-to('<448px') {
                    grid-template-columns: 1fr 1fr;
                }
                .f-field {
                    &.t-material {
                        display: flex;
                        flex: 1;
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        .f-f-input-number {
                            flex: 1;
                        }
                        @include base.respond-to('<medium') {
                            flex-direction: column;
                            grid-column: span 3 / auto;
                            align-items: start;
                            gap: 0;
                        }
                        @include base.respond-to('<448px') {
                            grid-column: span 2 / auto;
                        }
                    }
                    &.t-cost {
                        display: flex;
                        width: base.unit-rem-calc(144px);
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        position: relative;
                        &::before {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            content: '$';
                            color: base.$color-grey-dark-1;
                            width: base.unit-rem-calc(16px);
                            height: base.unit-rem-calc(32px);
                            line-height: base.unit-rem-calc(32px);
                            text-align: center;
                            font-size: base.unit-rem-calc(14px);
                            @include base.respond-to('<medium') {
                                left: base.unit-rem-calc(4px);
                            }
                        }
                        &::after {
                            position: absolute;
                            bottom: 0;
                            right: 0;
                            content: attr(data-content);
                            font-style: italic;
                            color: base.$color-grey-dark-1;
                            height: base.unit-rem-calc(32px);
                            line-height: base.unit-rem-calc(32px);
                            text-align: center;
                            font-size: base.unit-rem-calc(12px);
                            text-transform: lowercase;
                        }
                        > .f-f-input {
                            padding-right: base.unit-rem-calc(40px);
                            padding-left: base.unit-rem-calc(20px);
                            text-align: right;
                        }
                        @include base.respond-to('<medium') {
                            flex-direction: column;
                            align-items: start;
                            gap: 0;
                            width: 100%;
                        }
                        @include base.respond-to('<448px') {
                            grid-column: span 2 / auto;
                        }
                    }
                    &.t-quantity {
                        width:  base.unit-rem-calc(96px);
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        .f-f-input-number {
                            flex: 1;
                        }
                        @include base.respond-to('<medium') {
                            flex-direction: column;
                            align-items: start;
                            gap: 0;
                        }
                        @include base.respond-to('<448px') {
                            width: 100%;
                        }
                    }
                    &.t-markup {
                        width:  base.unit-rem-calc(96px);
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        .f-f-input-number {
                            flex: 1;
                        }
                        @include base.respond-to('<medium') {
                            flex-direction: column;
                            align-items: start;
                            gap: 0;
                        }
                        @include base.respond-to('<448px') {
                            width: 100%;
                        }
                    }
                    &.t-total {
                        display: flex;
                        flex: 1;
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        @include base.respond-to('<medium') {
                            flex-direction: column;
                            grid-column: span 3 / auto;
                            align-items: start;
                            gap: 0;
                            width: 100%;
                            .f-f-input-number {
                                width: 100%;
                            }
                        }
                        @include base.respond-to('<448px') {
                            grid-column: span 2 / auto;
                        }
                        .f-f-input-number {
                            flex: 1;
                        }
                        .t-button-remove {
                            display: flex;
                            justify-content: center;
                            @include base.respond-to('<medium') {
                                justify-content: center;
                                width: calc(100% + 16px);
                                border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                                padding: base.unit-rem-calc(8px) 0;
                                margin-left: base.unit-rem-calc(-8px);
                            }
                            .f-f-button-remove {
                                display: inline-flex;
                                > [data-icon] {
                                    margin-right: base.unit-rem-calc(7px) !important;
                                    @include base.respond-to('<medium') {
                                        display: none;
                                    }
                                }
                                > [data-text] {
                                    display: none;
                                    @include base.respond-to('<medium') {
                                        display: inline;
                                    }
                                }
                            }
                        }
                    }
                    .f-f-label {
                        display: none;
                        @include base.respond-to('<medium') {
                            display: inline;
                        }
                    }
                }
            }
        .c-cm-footer {
            display: flex;
            justify-content: flex-end;
            padding: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
            }
        }
            .c-cmf-add {
                @include base.button-text-icon-tertiary;
                padding-right: base.unit-rem-calc(7px) !important;
            }
    }

    .c-pfc-additional-costs {
        border-radius: base.unit-rem-calc(8px);
        margin-top: base.unit-rem-calc(32px);
        &.t-show {
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            background-color: base.$color-background-edit;
            .f-field {
                display: flex;
                flex-wrap: wrap;
                .f-f-label {
                    flex: 1;
                }
                > .f-f-button-remove {
                    display: inline-flex;
                    margin-right: base.unit-rem-calc(-1px);
                }
                > .f-f-button-add {
                    display: none;
                }
                &.t-ac-title {
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px) 0;
                    @include base.respond-to('<medium') {
                        padding-left: base.unit-rem-calc(8px);
                    }
                    .f-f-label {
                        @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(18px));
                    }
                }
            }
            .m-component-additional-costs {
                display: block;
            }
            .c-pfcac-paragraph {
                display: inline-block;
                a {
                    color: base.$color-primary-default;
                    @include base.respond-to('hover') {
                        &:hover {
                            color: base.$color-primary-light-1;
                        }
                        &:active {
                            color: base.$color-primary-dark-1;
                        }
                    }
                }
            }
        }
        .c-pfcac-paragraph {
            @include base.typo-paragraph;
            margin-bottom: 0;
            display: none;
            width: 100%;
        }
    }

    .m-component-additional-costs {
        display: none;
        width: 100%;
        padding-top: base.unit-rem-calc(16px);
        @include base.respond-to('<medium') {
            padding: 0;
        }

        .c-cac-error {
            display: none;
            width: calc(100% - 1rem);
            margin: 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px);
            @include base.callout-error;
            &.t-show {
                display: inline-flex;
            }
        }
        .c-cac-header {
            display: flex;
            align-items: center;
            border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            gap: base.unit-rem-calc(16px);
            padding-left: base.unit-rem-calc(16px);
            padding-right: base.unit-rem-calc(8px);
            @include base.respond-to('<medium') {
                display: none;
            }
            .f-field {
                &.t-item {
                    flex: 1;
                }
                &.t-cost {
                    width:  base.unit-rem-calc(144px);
                }
                &.t-quantity {
                    width:  base.unit-rem-calc(96px);
                }
                &.t-markup {
                    width:  base.unit-rem-calc(96px);
                }
                &.t-total {
                    flex: 1;
                }
            }
        }
        .c-cac-body {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                padding: 0;
                border-bottom: none;
            }
        }
        .c-cacb-row {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                display: grid;
                row-gap: base.unit-rem-calc(8px);
                column-gap: base.unit-rem-calc(16px);
                grid-template-columns: 1fr base.unit-rem-calc(96px) base.unit-rem-calc(96px);
                padding: 0 base.unit-rem-calc(8px);
            }
            @include base.respond-to('<448px') {
                grid-template-columns: 1fr 1fr;
            }
            .f-field {
                &.t-item {
                    display: flex;
                    flex: 1;
                    align-items: center;
                    flex-wrap: nowrap;
                    gap: base.unit-rem-calc(8px);
                    .f-f-input-number {
                        flex: 1;
                    }
                    @include base.respond-to('<medium') {
                        flex-direction: column;
                        grid-column: span 3 / auto;
                        align-items: start;
                        gap: 0;
                    }
                    @include base.respond-to('<448px') {
                        grid-column: span 2 / auto;
                    }
                }
                &.t-cost {
                    width:  base.unit-rem-calc(144px);
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    gap: base.unit-rem-calc(8px);
                    position: relative;
                    &::before {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        content: '$';
                        color: base.$color-grey-dark-1;
                        width: base.unit-rem-calc(16px);
                        height: base.unit-rem-calc(32px);
                        line-height: base.unit-rem-calc(32px);
                        text-align: center;
                        font-size: base.unit-rem-calc(14px);
                        @include base.respond-to('<medium') {
                            left: base.unit-rem-calc(4px);
                        }
                    }
                    &::after {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        content: attr(data-content);
                        font-style: italic;
                        color: base.$color-grey-dark-1;
                        height: base.unit-rem-calc(32px);
                        line-height: base.unit-rem-calc(32px);
                        text-align: center;
                        font-size: base.unit-rem-calc(12px);
                        text-transform: lowercase;
                    }
                    > .f-f-input {
                        padding-right: base.unit-rem-calc(45px);
                        padding-left: base.unit-rem-calc(20px);
                        text-align: right;

                    }
                    @include base.respond-to('<medium') {
                        flex-direction: column;
                        align-items: start;
                        gap: 0;
                        width: 100%;
                    }
                    @include base.respond-to('<448px') {
                        grid-column: span 2 / auto;
                    }
                }
                &.t-quantity {
                    width:  base.unit-rem-calc(96px);
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    gap: base.unit-rem-calc(8px);
                    .f-f-input-number {
                        flex: 1;
                    }
                    @include base.respond-to('<medium') {
                        flex-direction: column;
                        align-items: start;
                        gap: 0;
                    }
                    @include base.respond-to('<448px') {
                        width: 100%;
                    }
                }
                &.t-markup {
                    width:  base.unit-rem-calc(96px);
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    gap: base.unit-rem-calc(8px);
                    .f-f-input-number {
                        flex: 1;
                    }
                    @include base.respond-to('<medium') {
                        flex-direction: column;
                        align-items: start;
                        gap: 0;
                    }
                    @include base.respond-to('<448px') {
                        width: 100%;
                    }
                }
                &.t-total {
                    display: flex;
                    flex: 1;
                    align-items: center;
                    flex-wrap: nowrap;
                    gap: base.unit-rem-calc(8px);
                    @include base.respond-to('<medium') {
                        flex-direction: column;
                        grid-column: span 3 / auto;
                        align-items: start;
                        gap: 0;
                        width: 100%;
                        .f-f-input-number {
                            width: 100%;
                        }
                    }
                    @include base.respond-to('<448px') {
                        grid-column: span 2 / auto;
                    }
                    .f-f-input-number {
                        flex: 1;
                    }
                    .f-f-input {
                        flex: 1;
                    }
                    .t-button-remove {
                        display: flex;
                        justify-content: center;
                        @include base.respond-to('<medium') {
                            justify-content: center;
                            width: calc(100% + 16px);
                            border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                            padding: base.unit-rem-calc(8px) 0;
                            margin-left: base.unit-rem-calc(-8px);
                        }
                        .f-f-button-remove {
                            display: inline-flex;
                            > [data-icon] {
                                margin-right: base.unit-rem-calc(7px) !important;
                                @include base.respond-to('<medium') {
                                    display: none;
                                }
                            }
                            > [data-text] {
                                display: none;
                                @include base.respond-to('<medium') {
                                    display: inline;
                                }
                            }
                        }
                    }
                }
                .f-f-label {
                    display: none;
                    @include base.respond-to('<medium') {
                        display: inline;
                    }
                }
            }
        }
        .c-cac-footer {
            display: flex;
            justify-content: flex-end;
            padding: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
            }
        }
        .c-cacf-add {
            @include base.button-text-icon-tertiary;
            padding-right: base.unit-rem-calc(7px) !important;
        }
    }


    .c-pf-description {
        width: 100%;
    }
    .c-pf-pricing-disclaimer {
        width: 100%;
    }
    .c-pf-attributes {
        width: 100%;
        padding: 0 base.unit-rem-calc(8px);
        &.t-show {
            .f-field {
                display: flex;
                flex-wrap: wrap;
                > .f-f-label {
                    flex: 1;
                }
            }
            .f-f-button-remove {
                display: inline-flex;
            }
            .f-f-button-add {
                display: none;
            }
            .c-pfa-table {
                display: block;
            }
        }
    }
        .c-pfa-table {
            width: 100%;
            display: none;
        }
    .c-pf-categories-terms {
        width: 100%;
        margin-top: base.unit-rem-calc(24px);
    }
        .c-pfct-menu {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: base.unit-rem-calc(8px);
            padding: base.unit-rem-calc(8px);
            background: base.$color-background-form;
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(12px);
            overflow: auto;
        }
            .c-pfctm-tab {
                cursor: pointer;
                height: base.unit-rem-calc(40px);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: base.unit-rem-calc(8px);
                border: base.unit-rem-calc(1px) solid transparent;
                border-radius: base.unit-rem-calc(6px);
                padding: 0 base.unit-rem-calc(12px);
                transition: background-color 0.35s linear, padding 0.25s ease-in-out;
                @include base.respond-to('<small') {
                    padding: 0 base.unit-rem-calc(8px);
                    &.t-active {
                        padding: 0 base.unit-rem-calc(24px);
                    }
                }
                @include base.respond-to('hover') {
                    &:hover {
                        padding: 0 base.unit-rem-calc(24px);
                        background-color: #EDF1F7;
                    }
                }
                &.t-active {
                    color: base.$color-primary-default;
                    background: base.$color-white-default;
                    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                    box-shadow: base.$elevation-level-2;
                    padding: 0 base.unit-rem-calc(24px);
                    .c-csmt-icon, .c-csmt-title {
                        color: base.$color-primary-default;
                    }
                }
                &.t-disabled {
                    cursor: not-allowed;
                    color: base.$color-grey-light-4;
                    .c-csmt-icon, .c-csmt-title {
                        cursor: not-allowed;
                        color: base.$color-grey-light-4;
                    }
                    @include base.respond-to('hover') {
                        &:hover {
                            padding: 0 base.unit-rem-calc(12px);
                            background: transparent;
                            color: base.$color-grey-light-4;
                            .c-mmt-icon, .c-mmt-title {
                                color: base.$color-grey-light-4;
                            }
                        }
                    }
                }
            }
                .c-pfctmt-icon {
                    @include base.svg-icon('default-18');
                    color: base.$color-grey-dark-1;
                }
                .c-pfctmt-title {
                    flex: 1;
                    @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(18px));
                    color: base.$color-grey-dark-1;
                    white-space: nowrap;
                }
        .c-pfct-sections {}
            .c-pfcts-section {
                display: none;
                padding: base.unit-rem-calc(24px) 0 base.unit-rem-calc(32px);
                &.t-active {
                    display: block;
                    .f-field {
                        display: grid;
                        grid-column-gap: base.unit-rem-calc(16px);
                        grid-template-columns: repeat(2, 1fr);
                        grid-column: span 1;
                        @include base.respond-to('<small') {
                            grid-column: span 2;
                        }
                        .c-pfctss-paragraph {
                            grid-column: span 2;
                        }
                        .tag-list {
                            margin: base.unit-rem-calc(4px) 0 0;
                        }
                        @include base.respond-to('<small') {
                            display: block;
                            .tag-list {
                                margin: base.unit-rem-calc(8px) 0 0;
                            }
                        }
                    }
                }
            }
                .c-pfctss-paragraph {
                    @include base.typo-paragraph;
                }
    .c-pf-hidden {
        width: calc(50% - 12px);
        display: none;
        &.t-show {
            display: inline;
        }
    }
}

.m-products-list {
    @include base.full-width-height;
    padding: base.unit-rem-calc(12px);
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(12px) 0;
    }
    .c-pl-table {
        @include base.full-width-height;
        td {
            .t-materials {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                .t-icon {
                    @include base.svg-icon('default-16');
                    color: base.$color-green-light-1;
                }
            }
        }
        .t-icon-checked {
            @include base.svg-icon('default-18');
            color: base.$color-green-light-1;
        }
        .t-icon-unchecked {
            @include base.svg-icon('default-18');
            color: base.$color-grey-light-4;
        }
    }
}

.m-units-list {
    .c-cl-wrapper {
        display: flex;
        flex-direction: column;
        height:  calc(100% - 57px);
        padding: 0 base.unit-rem-calc(12px);
        @include base.respond-to('<small') {
            padding: 0;
        }
        > h4 {
            padding: base.unit-rem-calc(12px) base.unit-rem-calc(12px) base.unit-rem-calc(12px) base.unit-rem-calc(16px);
            margin: 0;
        }
    }
}

.m-product-units-list {
    height: calc(100% - 52px);
    .c-pul-table {
        height: calc(100% + 32px);
    }
}

.m-unit-form {
    display: flex;
    flex-direction: column;
    row-gap: base.unit-rem-calc(8px);
    column-gap: base.unit-rem-calc(16px);
    padding-bottom: base.unit-rem-calc(16px);
}

.m-delete-products-modal {
    .c-dpm-product-list {
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px) 0 base.unit-rem-calc(12px);
        border-radius: base.unit-rem-calc(8px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        background-color: base.$color-background-form;
    }
        .c-dpm-pl-items {
            padding: base.unit-rem-calc(2px) base.unit-rem-calc(8px);
            border-radius: base.unit-rem-calc(4px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            background-color: base.$color-white-default;
            display: inline-flex;
            margin: 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px) 0;
        }
}
@use '~@cac-sass/base';

.m-materials {
    .c-p--pages {
        height: 100%;
        position: relative;
    }
    .c-p--p-page {
        position: relative;
        height: 100%;
        &.t-hidden {
            display: none;
        }
    }

    .m-materials-list {
        padding: base.unit-rem-calc(12px);
        @include base.full-width-height;
        @include base.respond-to('<small') {
            padding: base.unit-rem-calc(12px) 0;
        }
        //  Edit Material Inside Table
        .m-form {
            display: flex;
            flex-direction: column;
            .c-f-grid {
                display: grid;
                grid-template-columns: repeat(6, 1fr);
                row-gap: base.unit-rem-calc(8px);
                column-gap: base.unit-rem-calc(16px);
            }
            .f-field {
                grid-column: span 2;
                &.t-name,
                &.t-unit {
                    grid-column: span 3;
                    @include base.respond-to('<xsmall') {
                        grid-column: span 6;
                    }
                }
                &.t-products {
                    grid-column: span 6;
                    .c-frf-list {
                        display: flex;
                        flex-wrap: wrap;
                        gap: base.unit-rem-calc(8px);
                        padding-bottom: base.unit-rem-calc(16px);
                        > p {
                            margin-bottom: 0;
                            line-height: 1.5;
                            background-color: base.$color-primary-light-1;
                            color: base.$color-white-default;
                            border-radius: base.unit-rem-calc(24px);
                            padding: base.unit-rem-calc(2px) base.unit-rem-calc(12px);
                        }
                    }
                }
                @include base.respond-to('<small') {
                    grid-column: span 3;
                    &.t-unit-price {
                        grid-column: span 6;
                    }
                }
                @include base.respond-to('<xsmall') {
                    grid-column: span 6;
                }
            }
        }
    }
    .c-ml-table {
        height: 100%;
        width: 100%;
        thead {
            @include base.respond-to('<small') {
                // markup
                th:nth-last-child(4) {
                    display: none !important;
                }
                // units
                th:nth-last-child(2) {
                    display: none !important;
                }
            }
        }
        tbody {
            @include base.respond-to('<small') {
                // markup
                td:nth-last-child(4) {
                    display: none !important;
                }
                // units
                td:nth-last-child(2) {
                    display: none !important;
                }
            }
        }
    }

    // Add Material Modal
    .s-modal {
        .m-form {
            display: flex;
            flex-direction: column;
            gap: base.unit-rem-calc(32px);
            padding-bottom: base.unit-rem-calc(8px);
        }
        &.t-add-material {
            .c-f-grid {
                display: grid;
                grid-template-columns: repeat(6, 1fr);
                row-gap: base.unit-rem-calc(8px);
                column-gap: base.unit-rem-calc(16px);
            }
            .f-field {
                grid-column: span 2;
                &.t-name,
                &.t-unit {
                    grid-column: span 3;
                    @include base.respond-to('<xsmall') {
                        grid-column: span 6;
                    }
                }
                @include base.respond-to('<small') {
                    grid-column: span 3;
                    &.t-unit-price {
                        grid-column: span 6;
                    }
                }
                @include base.respond-to('<xsmall') {
                    grid-column: span 6;
                }
            }
        }
    }

    .m-materials-import {

        .m-page-header{
            .t-cancel{
                color: base.$color-grey-dark-1;
                fill: currentColor;
            }
        }

        .c-cl-wrapper {
            margin: base.unit-rem-calc(12px);
        }

        .c-pi-table{
            @include base.full-width-height;

            @include base.respond-to('<small') {
                .c-t-footer {
                    margin: 0 base.unit-rem-calc(-12px);
                }
            }

            .c-t-table-wrapper {
                height: calc(100% - 56px);

                @include base.respond-to('<small') {
                    margin: 0 base.unit-rem-calc(-12px);
                }
            }

            .m-table.t-has-row-edit .c-ttw-table tbody td:first-child {
                overflow: unset;
            }
        }

        .m-table{
            container-type: inline-size;
        }

        .m-table-edit{

            .c-f-row {

                margin-bottom: base.unit-rem-calc(16px);

                &.t-name-unit {
                    display: flex;
                    gap: base.unit-rem-calc(16px);

                    > .c-fr-field {
                        flex: 1;
                        min-width: 0; // Prevents flex items from overflowing
                    }

                    @include base.respond-to('<medium') {
                        flex-direction: column;
                        gap: base.unit-rem-calc(16px);

                        > .c-fr-field {
                            width: 100%;
                        }
                    }
                }

                &.t-cost-markup-price {
                    display: flex;
                    gap: base.unit-rem-calc(16px);

                    > .c-fr-field {
                        flex: 1;
                        min-width: 0; // Prevents flex items from overflowing
                    }

                    @include base.respond-to('<medium') {
                        flex-direction: column;
                        gap: base.unit-rem-calc(16px);

                        > .c-fr-field {
                            width: 100%;
                        }
                    }
                }
            }


        }
    }

    .t-pi-table-columns {

        .id-column {
            max-width: base.unit-rem-calc(20px) !important;
            min-width: unset !important;
        }

        .unit-column {
            max-width: base.unit-rem-calc(55px) !important;
            min-width: unset !important;
        }

        .errors-column {
            max-width: base.unit-rem-calc(10px) !important;
            min-width: unset !important;
        }
    }

    .t-pi-preview-table {
        height: calc(100% - 180px) !important;

        //accounts for the js function this.state.mobile and sets height to auto.
        @media (max-width: 599px), (max-height: 749px) {
            height: auto;
            padding-bottom: base.unit-rem-calc(32px);
        }

        .c-t-table-wrapper {
            height: calc(100% - 56px);

            @include base.respond-to('<small') {
                border-radius: base.unit-rem-calc(8px);
                border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            }
        }
    }

    .c-import-step {
        height: calc(100% - 57px);
        padding: base.unit-rem-calc(12px) base.unit-rem-calc(12px) 0;
        overflow: auto;

        .c-import-step-content {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            height: 100%;

            .final-errors-count {
                color: inherit;
                font-style: normal;
                font-size: base.unit-rem-calc(14px);
            }

            .t-error{
                color: base.$color-red-default;
                font-style: normal;
                margin: base.unit-rem-calc(-8px) 0 base.unit-rem-calc(12px);
            }

            .c-isc-instructions {
                flex: 0 0 auto;

                .c-isci-title {
                    display: block;
                    font-size: base.unit-rem-calc(14px);
                    padding: base.unit-rem-calc(12px) base.unit-rem-calc(12px) 0;
                    @include base.respond-to('<small') {
                        padding: base.unit-rem-calc(4px) base.unit-rem-calc(4px) 0;
                    }
                }

                .c-isci-list {
                    padding-left: base.unit-rem-calc(12px);
                }
            }

            h4 {
                font-size: base.unit-rem-calc(16px);
                line-height: base.unit-rem-calc(22px);
                margin-bottom: base.unit-rem-calc(4px);
            }

            .c-import-header-row {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                flex-wrap: wrap;
                margin-bottom: base.unit-rem-calc(12px);

                h4 {
                    margin: 0 base.unit-rem-calc(8px) base.unit-rem-calc(4px) 0;
                    padding: 0;
                    font-size: base.unit-rem-calc(16px);
                }

                .h-text {
                    white-space: nowrap;
                }
            }

            .m-material-uploader {
                display: flex;
                justify-content: center;
                flex: 1 1 auto;
                min-height: base.unit-rem-calc(150px);
                padding: 0 base.unit-rem-calc(12px) base.unit-rem-calc(24px);
                @include base.respond-to('<small') {
                    padding: 0 base.unit-rem-calc(4px) base.unit-rem-calc(24px);
                }

                .uppy-Root {
                    height: 100%;
                    width: 100%;
                }

                .uppy-DashboardContent-bar,
                .uppy-Dashboard-progressindicators,
                .uppy-Dashboard-files {
                    display: none !important;
                }
                .uppy-Dashboard {
                    margin: 0 auto;
                    height: 100%;
                    width: 100%;
                }

                .uppy-Dashboard .uppy-Dashboard-inner::before {
                    display: none;
                }
                .uppy-Dashboard .uppy-Dashboard-inner .uppy-Dashboard-innerWrap {
                    border: none;
                    width: 100%;
                    .uppy-Dashboard-dropFilesHereHint {
                        border: none;
                    }
                }
                .uppy-Dashboard-inner {
                    display: flex;
                    flex: 1 0 auto;
                    justify-content: center;
                    align-items: center;
                    width: 100% !important;
                    height: 100% !important;
                    max-width: unset;
                    max-height: unset;
                    border: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;
                    background: base.$color-background-edit;
                    @include base.respond-to('<small') {
                        border-radius: base.unit-rem-calc(12px);
                    }
                }

                .uppy-Dashboard.uppy-Dashboard--isDraggingOver .uppy-Dashboard-inner {
                    background: rgba(255, 255, 255, 0.7);
                    border: base.unit-rem-calc(1px) dashed base.$color-primary-light-2;
                }
                @keyframes iconBounce {
                    0%, 100% {
                        background-position: center calc(50% + 0px);
                    }
                    50% {
                        background-position: center calc(50% - 8px);
                    }
                }

                .uppy-Dashboard.uppy-Dashboard--isDraggingOver .uppy-Dashboard-dropFilesHereHint {
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23005AD0'%3E%3Cpath d='M12 2C17.52 2 22 6.48 22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2ZM12 20C16.42 20 20 16.42 20 12C20 7.58 16.42 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20ZM13 12H16L12 16L8 12H11V8H13V12Z'%3E%3C/path%3E%3C/svg%3E");
                    background-color: base.$color-primary-light-4;
                    color: base.$color-primary-default;
                    outline: base.unit-rem-calc(8px) solid base.$color-primary-light-4;
                    background-size: base.unit-rem-calc(64px) base.unit-rem-calc(64px);
                    background-repeat: no-repeat;
                    background-position: center;
                    font-weight: bold;
                    animation: iconBounce 1s ease-in-out infinite;
                }

                .uppy-Dashboard.uppy-Dashboard--isDraggingOver .uppy-Dashboard-AddFiles {
                    opacity: 0;
                }
            }
        }

        .c-pi-components {
            h4 {
                margin-bottom: 0;
                font-size: base.unit-rem-calc(16px);
            }

            .c-pfc-input-container {
                display: grid;
                grid-template-columns: repeat(4, minmax(0, 1fr));
                width: 100%;
                row-gap: base.unit-rem-calc(8px);
                column-gap: base.unit-rem-calc(16px);

                @include base.respond-to('<medium') {
                    grid-template-columns: repeat(3, minmax(0, 1fr));
                }

                @include base.respond-to('<small') {
                    grid-template-columns: 1fr;
                }

                .f-f-label {
                    font-size: base.unit-rem-calc(14px);
                }

                .f-f-input {
                    width: 100%;
                }
            }
        }

        .c-pi-mapping {
            background-color: base.$color-white-default;
            border:none;
            margin-bottom: base.unit-rem-calc(32px);
        }

        .t-pi-table-columns {

            .id-column {
                max-width: base.unit-rem-calc(20px) !important;
                min-width: unset !important;
            }

            .unit-column {
                max-width: base.unit-rem-calc(55px) !important;
                min-width: unset !important;
            }

            .errors-column {
                max-width: base.unit-rem-calc(20px) !important;
                min-width: unset !important;
            }
        }

    }

}
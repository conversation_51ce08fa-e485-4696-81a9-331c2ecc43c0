'use strict';

const Api = require('@ca-package/api');
const FormValidator = require('@ca-submodule/validator');
const Modal = require('@ca-submodule/modal').Base;
const modal_tpl = require('@cam-company-profile-tpl/modals/bid-customization/template/create_update.hbs');
const CodeMirror = require('codemirror');
const lang = require('lodash/lang');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));

require('codemirror/addon/display/autorefresh');
require('codemirror/mode/htmlembedded/htmlembedded');
require('codemirror/mode/css/css');
const Tooltip = require("@cas-tooltip-js");

class CreateUpdate extends Modal {
    constructor(parent) {
        super(
            modal_tpl({
                ns: parent.state.is_update ? 'edit' : 'add',
                info: window.fx_url.assets.IMAGE + 'icons/info.png'
            }),
            {
                size: Modal.Size.TINY,
                classes: ['t-template-create-update']
            }
        );
        Object.assign(this.state, {
            template_id: null,
            external_close: false
        });
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });

        this.elem.form_elements = this.elem.root.find(
            'input, textarea, select'
        );
        this.elem.form = this.elem.root.find('form');
        this.elem.template_alias_wrapper = this.elem.form.fxFind(
            'template-alias-wrapper'
        );

        this.elem.codemirror_content =
            this.elem.form.fxFind('codemirror-content');
        this.elem.codemirror_styles =
            this.elem.form.fxFind('codemirror-styles');
        this.elem.content_container = this.elem.root.find('.content-container');
        this.elem.styles_container = this.elem.root.find('.styles-container');
        this.elem.type_container = this.elem.root.find('.type-container');
        this.elem.type_display = this.elem.root.find('.type-display');
        this.elem.type_name = this.elem.form.fxFind('type-name');

        this.elem.input = {};
        for (let name of [
            'name',
            'alias',
            'type',
            'styles',
            'content',
            'is_default'
        ]) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        FormInput.init(this.elem.input.is_default);

        Tooltip.initAll(this.elem.root);

        this.state.form = FormValidator.init(this.elem.form).on(
            'form:submit',
            () => {
                this.save();
                return false;
            }
        );
        let fields = {
            name: {
                required: true,
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            type: {},
            alias: {}
        };

        this.state.field = {};

        for (let name in fields) {
            fields[name].requiredMessage = 'Required';
            this.state.field[name] = this.elem.input[name].parsley(
                fields[name]
            );
        }

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
            this.reset();
        });

        let map = { 68: false, 69: false, 86: false };
        $(this.elem.root)
            .keydown((e) => {
                if (e.keyCode in map) {
                    map[e.keyCode] = true;
                    if (map[68] && map[69] && map[86]) {
                        if (
                            this.elem.template_alias_wrapper.hasClass('hidden')
                        ) {
                            this.elem.template_alias_wrapper.removeClass(
                                'hidden'
                            );
                            if (!this.dev_mode) {
                                this.devEditor();
                            }
                        }
                    }
                }
            })
            .keyup(function (e) {
                if (e.keyCode in map) {
                    map[e.keyCode] = false;
                }
            });
    };

    devEditor() {
        this.elem.root.width('63rem');
        this.dev_mode = true;
        this.elem.codemirror_content.show();
        this.elem.codemirror_styles.show();
        if (this.content_editor != undefined) {
            this.content_editor.setValue(this.elem.content.val());
            this.content_editor.refresh();
        } else {
            this.content_editor = CodeMirror.fromTextArea(
                this.elem.input.content[0],
                {
                    mode: 'htmlembedded',
                    autoRefresh: true,
                    lineNumbers: true,
                    theme: 'darcula',
                    indentUnit: 4,
                    extraKeys: {
                        Tab: function (cm) {
                            let spaces = Array(
                                cm.getOption('indentUnit') + 1
                            ).join(' ');
                            cm.replaceSelection(spaces, 'end', '+input');
                        }
                    }
                }
            );
        }
        if (this.styles_editor != undefined) {
            this.styles_editor.setValue(this.elem.styles.val());
            this.styles_editor.refresh();
        } else {
            this.styles_editor = CodeMirror.fromTextArea(
                this.elem.input.styles[0],
                {
                    mode: 'css',
                    autoRefresh: true,
                    lineNumbers: true,
                    theme: 'darcula',
                    indentUnit: 4,
                    extraKeys: {
                        Tab: function (cm) {
                            let spaces = Array(
                                cm.getOption('indentUnit') + 1
                            ).join(' ');
                            cm.replaceSelection(spaces, 'end', '+input');
                        }
                    }
                }
            );
        }
        this.content_editor.setValue(this.elem.input.content.val());
        this.styles_editor.setValue(this.elem.input.styles.val());
        this.elem.root.addClass('small').removeClass('tiny');
        this.elem.template_alias_wrapper.removeClass('hidden');
    };

    clearForm() {
        for (let item of this.elem.form_elements) {
            let this_item = $(item);

            if (this_item.is('textarea')) {
                this_item.val('');
            } else {
                switch (this_item.attr('type')) {
                    case 'submit':
                        break;
                    default:
                        this_item.val('');
                }
            }
        }
        this.dev_mode = false;
        this.elem.type_container.hide();
        this.elem.type_display.show();
        this.elem.content_container.hide();
        this.elem.styles_container.hide();
        if (this.content_editor !== undefined) {
            this.content_editor.setValue('');
            this.content_editor.refresh();
        }
        if (this.styles_editor !== undefined) {
            this.styles_editor.setValue('');
            this.styles_editor.refresh();
        }
        this.elem.root.addClass('tiny').removeClass('small');
        this.elem.root.scrollTop(0);
        this.elem.alias.addClass('hidden');
        this.formReset();
    };

    /**
     * Load template by id
     *
     * @param {string} template_id - UUID
     * @returns {Promise<void>}
     */
    async loadData(template_id) {
        let { data: template } = await Api.Resources.ContentTemplates()
            .fields([
                'id',
                'name',
                'type_name',
                'type',
                'is_default',
                'alias',
                'styles',
                'content'
            ])
            .retrieve(template_id);
        return template;
    };

    /**
     * Populate fields using data object
     *
     * @param {object} data
     */
    populate(data) {
        this.elem.input.name.val(data.name);
        this.elem.input.alias.val(data.alias);
        this.elem.input.content.val(data.content);
        this.elem.input.styles.val(data.styles);
        this.elem.type_name.text(data.type_name);
        this.elem.input.is_default.prop('checked', data.is_default).trigger('change');
    };


    loadCodeMirrorNew() {
        if (!this.dev_mode) {
            this.devEditor();
        }
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {object} $0.config
     * @param {Promise} $0.promise
     */
    open({ config = null, promise }) {
        this.state.promise = promise;
        this.state.template_id = config.template_id || null;

        if (this.state.template_id === null) {
            this.setTitle('Add Content Template');
            this.loadCodeMirrorNew();
            this.elem.type_container.show();
            this.elem.type_display.hide();

        } else {
            this.startWorking();
            this.setTitle('Edit Content Template');
            this.elem.type_name.show();
            let data_load = this.loadData(this.state.template_id);
            Promise.all([data_load]).then(
                ([data]) => {
                    this.populate(data);
                    this.resetWorking();
                },
                (error) => {
                    this.showErrorMessage(
                        'Unable to load template info, please contact support'
                    );
                    console.log(error);
                }
            );
        }
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            handler: () => this.elem.form.submit()
        });
        super.open();
    };
    /**
     * Save changes
     */
    save() {
        this.clearMessages();
        this.startWorking();

        let data = {
            name: this.elem.input.name.val(),
            alias: this.elem.input.alias.val(),
            is_default: this.elem.input.is_default.is(":checked"),
        };
     
        if(this.dev_mode) {
            data['content'] = this.content_editor.getValue();
            data['styles'] = this.styles_editor.getValue();
        };
        
        if (this.state.template_id === null) {
            data['type'] = parseInt(this.elem.input.type.val());
        };
        let resource = Api.Resources.ContentTemplates(),
            request =
                this.state.template_id !== null
                    ? resource.partialUpdate(this.state.template_id, data)
                    : resource.store(data);
        request.then(
            ({ data }) => {
                this.state.promise.resolve(data);
            },
            (error, response) => {
                switch (response.statusCode()) {
                    case 422:
                        let item_errors = response.data().errors;
                        for (let item in item_errors) {
                            this.state.field[item].addError('fx-' + item, {
                                message: item_errors[item]
                            });
                        }
                        break;
                    default:
                        this.showErrorMessage(
                            'Unable to save template, please contact support'
                        );
                        break;
                }
            }
        );
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.resetWorking();
        this.state.template_id = null;
        this.state.form.reset();
        this.elem.form[0].reset();
        this.removeAction(this.state.save_action);
        this.elem.input.is_default.prop('checked', false).trigger('change');
    };

    /**
     * Externally close modal without triggering certain events
     */
    externalClose() {
        this.state.external_close = true;
        this.close();
        this.state.external_close = false;
    };
}

module.exports = CreateUpdate;

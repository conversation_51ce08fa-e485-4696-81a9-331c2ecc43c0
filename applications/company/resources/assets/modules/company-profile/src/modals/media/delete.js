'use strict';

const Api = require('@ca-package/api');

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-company-profile-tpl/modals/media/delete.hbs');


class Delete extends Confirm {
    constructor() {
        super();
        this.setTitle('Delete Media');
        this.setContent(content_tpl());
        this.elem.media = this.elem.content.fxFind('media');
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.media_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({media_id, filename,  promise}) {
        this.state.media_id = media_id;
        this.elem.media.text(filename);
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        this.startWorking();
        Api.Resources.Media()
            .partialUpdate(this.state.media_id, {
                status: Api.Constants.Media.Status.ARCHIVED
            })
            .then(() => {
                this.resetWorking();
                this.state.promise.resolve(true);
            }, (error) => {
                let message = 'Unable to delete media';
                if (error.code === 1009) { //forbidden
                    message = 'Cannot delete a media attached to a product';
                }
                this.showErrorMessage(message);
                this.resetWorking();
            });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
    };
}

module.exports = Delete;

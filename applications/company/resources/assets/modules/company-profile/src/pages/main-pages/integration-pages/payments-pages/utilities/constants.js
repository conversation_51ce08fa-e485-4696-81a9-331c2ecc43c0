'use strict';

/**
 * CoPilot Payment System Constants
 * 
 * Defines the mapping between ownership types and their allowed owner titles
 * Based on CoPilot API requirements
 */

// Application Status Constants (matches PropelrMerchant.php)
const APPLICATION_STATUS_CREATED = 1;
const APPLICATION_STATUS_PENDING_SIGNATURE = 2;
const APPLICATION_STATUS_QUALIFY = 3;
const APPLICATION_STATUS_UNDER = 4;
const APPLICATION_STATUS_BOARDING = 5;
const APPLICATION_STATUS_BOARDED = 6;
const APPLICATION_STATUS_LIVE = 7;
const APPLICATION_STATUS_DECLINED = 8;

// Gateway Status Constants
const GATEWAY_STATUS_NOT_BOARDED = 1;
const GATEWAY_STATUS_BOARDED = 2;

// Signature Status Constants
const SIGNATURE_STATUS_PENDING = 1;
const SIGNATURE_STATUS_SIGNED = 2;

// Ready to Process Status Constants
const IS_READY_TO_PROCESS_PENDING = 1;
const IS_READY_TO_PROCESS_READY = 2;

const OWNERSHIP_TYPE_TITLES = {
    'PRTNRSHP': [
        { value: 'PARTNER', label: 'Partner' }
    ],
    'GOVT': [
        { value: 'OWNER', label: 'Owner' },
        { value: 'PARTNER', label: 'Partner' },
        { value: 'PRESIDENT', label: 'President' },
        { value: 'VICE_PRESIDENT', label: 'Vice President' },
        { value: 'MEMBER_LLC', label: 'Member (LLC)' },
        { value: 'SECRETARY', label: 'Secretary' },
        { value: 'TREASURER', label: 'Treasurer' },
        { value: 'CEO', label: 'CEO' },
        { value: 'CFO', label: 'CFO' },
        { value: 'COO', label: 'COO' }
    ],
    'PUBCORP': [
        { value: 'PRESIDENT', label: 'President' },
        { value: 'VICE_PRESIDENT', label: 'Vice President' },
        { value: 'SECRETARY', label: 'Secretary' },
        { value: 'TREASURER', label: 'Treasurer' },
        { value: 'CEO', label: 'CEO' },
        { value: 'CFO', label: 'CFO' },
        { value: 'COO', label: 'COO' }
    ],
    'LLC': [
        { value: 'PRESIDENT', label: 'President' },
        { value: 'VICE_PRESIDENT', label: 'Vice President' },
        { value: 'SECRETARY', label: 'Secretary' },
        { value: 'TREASURER', label: 'Treasurer' },
        { value: 'CEO', label: 'CEO' },
        { value: 'CFO', label: 'CFO' },
        { value: 'COO', label: 'COO' },
        { value: 'MEMBER_LLC', label: 'Member (LLC)' }
    ],
    'PRIVCORP': [
        { value: 'PRESIDENT', label: 'President' },
        { value: 'VICE_PRESIDENT', label: 'Vice President' },
        { value: 'SECRETARY', label: 'Secretary' },
        { value: 'TREASURER', label: 'Treasurer' },
        { value: 'CEO', label: 'CEO' },
        { value: 'CFO', label: 'CFO' },
        { value: 'COO', label: 'COO' }
    ],
    'TAXEXMPT': [
        { value: 'OWNER', label: 'Owner' },
        { value: 'PARTNER', label: 'Partner' },
        { value: 'PRESIDENT', label: 'President' },
        { value: 'VICE_PRESIDENT', label: 'Vice President' },
        { value: 'MEMBER_LLC', label: 'Member (LLC)' },
        { value: 'SECRETARY', label: 'Secretary' },
        { value: 'TREASURER', label: 'Treasurer' },
        { value: 'CEO', label: 'CEO' },
        { value: 'CFO', label: 'CFO' },
        { value: 'COO', label: 'COO' }
    ],
    'NONPRFT': [
        { value: 'OWNER', label: 'Owner' },
        { value: 'PARTNER', label: 'Partner' },
        { value: 'PRESIDENT', label: 'President' },
        { value: 'VICE_PRESIDENT', label: 'Vice President' },
        { value: 'MEMBER_LLC', label: 'Member (LLC)' },
        { value: 'SECRETARY', label: 'Secretary' },
        { value: 'TREASURER', label: 'Treasurer' },
        { value: 'CEO', label: 'CEO' },
        { value: 'CFO', label: 'CFO' },
        { value: 'COO', label: 'COO' }
    ],
    'INDIVSOLE': [
        { value: 'OWNER', label: 'Owner' }
    ]
};

const IS_READY_TO_PROCESS_TOOLTIP_CONTENT_MAP = {
    [IS_READY_TO_PROCESS_PENDING]: 'Contractor Accelerator is finalizing the connection. Check back in 24 hours or contact <NAME_EMAIL>',
    [IS_READY_TO_PROCESS_READY]: 'You are ready to process transactions in Contractor Accelerator'
};

/**
 * Status maps for merchant application states
 */
const APPLICATION_STATUS_MAP = {
    [APPLICATION_STATUS_CREATED]: {
        text: 'Created',
        class: 't-grey'
    },
    [APPLICATION_STATUS_PENDING_SIGNATURE]: {
        text: 'Pending Signature',
        class: 't-yellow'
    },
    [APPLICATION_STATUS_QUALIFY]: {
        text: 'Qualifying',
        class: 't-blue'
    },
    [APPLICATION_STATUS_UNDER]: {
        text: 'Under Review',
        class: 't-blue'
    },
    [APPLICATION_STATUS_BOARDING]: {
        text: 'Boarding',
        class: 't-blue'
    },
    // todo - @amanda - Do we need to set the text here to be Approved or ready instead of Boarded / Live?
    [APPLICATION_STATUS_BOARDED]: {
        text: 'Boarded',
        class: 't-green'
    },
    // todo - @amanda - same for the live status.
    [APPLICATION_STATUS_LIVE]: {
        text: 'Live',
        class: 't-green'
    },
    [APPLICATION_STATUS_DECLINED]: {
        text: 'Declined',
        class: 't-red'
    }
};

const READY_TO_PROCESS_STATUS_MAP = {
    [IS_READY_TO_PROCESS_PENDING]: {
        text: 'Pending',
        class: 't-yellow'
    },
    [IS_READY_TO_PROCESS_READY]: {
        text: 'Ready',
        class: 't-green'
    }
};

// todo - @amanda - Same idea here - do we want to change the text to be Pending and Ready instead of Not Boarded and Boarded?
const GATEWAY_STATUS_MAP = {
    [GATEWAY_STATUS_NOT_BOARDED]: {
        text: 'Pending',
        class: 't-yellow'
    },
    [GATEWAY_STATUS_BOARDED]: {
        text: 'Ready',
        class: 't-green'
    }
};

/**
 * Format application status for display
 * @param {number} status_id - The application status ID
 * @returns {string} - Formatted HTML string
 */
function formatApplicationStatus(status_id) {
    const status = APPLICATION_STATUS_MAP[status_id];
    if (!status) {
        return '<span class="h-text t-grey">Unknown</span>';
    }
    return `<span class="h-text ${status.class}">${status.text}</span>`;
}

/**
 * Format ready to process status for display
 * @param {number} status_id - The ready to process status ID
 * @returns {string} - Formatted HTML string
 */
function formatReadyToProcessStatus(status_id) {
    const status = READY_TO_PROCESS_STATUS_MAP[status_id];
    if (!status) {
        return '<span class="h-text t-grey">Unknown</span>';
    }
    return `<span class="h-text ${status.class}">${status.text}</span>`;
}

/**
 * Format gateway status for display
 * @param {number} status_id - The gateway status ID
 * @returns {string} - Formatted HTML string
 */
function formatGatewayStatus(status_id) {
    const status = GATEWAY_STATUS_MAP[status_id];
    if (!status) {
        return '<span class="h-text t-grey">Unknown</span>';
    }
    return `<span class="h-text ${status.class}">${status.text}</span>`;
}

module.exports = {
    APPLICATION_STATUS_CREATED,
    APPLICATION_STATUS_PENDING_SIGNATURE,
    APPLICATION_STATUS_QUALIFY,
    APPLICATION_STATUS_UNDER,
    APPLICATION_STATUS_BOARDING,
    APPLICATION_STATUS_BOARDED,
    APPLICATION_STATUS_LIVE,
    APPLICATION_STATUS_DECLINED,
    GATEWAY_STATUS_NOT_BOARDED,
    GATEWAY_STATUS_BOARDED,
    SIGNATURE_STATUS_PENDING,
    SIGNATURE_STATUS_SIGNED,
    IS_READY_TO_PROCESS_PENDING,
    IS_READY_TO_PROCESS_READY,
    
    OWNERSHIP_TYPE_TITLES,
    IS_READY_TO_PROCESS_TOOLTIP_CONTENT_MAP,
    APPLICATION_STATUS_MAP,
    READY_TO_PROCESS_STATUS_MAP,
    GATEWAY_STATUS_MAP,
    
    formatApplicationStatus,
    formatReadyToProcessStatus,
    formatGatewayStatus
};
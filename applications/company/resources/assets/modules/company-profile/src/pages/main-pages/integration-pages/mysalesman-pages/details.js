'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Tooltip = require('@ca-submodule/tooltip');

const {findChild, jsSelector, onClick} = require("@ca-package/dom");

const mysalesman_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/mysalesman-pages/details.hbs');

class Details extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            integrations: parent.getParentByName('integrations'),
            allow_mysalesman: profile_data.is_mysalesman_industry,
            feature_enabled: profile_data.features.mysalesman
        });
    };

    /**
     * Get status highlight html
     *
     * @returns {Map<boolean, string>}
     */
    get status () {
        return new Map([
            [false, '<span class="h-text t-grey">Disabled</span>'],
            [true, '<span class="h-text t-green">Enabled</span>']
        ]);
    };

    /**
     * Populate fields using data object
     *
     * @param {object} data
     */
    populate(data) {
        this.elem.integration_enabled.html(
            this.status.get(data.my_salesman_integration_enabled)
        );

        this.elem.capture_dropoffs.html(
            this.status.get(data.my_salesman_capture_dropoffs)
        );

        this.elem.send_notifications.html(
            this.status.get(data.my_salesman_send_notifications)
        );

        let email_recipients = '<em>None</em>';
        if (data.leads_additional_email_recipients.length > 0) {
            email_recipients = data.leads_additional_email_recipients.join(', ');
        }
        this.elem.additional_email_recipients.html(email_recipients);

        let default_assigned_to = '<em>None</em>';
        if (data.my_salesman_default_assigned_to !== null) {
            default_assigned_to = data.my_salesman_default_assigned_to_name;
        }
        this.elem.default_assigned_to_name.html(default_assigned_to);
    };

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: company_entity} = await Api.Resources.Companies().
            fields(['id']).relations({
                'settings': {}
            }).retrieve('current');

            let data = {
                my_salesman_integration_enabled: company_entity.settings.my_salesman_integration_enabled ?? false,
                my_salesman_api_key: company_entity.settings.my_salesman_api_key ?? '',
                my_salesman_default_assigned_to: company_entity.settings.my_salesman_default_assigned_to ?? null,
                my_salesman_capture_dropoffs: company_entity.settings.my_salesman_capture_dropoffs ?? false,
                my_salesman_send_notifications: company_entity.settings.my_salesman_send_notifications ?? false,
                leads_additional_email_recipients: company_entity.settings.leads_additional_email_recipients ?? []
            };

            if (data.my_salesman_default_assigned_to !== null) {
                let {data: user_entity} = await Api.Resources.Users().
                fields(['id', 'first_name', 'last_name']).retrieve(data.my_salesman_default_assigned_to);
                data['my_salesman_default_assigned_to_name'] = `${user_entity.first_name} ${user_entity.last_name}`
            }

            this.populate(data);
        } catch (e) {}
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.elem.root.scrollTop(0);
        await super.unload(request, next);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.integrations.showLoader();

        if (!this.state.allow_mysalesman) {
            this.router.navigate('integrations.quickbooks.details');
        }
        if (this.state.feature_enabled) {
            await this.fetchData();
        }
        this.state.integrations.hideLoader();
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.settings = findChild(root, jsSelector('settings'));
        this.elem.mysalesman_feature_content = findChild(root, jsSelector('mysalesman-feature-content'));
        this.elem.mysalesman_teaser_content = findChild(root, jsSelector('mysalesman-teaser-content'));

        this.elem.integration_enabled = findChild(root, jsSelector('integration-enabled'));
        this.elem.default_assigned_to_name = findChild(root, jsSelector('default-assigned-to-name'));
        this.elem.capture_dropoffs = findChild(root, jsSelector('capture-dropoffs'));
        this.elem.send_notifications = findChild(root, jsSelector('send-notifications'));
        this.elem.additional_email_recipients = findChild(root, jsSelector('additional-email-recipients'));

        if (this.state.feature_enabled) {
            this.elem.mysalesman_feature_content.removeClass('t-hidden');
            this.elem.settings.removeClass('t-hidden');
        } else {
            this.elem.mysalesman_teaser_content.removeClass('t-hidden');
        }

        onClick(this.elem.settings, async () => {
            this.router.navigate('integrations.mysalesman.settings');
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return mysalesman_tpl();
    };
}

module.exports = Details;
'use strict';

const states = require('@cac-js/data/states');
const us_territories = require('@cac-js/data/us_territories');
const provinces = require('@cac-js/data/provinces');
const {FIELDS, TYPES, GOOGLE_RECAPTCHA_URL} = require("./constants");

class LeadFormBuilder {

    /**
     * Constructor
     * @param {Object} form_props - Properties of the form
     * @param {Object[]} marketing_types - Marketing properties
     * @param {Object[]} project_types - Project types
     * @param {boolean} isMock - Whether the form is a mock (disables submission)
     */
    constructor({form_props, marketing_types,project_types,  isMock = false}) {
        if (Array.isArray(form_props.fields)) {
            form_props.fields = form_props.fields.reduce((acc, field) => {
                acc[field.reference] = field;
                return acc;
            }, {});
        }

        this.form_properties = form_props;
        this.marketing_types = marketing_types || [];
        this.project_types = project_types || [];
        this.isMock = isMock;
        this.elem = {};
        this.state = {};
    }

    generateHTML() {
        const url = window.fx_url.BASE + 'website-leads-form/' + this.form_properties.token;
        return `<iframe src="${url}" style="width: 100%; height: 100vh; border: none;"></iframe>`;
    }


    /**
     * Build the complete HTML for the form
     */
    async buildForm() {
        const props = {
            form_attributes: '',
            token_field: '',
            captcha_script: '',
            gtm_script: '',
        }

        if (!this.isMock) {
            props.form_attributes = `action="${this.form_properties.form_url}" method="POST"`;
            props.token_field = `<input type="hidden" name="token" value="${this.form_properties.token}">`;
            props.captcha_script = this.buildCaptchaJSSection();
            props.gtm_script = this.buildGoogleTagManagerSection();
        }

        const {title = 'Default Title'} = this.form_properties || {};
        const appointment_request_section = this.buildAppointmentRequestSection();

        return `
                <div class=ca-form-wrapper>
                    <form id="ca-lead-form" data-js="ca-lead-form" ${props.form_attributes}>${props.token_field}<h3 class="i-overflow">${title}</h3>
                        ${this.buildPersonalDataSection()}
                        ${this.buildContactSection()}
                        ${this.buildAddressSection()}
                        ${this.buildMarketingSection()}
                        ${this.buildCustomerNotesSection()}
                        ${this.buildEmailCheckboxSection()}
                        ${this.buildUploadFilesSection()}
                        ${appointment_request_section.html}
                        ${this.buildSubmitButton()}
                        ${this.buildFooterSection()}
                    </form>
                    ${props.captcha_script}
                    ${props.gtm_script}
                    ${appointment_request_section.script}
                </div>`
    }


    /**
     * Build the reCAPTCHA JS section
     *
     * @returns {string}
     */
    buildCaptchaJSSection() {
        return `<script>
            const loadReCaptcha = () => {
                const recaptchaScript = document.createElement('script');
                recaptchaScript.src = '${GOOGLE_RECAPTCHA_URL}?render=${this.form_properties.captcha_key}'; 
                document.head.appendChild(recaptchaScript);
            }
    
            const executeReCaptcha = () => {
                const key = '${this.form_properties.captcha_key}';
                grecaptcha.ready(() => {
                    grecaptcha.execute(key, {action: 'lead_submit'}).then((token) => {
                        const event = new CustomEvent('recaptcha_token_set', { token: token});
                        document.getElementById('recaptcha_token').value = token;
                        document.dispatchEvent(event); 
                    });
                });
            }
    
            loadReCaptcha();
    
            document.addEventListener('trigger_recaptcha_execution', function() {
                executeReCaptcha();
            });
        </script>`;
    }

    /**
     * Build the Google Tag Manager snippets
     * – <script> in head-equivalent
     * – <noscript> fallback immediately after form
     */
    buildGoogleTagManagerSection() {
        const id = this.form_properties.google_tag_id;
        if (!id) return '';

        return `
        <script>
          (function(w,d,s,l,i){
            w[l]=w[l]||[]; w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});
            var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
            j.async=true; j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
            f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${id}');
        </script>
        
        <noscript>
          <iframe src="https://www.googletagmanager.com/ns.html?id=${id}"
                  height="0" width="0" style="display:none;visibility:hidden">
          </iframe>
        </noscript>
      `;
    }

    buildFooterSection() {
        const privacy_policy = 'https://policies.google.com/privacy';
        const terms_of_service = 'https://policies.google.com/terms';

        return `<div class="form-footer">
            <p>This form is protected by reCAPTCHA and Google <a href="${privacy_policy}" target="_blank">Privacy Policy</a> and <a href="${terms_of_service}" target="_blank">Terms of Service</a> apply.</p>
        </div>`;
    }

    buildPersonalDataSection() {
        return `<div class="form-section personal-data-section two-columns" data-js="personal-section">
            <div class="form-group f-field">
                <label class="f-f-label" for="first_name">First Name</label>
                <input type="text" name="first_name" id="first_name" class="form-control f-f-input" required>
            </div>
            <div class="form-group f-field">
                <label class="f-f-label" for="last_name">Last Name</label>
                <input type="text" name="last_name" id="last_name" class="form-control f-f-input" required>
            </div>
        </div>`;
    }


    /**
     * Build the contact section HTML
     * @returns {string} - Raw HTML for the contact section
     */
    buildContactSection() {
        const { email, phone } = this.form_properties.fields;
        let activeFields = 0;

        if (email && email.is_enabled) activeFields++;
        if (phone && phone.is_enabled) activeFields++;

        let section_style = '';
        switch (activeFields) {
            case 2:
                section_style  = 'two-columns';
                break;
            case 3:
                section_style = 'three-columns';
                break;
        }
        let contactHTML = `<div class="contact-section ${section_style}">`;

        if (email && email.is_enabled) {
            contactHTML += this.createInput(FIELDS.EMAIL, email.is_required);
            activeFields++
        }

        if (phone && phone.is_enabled) {
            contactHTML += this.createInput(FIELDS.PHONE, phone.is_required);
            activeFields++
        }

        contactHTML += '</div>';
        return contactHTML;
    }

    /**
     * Build the address section HTML
     * @returns {string} - Raw HTML for the address section
     */
    buildAddressSection() {
        const { address } = this.form_properties.fields;
        if (!address || !address.is_enabled) return '';

        return `<div class="address-section" data-js="address-section">
                <div class="address1-section" data-js="address1-section">
                    ${this.createInput(FIELDS.ADDRESS, address.is_required)}
                </div>
                <div class="address2-section three-columns" data-js="address2-section">
                    ${this.createInput(FIELDS.CITY, address.is_required)}
                    ${this.createSelect(FIELDS.STATE, address.is_required, [
                        { label: 'States', options: states },
                        { label: 'US Territories', options: us_territories },
                        { label: 'Provinces', options: provinces }
                    ])}
                    ${this.createInput(FIELDS.POSTAL_CODE, address.is_required)}
                </div>
            </div>`;
    }

    /**
     * Build the marketing section HTML
     * @returns {string} - Raw HTML for the marketing and project type section
     */
    buildMarketingSection() {
        const { marketing_source, project_type } = this.form_properties.fields;
        let activeFields = 0;

        if (marketing_source && marketing_source.is_enabled) activeFields++;
        if (project_type && project_type.is_enabled) activeFields++;

        const section_style = activeFields === 2 ? 'two-columns' : '';
        let marketingHTML = `<div class="form-section marketing-section ${section_style}">`;

        // Marketing Source Section
        if (marketing_source && marketing_source.is_enabled) {
            const label = marketing_source.label ? marketing_source.label : FIELDS.MARKETING_SOURCE.label;
            const field = { name: FIELDS.MARKETING_SOURCE.name, label }

            if (marketing_source.field_type === TYPES.DROPDOWN) {
                marketingHTML += this.createSelect(field, marketing_source.is_required, this.marketing_types);
            } else {
                marketingHTML += this.createInput(field, marketing_source.is_required);
            }
        }

        // Project Type Section
        if (project_type && project_type.is_enabled) {
            const label = project_type.label ? project_type.label : FIELDS.PROJECT_TYPE.label;
            const field = { name: FIELDS.PROJECT_TYPE.name, label }

            if (project_type.field_type === TYPES.DROPDOWN) {
                marketingHTML += this.createSelect(field, project_type.is_required, this.project_types);
            } else {
                marketingHTML += this.createInput(field, project_type.is_required);
            }
        }

        marketingHTML += '</div>';
        return marketingHTML;
    }

    /**
     * Build the customer notes section HTML
     * @returns {string} - Raw HTML for the customer notes section
     */
    buildCustomerNotesSection() {
        const { customer_notes } = this.form_properties.fields;
        if (!customer_notes || !customer_notes.is_enabled) return '';

        const label = customer_notes.label ? customer_notes.label : FIELDS.CUSTOMER_NOTES.label;
        const field = { name: FIELDS.CUSTOMER_NOTES.name, label }

        return `<div class="customer-notes-section">
                ${this.createTextarea(field, customer_notes.is_required)}
            </div>`;
    }

    buildEmailCheckboxSection() {
        const { email_checkbox } = this.form_properties.fields;
        if (!email_checkbox || !email_checkbox.is_enabled) return '';

        const label = email_checkbox.label ? email_checkbox.label : FIELDS.EMAIL_CHECKBOX.label;
        const field = { name: FIELDS.EMAIL_CHECKBOX.name, label }

        return `<div class="email-checkbox-section">
                ${this.createCheckbox(field, email_checkbox.is_required)}
                <div id="${field.name}-error-container" class="f-field"></div>
            </div>`;
    }

    buildUploadFilesSection() {
        const {upload_file} = this.form_properties.fields;
        if (!upload_file || !upload_file.is_enabled) return '';

        const label = upload_file.label ? upload_file.label : FIELDS.UPLOAD_FILE.label;
        const field = {name: FIELDS.UPLOAD_FILE.name, label}
        const requiredText = upload_file.is_required ? '' : '(Optional)';

        return `<div class="c-c-files">
                  <div class="upload-files-section">
                      <div class="form-group f-field">
                          <div class="c-ci-upload-title-section" data-js="upload-title-section">
                                <label class="f-f-label" id="uppy_${field.name}">
                                    ${label} <small>${requiredText}</small>
                                </label>
                          </div>
                
                          <input
                                 type="hidden"
                                 id="${field.name}"
                                 name="${field.name}"
                                 data-parsley-validate-if-empty="true"
                                 data-parsley-excluded="false"
                                 data-parsley-errors-container="#${field.name}-error-container">
                        </div>
                
                      </div>
                  <div id="${field.name}-error-container" class="f-field"></div>
                  <div class="c-ci-image" data-js="thumbnail"></div>
            </div>`
    }

    /**
     * Build the Appointment Request section (3 date/time options)
     */
    buildAppointmentRequestSection() {
        const appt = this.form_properties.fields.appointment_request;
        if (!appt || !appt.is_enabled) return {html: '', script: ''};
        const requiredText = appt.is_required ? '' : '(Optional)';

        let html = `
            <hr class="form-section-hr">
            <div class="appointment-request-section" data-js="appointment-request-section">
              <div class="f-section-title">${appt.label} <label class="optional">${requiredText}</label></div>
              ${appt.instruction ? `<p class="instruction">${appt.instruction}</p>` : ''}
          `;

        for (let i = 1; i <= 3; i++) {
            html += `
              <div class="appointment-option f-field" data-js="appointment-request-option-${i}">
                <label class="f-field-label" for="appointment_request_date_${i}">Option ${i}</label>
                <div class="option-slot">
                    <div class="form-group">
                      <input
                        id="appointment_request_date_${i}"
                        name="appointment_request_date_${i}"
                        type="date"
                        class="js-date-input"
                        data-js="appointment_request_date_${i}"
                        autocomplete="off"
                        ${appt.is_required ? 'required' : ''}
                      />
                    </div>            
                    <div class="time-slots">
                      <label class="time-slot active">
                        <input type="radio" name="appointment_request_time_${i}" value="8am-12pm">
                        8am – 12pm
                      </label>
                      <label class="time-slot">
                        <input type="radio" name="appointment_request_time_${i}" value="12pm-4pm">
                        12pm – 4pm
                      </label>
                      <label class="time-slot">
                        <input type="radio" name="appointment_request_time_${i}" value="4pm-6pm">
                        4pm – 6pm
                      </label>
                    </div>
                </div>
              </div>
            `;
        }

        html += `</div><hr class="form-section-hr"/>`;
        const script = `<script>
            (function(){
              document.querySelectorAll('.appointment-option').forEach(opt => {
                const slots = opt.querySelectorAll('.time-slot');
                slots.forEach(label => {
                  const radio = label.querySelector('input[type="radio"]');
                  if (radio.value === '8am-12pm') {
                    radio.checked = true;
                    label.classList.add('active');
                  }
                  radio.addEventListener('change', () => {
                    if (!radio.checked) return;
                    slots.forEach(l => l.classList.remove('active'));
                    label.classList.add('active');
                  });
                });
              });
            })();
          </script>`

        return {html, script};
    }

    /**
     * Build the submit button HTML
     * @returns {string} - Raw HTML for the submit button
     */
    buildSubmitButton() {
        const isDisabled = this.isMock ? 'disabled' : '';
        const {save_button_label} = this.form_properties;

        return `<div class="form-actions">
            <div class="feedback-container" data-js="feedback-container"></div>
            <div class="wrapper-actions">
                <button type="submit" data-js="save_button" class="btn btn-primary b-text t-primary submit-button" ${isDisabled}>${save_button_label}</button>
                <input type="hidden" name="recaptcha_token" id="recaptcha_token">
            </div>
        </div>`;
    }


    /**
     * Create an input field with validation
     * @param {Object} field - Field properties
     * @param {boolean} isRequired - Whether the field is required
     * @returns {string} - Raw HTML for the input field
     */
    createInput(field, isRequired) {
        const {label, name} = field;
        const requiredText = isRequired ? '' : '(Optional)';
        let pattern = '';
        let title = '';
        let inputType = 'text';

        switch (name) {
            case 'phone':
                inputType = 'tel';
                pattern = '^\\(\\d{3}\\)\\s\\d{3}-\\d{4}';
                title = 'Please enter a valid phone number: (XXX) XXX-XXXX';
                break;
            case 'postal_code':
                pattern = '\\d{5,9}';
                title = 'Please enter a valid postal code (5-9 digits)';
                break;
            case 'city':
                pattern = '[A-Za-z\\s]+';
                title = 'Please enter a valid city name (letters only)';
                break;
            default:
                pattern = '';
                title = '';
                break;
        }

        return `<div class="form-group f-field">
                <label class="f-f-label" for="${name}">${label} <small>${requiredText}</small></label>
                <input type="${inputType}" name="${name}" data-js="${name}" id="${name}" class="form-control f-f-input i-overflow" 
                    ${pattern ? `pattern="${pattern}"` : ''} 
                    ${title ? `title="${title}"` : ''} 
                    ${isRequired ? 'required' : ''}>
            </div>`;
    }

    /**
     * Create a checkbox field
     *
     * @param field
     * @param isRequired
     * @returns {string}
     */
    createCheckbox(field, isRequired) {
        const {label, name} = field;
        const requiredText = isRequired ? '' : '(Optional)';
        return `<div class="form-group-email f-field">
                    <input type="checkbox" checked
                    data-parsley-errors-container="#${name}-error-container"
                    name="${name}" data-js="${name}" id="${name}" class="form-control f-f-input" ${isRequired ? 'required' : ''}>
                    <label class="f-f-label" for="${name}">${label} <small>${requiredText}</small></label>
            </div>`;
    }

    /**
     * Create a select field
     * @param {object} field - Field properties
     * @param {boolean} isRequired - Whether the field is required
     * @param {Object[]} optionGroups - Array of option groups (label and options)
     * @returns {string} - Raw HTML for the select field
     */
    createSelect(field, isRequired, optionGroups) {
        const {label, name} = field;
        const requiredText = isRequired ? '' : '(Optional)';

        const selectOptions = optionGroups.map(group => {
            const groupOptions = Object.entries(group.options)
                .map(([key, value]) => `<option value="${key}">${value}</option>`)
                .join('');
            return `<optgroup label="${group.label}">${groupOptions}</optgroup>`;
        }).join('');

        return `<div class="form-group f-field">
                <label class="f-f-label" for="${name}">${label} <small>${requiredText}</small></label>
                <select name="${name}" data-js="${name}" id="${name}" placeholder="-- Select One --" class="form-control f-f-input" ${isRequired ? 'required' : ''}>
                    ${selectOptions}
                </select>
            </div>`;
    }

    /**
     * Create a textarea field
     * @param {object} field - Field properties
     * @param {boolean} isRequired - Whether the field is required
     * @returns {string} - Raw HTML for the textarea field
     */
    createTextarea(field, isRequired) {
        const {label, name} = field;
        const requiredText = isRequired ? '' : '(Optional)';
        return `<div class="form-group f-field">
                <label class="f-f-label" for="${name}">${label} <small>${requiredText}</small></label>
                <textarea name="${name}" data-js="${name}" id="${name}" class="form-control f-f-input" ${isRequired ? 'required' : ''}></textarea>
            </div>`;
    }
}

module.exports = LeadFormBuilder;

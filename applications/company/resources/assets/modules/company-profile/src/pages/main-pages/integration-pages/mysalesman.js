'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");

const mysalesman_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/mysalesman.hbs');

class MySalesman extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
        });
    };


    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            details: {
                path: '/details',
                page: require('./mysalesman-pages/details'),
            },
            settings: {
                path: '/settings',
                page: require('./mysalesman-pages/settings'),
            }
        };
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.page_container = findChild(root, jsSelector('mysalesman-pages'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return mysalesman_tpl()
    };
}

module.exports = MySalesman;
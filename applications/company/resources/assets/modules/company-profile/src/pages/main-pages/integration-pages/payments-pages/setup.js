'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");

const setup_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/payments-pages/setup.hbs');
const FormInput = require('@ca-submodule/form-input');
const FormValidator = require('@ca-submodule/validator');
const PasswordInput = require("@cas-form-input-js/password");

const UI = require('./utilities/ui-enhancer');
const PayloadBuilder = require('./utilities/payload-builder');
const TestingUtils = require('./utilities/_testing');

FormInput.use(PasswordInput);
FormInput.use(require('@ca-submodule/form-input/src/date'));


class Setup extends Page {

    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            integrations: parent.getParentByName('integrations'),
            validator: null,
        });
    }


    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);

        if (profile_data?.merchant_data && Object.keys(profile_data.merchant_data).length > 0) {
            // Merchant data exists, redirect to details page
            this.router.navigate('integrations.payments.details');
            return;
        }
    }


    initializeElements(root) {
        // Navigation buttons
        this.elem.go_back_button = findChild(root, jsSelector('go-back-button'));
        this.elem.submit_setup_button = findChild(root, jsSelector('submit-setup-button'));
        this.elem.fill_test_data_button = findChild(root, jsSelector('fill-test-data-button'));
        this.elem.setup_form = findChild(root, jsSelector('setup-form'));
        this.elem.loader = findChild(root, jsSelector('loader'));

        // Error callout element
        this.elem.error = findChild(root, jsSelector('error'));

        // Date inputs
        this.elem.business_start_date = findChild(root, jsSelector('business-start-date'));
        this.elem.owner_dob = findChild(root, jsSelector('owner-dob'));

        // Sensitive data inputs
        this.elem.owner_ssn = findChild(root, jsSelector('owner-ssn'));
        this.elem.deposit_account_number = findChild(root, jsSelector('deposit-account-number'));
        this.elem.deposit_routing_number = findChild(root, jsSelector('deposit-routing-number'));
        this.elem.withdrawal_account_number = findChild(root, jsSelector('withdrawal-account-number'));
        this.elem.withdrawal_routing_number = findChild(root, jsSelector('withdrawal-routing-number'));

        // Tax filing elements
        this.elem.tax_filing_method = findChild(root, jsSelector('tax-filing-method'));
        this.elem.tax_id = findChild(root, jsSelector('tax-id'));

        // State dropdowns
        this.elem.business_state = findChild(root, jsSelector('business-state'));
        this.elem.mailing_state = findChild(root, jsSelector('mailing-state'));
        this.elem.owner_state = findChild(root, jsSelector('owner-state'));
        this.elem.drivers_license_state = findChild(root, jsSelector('drivers-license-state'));
        
        // Mailing address elements
        this.elem.add_mailing_address_button = findChild(root, jsSelector('add-mailing-address'));
        this.elem.mailing_address_group = findChild(root, jsSelector('mailing-address-group'));
        this.elem.remove_mailing_address_button = findChild(root, jsSelector('remove-mailing-address'));

        // Additional owners elements
        this.elem.add_additional_owner_button = findChild(root, jsSelector('add-additional-owner'));
        this.elem.additional_owners_group = findChild(root, jsSelector('additional-owners-group'));
        this.elem.additional_owners_container = findChild(root, jsSelector('additional-owners-container'));
        this.elem.add_additional_owner_entry_button = findChild(root, jsSelector('add-additional-owner-entry'));

        // Initialize additional owners state
        this.state.additional_owners_count = 0;
    }

    setupListeners() {
        this.elem.setup_form?.on('submit', (e) => {
            e.preventDefault();
            if (this.state.validator && !this.state.validator.hasErrors()) {
                const data = PayloadBuilder.getSetupPayload(this.elem, this.state);
                PayloadBuilder.submitApplication(this.elem, this.router, data);
            }
        });

        // Clear errors when user starts typing/changing inputs
        this.elem.setup_form?.on('input change', 'input, select, textarea', () => {
            PayloadBuilder.hideErrorCallout(this.elem.error);
        });

        this.elem.go_back_button?.on('click', (e) => {
            e.preventDefault();
            this.router.navigate('integrations.payments.details');
            return false;
        });

        this.elem.submit_setup_button?.on('click', (e) => {
            e.preventDefault();
            this.elem.setup_form?.trigger('submit');
            return false;
        });

        this.elem.fill_test_data_button?.on('click', (e) => {
            e.preventDefault();
            TestingUtils.fillTestData(this.elem, UI);
            return false;
        });

        this.elem.add_mailing_address_button?.on('click', (e) => {
            e.preventDefault();
            UI.showMailingSection(this.elem);
            UI.reinitializeValidator(this.elem, this.state);
            return false;
        });

        this.elem.remove_mailing_address_button?.on('click', (e) => {
            e.preventDefault();
            UI.hideMailingSection(this.elem);
            UI.reinitializeValidator(this.elem, this.state);
            return false;
        });

        this.elem.add_additional_owner_button?.on('click', (e) => {
            e.preventDefault();
            UI.showAdditionalOwnersSection(this.elem, this.state);
            UI.reinitializeValidator(this.elem, this.state);
            return false;
        });

        this.elem.add_additional_owner_entry_button?.on('click', (e) => {
            e.preventDefault();
            UI.addAdditionalOwnerEntry(this.elem, this.state);
            UI.reinitializeValidator(this.elem, this.state);
            return false;
        });
    }

    initializeSetupForm(root) {
        const $form = this.elem.setup_form;
        if (!$form?.length) return;

        const dropdowns = [this.elem.business_state, this.elem.mailing_state, this.elem.owner_state, this.elem.drivers_license_state];
        UI.populateStateDropdowns(dropdowns);
        UI.initializeFormInputs($form, this.elem, FormInput);
        UI.setupFormBehaviors($form);
        
        this.state.validator = FormValidator.create($form, UI.getValidationRules());
        UI.setupOwnershipTypeHandler($form);
        UI.setupTaxFilingMethodHandler($form, this.state.validator);
    }

    boot(root) {
        super.boot(root);
        
        this.initializeElements(root);
        this.setupListeners(root);
        this.initializeSetupForm(root);
        TestingUtils.setupTestButton(this.elem);
    }

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // Clean up SSN mask observer
        if (this.state.ssnObserver) {
            this.state.ssnObserver.disconnect();
            this.state.ssnObserver = null;
        }

        await super.unload(request, next);
    }


    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return setup_tpl();
    }
}

module.exports = Setup;
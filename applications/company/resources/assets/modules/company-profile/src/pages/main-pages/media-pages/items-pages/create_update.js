'use strict';

const Uppy = require('@uppy/core');
const ThumbnailGenerator = require("@uppy/thumbnail-generator");

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent, onClick} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/hidden_textarea'));
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const Tooltip = require('@ca-submodule/tooltip');

const FormValidator = require("@cas-validator-js");

const { file_types, file_types_name, mime_types, MAX_UPLOAD_SIZE_200MB } = require('./constants');

const create_update_tpl = require('@cam-company-profile-tpl/pages/main-pages/media-pages/items-pages/create_update.hbs');

class CreateUpdate extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            media: parent.getParentByName('media'),
            is_update: true,
            uppy_file: null
        });
    };


    /**
     * Fetch user info by id
     *
     * @param {string} id - UUID
     * @returns {Promise<void>}
     */
    async fetchData(id) {
        try {
            let file_relations = {
                file_media_urls: {},
                file: {
                    fields: ['content_type']
                }
            };
            let { data: entity } = await Api.Resources.Media().relations(file_relations).retrieve(id);
            return entity;
        } catch (e) {
            let message = createErrorMessage('Unable to fetch media info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            console.log(e);
        }
    };

    /**
     * Get display type icon and text for table row display
     *
     * @param {string} mime_type
     */
    getDisplayType(mime_type) {
        const this_type = mime_types[mime_type] || 10; // '10' is empty.

        const key = file_types[this_type];
        const name = file_types_name[this_type];

        return `<span class="media-icon icon-file-${key}"></span> ${name}`;
    };

    /**
     * Populate data into template
     *
     * @param {object} data
     */
    populate(data) {
        this.state.validator.getInputElem('name').val(data.name);

        if (data.description) {
            this.state.description.toggle();
            this.state.validator.getInputElem('description').val(data.description).trigger('change');
        }

        this.elem.file_type.html(this.getDisplayType(data.file.content_type));
        this.elem.is_bid_media.attr('checked', data.is_bid_media);
        this.elem.is_bid_default.attr('checked', data.is_bid_default);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        // show parent loader
        this.state.media.showLoader();
        let data = await this.fetchData(request.params.media_id);
        this.state.media_data = data;
        this.populate(data);

        this.state.media.hideLoader();
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.clearError();
        this.state.validator.reset();
        this.elem.root.scrollTop(0);

        if (this.state.description.opened) {
            this.state.description.toggle();
        }

        this.elem.file_type.empty();

        this.deleteImage();
        await super.unload(request, next);
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            name: {
                required: true,
                maxlength: 200,
                maxlengthMessage: 'Invalid length - 200 chars. max'
            },
            description: {
                required: false,
                maxlength: 5000,
                maxlengthMessage: 'Invalid length - 5000 chars. max'
            },
            is_bid_media: {},
            is_bid_default: {}
        },
        {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {})
            .on('error', () => this.setError('Please review form errors below'));

    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Build entity
     * @returns {{name: string, description: string}}
     */
    buildEntity() {
        let entity = {
            id: this.state.media_data.id,
            name: this.state.validator.getInputElem('name').val()
        }
        if (this.state.description.opened) {
            entity['description'] = this.state.validator.getInputElem('description').val();
        } else {
            entity['description'] = null;
        }
        entity['is_bid_media'] = this.elem.is_bid_media.is(':checked') ?? false;
        entity['is_bid_default'] = this.elem.is_bid_default.is(':checked') ?? false;
        return entity;
    }

    /**
     * Save media
     */
    async save() {
        this.state.media.showLoader();

        try {
            let mediaID = this.state.media_data.id,
            data = this.buildEntity();

            // Send media info
            await Api.Resources.Media().partialUpdate(mediaID, data);

            // Send media file
            if (this.state.uppy_file) {
                await Api.Resources.Media()
                    .file(this.state.uppy_file.data)
                    .method(Api.Request.Method.PATCH)
                    .custom(mediaID);
            }

            // Success handling
            let message = createSuccessMessage(`Media item edited successfully`);
            this.router.main_route.layout.toasts.addMessage(message);
            this.router.navigate('media.items.manager');

        } catch (error) {
            // Error handling
            console.error('Error during update:', error);

            if (error.response && error.response.statusCode() === 422) {
                let item_errors = error.response.data().errors;
                for (let item in item_errors) {
                    this.setError(item_errors[item]);
                }
            } else {
                let message = createErrorMessage('Unable to update media item, please contact support');
                this.router.main_route.layout.toasts.addMessage(message);
            }
        } finally {
            // Hide loader in both success and error cases
            this.state.media.hideLoader();
        }
    }

    /**
     * Configure Uppy
     *
     * @param {jQuery} root
     */
    configureUppy(root) {
        this.state.uppy = Uppy({
            id: 'media-image-upload',
            autoProceed: false,
            restrictions: {
                allowedFileTypes: Object.keys(mime_types),
                maxNumberOfFiles: 1,
                maxFileSize: MAX_UPLOAD_SIZE_200MB
            },
            locale: {
                strings: {
                    youCanOnlyUploadFileTypes: 'This file type is not allowed'
                },
            }

        }).use(ThumbnailGenerator, {
            thumbnailWidth: 100,
            thumbnailHeight: 100,
            thumbnailType: 'image/jpeg',
            waitForThumbnailsBeforeUpload: true,
        }).on('file-added', (file) => {
            this.state.uppy_file = file;
            this.addImage(file);
        }).on('thumbnail:generated', (file, preview) => {
            this.state.uppy_file = file;
            this.addImage(file);
        });
    }

    /**
     * Adds thumbnail image and sets click event for the delete image
     *
     * @param {object} file
     */
    addImage(file) {
        // Set filename and thumbnail image in the DOM
        this.elem.thumbnail_image.attr('src', '');
        this.elem.filename.text(file.name);

        if (file.preview) {
            this.elem.thumbnail_image.attr('src', file.preview);
        }
        this.elem.thumbnail_block.removeClass('t-hidden');
        this.elem.filename.removeClass('t-hidden');
    }

    /**
     * Removes the user-profile image thumbnail
     */
    deleteImage() {
        this.state.uppy_file = null;
        this.elem.thumbnail_image.attr('src', '');
        this.elem.filename.text('');
        this.elem.file.val(null);

        this.elem.thumbnail_block.addClass('t-hidden');
        this.elem.filename.addClass('t-hidden');
    };


    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        Tooltip.initAll(root);

        // Form
        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.cancel = findChild(root, jsSelector('cancel'));
        this.elem.preview = findChild(root, jsSelector('preview'));
        this.elem.error = findChild(root, jsSelector('error'));

        // Fields
        this.elem.name = findChild(root, jsSelector('name'));
        this.elem.file_type = findChild(root, jsSelector('file-type'));
        this.elem.description = findChild(root, jsSelector('description'));
        this.elem.is_bid_media = findChild(root, jsSelector('is_bid_media'));
        this.elem.is_bid_default = findChild(root, jsSelector('is_bid_default'));

        this.state.description = FormInput.init(this.elem.description, {
            label: 'Description'
        });

        FormInput.init(this.elem.is_bid_media);
        FormInput.init(this.elem.is_bid_default);

        // File management
        this.elem.upload_button = findChild(root, jsSelector('upload'));
        this.elem.file = findChild(root, jsSelector('file'));
        this.elem.filename = findChild(root, jsSelector('filename'));

        // image management
        this.elem.thumbnail_block = findChild(root, jsSelector('thumbnail-block'));
        this.elem.thumbnail_image = findChild(root, jsSelector('thumbnail-image'));
        this.elem.delete_image = findChild(root, jsSelector('delete-image'));

        // Configure uppy
        this.configureUppy(root);

        // Set up events
        this.setupEvents();
        this.initForm();
    };

    /**
     * Set up events
     *
     */
    setupEvents() {
        onClick(this.elem.upload_button, () => {
            this.elem.file.trigger('click');
            return false;
        });

        onClick(this.elem.delete_image, () => {
            this.deleteImage();
            return false;
        });

        onClick(this.elem.preview, () => {
            window.open(this.state.media_data.file_media_urls.original, '_blank');
            return false;
        });

        onEvent(this.elem.file, 'change', () => {
            try {
                this.clearError();
                let file = this.elem.file[0].files[0];
                this.state.uppy.reset();

                this.elem.thumbnail_image.empty();
                this.state.uppy.addFile({
                    source: 'Local',
                    name: file.name,
                    type: file.type,
                    data: file
                });

                this.elem.filename.text(file.name);

            } catch (err) {
                if (err.isRestriction) {
                    // handle restrictions
                    console.log('Restriction error:', err);
                    this.setError(err);
                } else {
                    // handle other errors
                    console.error(err)
                }
            }
        });

        onClick(this.elem.save, () => {
            this.elem.form.trigger('submit');
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return create_update_tpl({
            cancel_route: 'media.items.manager',
            title: `${this.state.is_update ? 'Edit' : 'Add'} Media Item`
        });
    };
}

module.exports = CreateUpdate;

'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");

const payments_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/payments.hbs');

class Payments extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
        });
    };


    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            details: {
                path: '/details',
                page: require('./payments-pages/details'),
            },
            setup: {
                path: '/setup',
                page: require('./payments-pages/setup'),
            },
            settings: {
                path: '/settings',
                page: require('./payments-pages/settings'),
            }
        };
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.page_container = findChild(root, jsSelector('payments-pages'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return payments_tpl()
    };
}

module.exports = Payments;
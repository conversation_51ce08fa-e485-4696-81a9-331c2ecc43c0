'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");
// const mySalesmanAPI = require('@cac-js/mysalesman').default;

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
FormInput.use(require('@ca-submodule/form-input/src/static_dropdown'));
const FormValidator = require('@ca-submodule/validator');
const Tooltip = require('@ca-submodule/tooltip');

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const settings_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/mysalesman-pages/settings.hbs');

class Settings extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            integrations: parent.getParentByName('integrations'),
            allow_mysalesman: profile_data.is_mysalesman_industry,
            feature_enabled: profile_data.features.mysalesman
        });
    };

    /**
     * Get status highlight html
     *
     * @returns {Map<boolean, string>}
     */
    get status () {
        return new Map([
            [false, '<span class="h-text t-grey">Disabled</span>'],
            [true, '<span class="h-text t-green">Enabled</span>']
        ]);
    };

    /**
     * Populate fields using data object
     */
    populate(data) {
        this.elem.integration_enabled.attr('checked', data.my_salesman_integration_enabled).trigger('change');
        this.elem.my_salesman_api_key.val(data.my_salesman_api_key);
        this.elem.default_assigned_to.val(data.my_salesman_default_assigned_to).trigger('change');
        this.elem.additional_email_recipients.val(data.leads_additional_email_recipients.join(', '));
        this.elem.capture_dropoffs.attr('checked', data.my_salesman_capture_dropoffs);
        this.elem.send_welcome_email.attr('checked', data.my_salesman_send_notifications);
    };

    /**
     * Prepare entity to save
     *
     * @returns {object}
     */
    buildEntity() {
        let data = {
            my_salesman_integration_enabled: this.elem.integration_enabled.is(':checked') ?? false,
            my_salesman_api_key: this.elem.my_salesman_api_key.val(),
            my_salesman_default_assigned_to: parseInt(this.elem.default_assigned_to.val()),
            my_salesman_capture_dropoffs: this.elem.capture_dropoffs.is(':checked') ?? false,
            my_salesman_send_notifications: this.elem.send_welcome_email.is(':checked') ?? false
        };

        let leads_additional_email_recipients = this.elem.additional_email_recipients.val();
        if (leads_additional_email_recipients === '') {
            data.leads_additional_email_recipients = [];
        } else {
            leads_additional_email_recipients = leads_additional_email_recipients.replace(/\s+/g, '');
            if (leads_additional_email_recipients[leads_additional_email_recipients.length - 1] === ',') {
                leads_additional_email_recipients = leads_additional_email_recipients.slice(0, -1);
            }
            data.leads_additional_email_recipients = leads_additional_email_recipients.split(',').filter(function (value, index, self) {
                return self.indexOf(value) === index;
            });
        }

        return {
            settings: data
        };
    };

    /**
     * Save mySalesman settings
     */
    save() {
        this.clearError();
        this.state.integrations.showLoader();

        Api.Resources.Companies().partialUpdate('current', this.buildEntity()).then(({data}) => {
            setTimeout(() => {
                this.state.integrations.hideLoader();
                let message = createSuccessMessage(`mySalesman settings saved successfully`);
                this.router.main_route.layout.toasts.addMessage(message);
                this.router.navigate('integrations.mysalesman.details');
            }, 2000);
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    this.state.integrations.hideLoader();
                    break;
                default:
                    this.state.integrations.hideLoader();
                    let message = createErrorMessage('Unable to save mySalesman settings, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Init User Dropdown
     */
    async initUserDropdown(users) {
        const input = FormInput.init(this.elem.default_assigned_to, {
            data_provider : () => users.map(user =>
                ({
                    id: user.id,
                    text: `${user.first_name} ${user.last_name}`
                })
            ),
            placeholder   : '-- Select One --',
            closeOnSelect : true
        });
        await input.promise;
    };

    /**
     * Get list of users from API
     */
    async getUsers() {
        const { entities } = await Api.Resources.Users()
            .fields(['id','first_name','last_name'])
            .filter('is_active', true)
            .filter('is_user_invited', false)
            .sort('created_at', 'asc')
            .all();
        return entities.map(u => u.data);
    }

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: company_entity} = await Api.Resources.Companies().
            fields(['id']).relations({
                'settings': {}
            }).retrieve('current');

            let data = {
                my_salesman_integration_enabled: company_entity.settings.my_salesman_integration_enabled ?? false,
                my_salesman_api_key: company_entity.settings.my_salesman_api_key ?? '',
                my_salesman_default_assigned_to: company_entity.settings.my_salesman_default_assigned_to ?? null,
                leads_additional_email_recipients: company_entity.settings.leads_additional_email_recipients ?? [],
                my_salesman_capture_dropoffs: company_entity.settings.my_salesman_capture_dropoffs ?? false,
                my_salesman_send_notifications: company_entity.settings.my_salesman_send_notifications ?? false
            };

            let users = await this.getUsers();
            await this.initUserDropdown(users);

            this.populate(data);
        } catch (e) {}
    };

     /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.elem.cancel.prop('disabled', false);
        this.state.validator.reset();
        this.elem.root.scrollTop(0);
        this.clearError();

        this.state.integrations.setEditMode(false);
        await super.unload(request, next);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.integrations.showLoader();

        if (!this.state.feature_enabled) {
            this.router.navigate('integrations.quickbooks.details');
        }

        this.state.integrations.setEditMode(true);
        await this.fetchData();
        this.state.integrations.hideLoader();
        await super.load(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            integration_enabled: {},
            my_salesman_api_key: {
                required: true
            },
            default_assigned_to: {},
            additional_email_recipients: {},
            capture_dropoffs: {},
            send_welcome_email: {}
        }, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {
                this.clearError();
            })
            .on('error', () => this.setError('Please review form errors below'));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.cancel = findChild(root, jsSelector('cancel'));
        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.error = findChild(root, jsSelector('error'));

        this.elem.integration_enabled = findChild(root, jsSelector('status'));
        this.elem.status_cell = findChild(root, jsSelector('status-cell'));
        this.elem.my_salesman_api_key = findChild(root, jsSelector('my_salesman_api_key'));
        this.elem.default_assigned_to = findChild(root, jsSelector('default_assigned_to'));
        this.elem.additional_email_recipients = findChild(root, jsSelector('additional_email_recipients'));
        this.elem.capture_dropoffs = findChild(root, jsSelector('capture_dropoffs'));
        this.elem.send_welcome_email = findChild(root, jsSelector('send_welcome_email'));

        FormInput.init(this.elem.integration_enabled);
        FormInput.init(this.elem.capture_dropoffs);
        FormInput.init(this.elem.send_welcome_email);

        this.initForm();

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });

        onEvent(this.elem.integration_enabled, 'change', (e) => {
            this.elem.status_cell.html(this.status.get(this.elem.integration_enabled.is(':checked')));
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return settings_tpl({
            cancel_route: 'integrations.mysalesman.details'
        });
    };
}

module.exports = Settings;
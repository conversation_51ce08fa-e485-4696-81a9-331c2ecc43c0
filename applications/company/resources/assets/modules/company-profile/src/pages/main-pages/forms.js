'use strict';

const Page = require('@ca-package/router/src/page');

const forms_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms.hbs');

class Forms extends Page {
    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            items: {
                default: true,
                page: require('./forms-pages/items')
            },
            categories: {
                path: '/categories',
                page: require('./forms-pages/categories')
            }
        };
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Get container element to hold sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.loader = root.fxFind('loader');
        this.elem.page_container = root.fxFind('page-container');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return forms_tpl();
    };
}

module.exports = Forms;

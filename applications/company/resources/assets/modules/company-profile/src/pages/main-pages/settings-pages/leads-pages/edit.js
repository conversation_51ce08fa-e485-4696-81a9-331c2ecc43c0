'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const Tooltip = require('@ca-submodule/tooltip');
const FormValidator = require("@cas-validator-js");

const lead_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/leads-pages/edit.hbs');
const {FIELD_NAMES, TYPES, MEDIUM_LENGTH, SMALL_LENGTH, MAX_LENGTH} = require("./utilities/constants");
const LeadFormBuilder = require("./utilities/lead-form-builder");
const LeadFormHelper = require("./utilities/lead-form-helper");
const GenerateApiKeyModal = require('@cam-company-profile-js/modals/leads/generate-api-key');
const DeleteApiKeyModal = require('@cam-company-profile-js/modals/leads/delete-api-key');
const lead_api = require('./utilities/api');

class Edit extends Page {

    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings'),
            modals: {},
            leads_form_fields: {},
            ignore_api_key_change: false,
            previous_api_key_state: null,
        });
        this.helper = null;
    };

    refreshForm() {
        this.refreshValidation();
        const entity = this.helper.buildEntity(
            this.state.leads_form_id,
            this.state.validator,
            this.elem.lead_form_status,
            this.elem.lead_form_settings
        );

        const lead_form_builder = new LeadFormBuilder({
            form_props: entity,
            marketing_types: this.state.marketing_types,
            project_types: this.state.project_types,
            isMock: true,
        });

        lead_form_builder.buildForm().then(async (lead) => {
            this.elem.form_preview_container.html(lead);
            await this.helper.initializeFormComponents();
        })
    }

    refreshValidation() {
        const validationRules = {
            title_label: {
                required: true,
                maxlength: MAX_LENGTH,
            },
            save_button_label: {
                required: true,
                maxlength: SMALL_LENGTH,
            },
            default_assigned_to: {
                required: false,
            },
            google_tag_id: {
                required: false,
                maxlength: SMALL_LENGTH,
            },
            additional_email_recipients: {
                required: false,
                maxlength: MAX_LENGTH,
            },
            email_checkbox_label: {
                required: false,
                maxlength: MEDIUM_LENGTH,
            },
            upload_file_label: {
                required: false,
                maxlength: SMALL_LENGTH,
            },
            appointment_request_label: {
                required: true,
                maxlength: MEDIUM_LENGTH,
            },
            appointment_request_instruction_label: {
                required: false,
                maxlength: MAX_LENGTH,
            },
            is_send_notifications: {
                required: false
            }
        };


        // Adjust validation for inputs that have changeable labels.
        if (this.elem.lead_form_settings.is(':visible')) {
            const dynamic_fields = [
                { label: FIELD_NAMES.MARKETING_SOURCE, size: MEDIUM_LENGTH },
                { label: FIELD_NAMES.PROJECT_TYPE, size: MEDIUM_LENGTH },
                { label: FIELD_NAMES.CUSTOMER_NOTES, size: MAX_LENGTH },
                { label: FIELD_NAMES.EMAIL_CHECKBOX, size: MEDIUM_LENGTH },
                { label: FIELD_NAMES.UPLOAD_FILE, size: SMALL_LENGTH },
            ]

            dynamic_fields.forEach((field) => {
                const element = findChild(this.elem.lead_form_settings, jsSelector(`${field.label}_visibility`));

                // If field is visible, make its label required with a max length validation
                if (element.is(':checked')) {
                    validationRules[field.label + '_label'] = { required: true, maxlength: field.size };
                }
            });
        }

        // Create the validator dynamically using the constructed rules
        this.state.validator = FormValidator.create(this.elem.form, validationRules, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('error', () => {});

    }

    async activateLeadForm() {
        try {
            this.state.settings.showLoader();
            await Api.Resources.LeadFormsAction()
                .accept('application/vnd.adg.fx.form-v1+json')
                .method(Api.Request.Method.POST)
                .custom('activate')

            let message = createSuccessMessage(`Lead form enabled successfully`);
            this.router.main_route.layout.toasts.addMessage(message);
        } catch (error) {
            console.log(error);
            let message = createErrorMessage('Unable to save leads form settings, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
        }

        this.state.settings.hideLoader();
    }

    /**
     * Handles toggle of the API-Key on/off switch,
     * safely reverting on failure.
     */
    async handleApiKeySwitchChange(e) {
        if (this.state.ignore_api_key_change) {
            this.state.ignore_api_key_change = false;
            return;
        }

        const checkbox = this.elem.api_key_status;
        const new_state = checkbox.is(':checked');
        const old_state = this.state.previous_api_key_state ?? !new_state;
        this.state.previous_api_key_state = new_state;


        try {
            const success = new_state
                ? await this.generateLeadAPIKey()
                : await this.deleteLeadAPIKey();

            if (success === null) {
                throw new Error('Operation failed');
            }
            else if (success === false) {
                checkbox.prop('checked', old_state);
                return;
            }

            // on success, update the feature-status and preview visibility
            this.elem.api_key_feature_status.toggleClass('t-hidden', !new_state);
            this.elem.api_key_code.toggleClass('t-hidden', !new_state);
        } catch (err) {
            this.state.ignore_api_key_change = true;
            checkbox.prop('checked', old_state);

            let msg = createErrorMessage('Could not update API key status. Please try again.');
            this.router.main_route.layout.toasts.addMessage(msg);
        }
    }


    async generateLeadAPIKey() {
        try {
            this.state.settings.showLoader();
            const data = await Api.Resources.LeadFormsAction()
                .method(Api.Request.Method.POST)
                .custom('api-key')

            if (!data?.api_key) {
                throw new Error('No API key returned');
            }

            this.elem.api_key_feature_status.html(`<span class='h-text t-green'>Active</span>`);
            this.elem.api_key_code
                .removeClass('t-hidden')
                .html(data.api_key);

            this.state.modals.generate_api_key = new GenerateApiKeyModal(this);
            this.state.modals.generate_api_key.open({
                toast: this.router.main_route.layout.toasts,
                api_key: data.api_key
            });

            return true;
        } catch (error) {
            console.log(error);
            let message = createErrorMessage('Unable to save leads form settings, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
            return false;
        } finally {
            this.state.settings.hideLoader();
        }
    }

    async deleteLeadAPIKey() {
        this.state.modals.delete_api_key = new DeleteApiKeyModal(this);
        const result = await new Promise((resolve, reject) => {
            this.state.modals.delete_api_key.open({ promise: { resolve, reject } });
        });

        if (result !== true) return result;

        this.elem.api_key_feature_status.html(`<span class='h-text t-grey'>Not Setup</span>`);
        this.elem.api_key_code.addClass('t-hidden').html('')
        return true;
    }


    populate(data) {
        this.state.marketing_types = data.marketing_types;
        this.state.project_types = data.project_types;
        this.state.leads_form_id = data.id;
        this.state.users = data.users;

        this.elem.api_key_status.prop('checked', !!data.api_key);
        if (data.api_key) {
            this.elem.api_key_feature_status.html(`<span class='h-text t-green'>Active</span>`);
            this.elem.api_key_code.removeClass('t-hidden').html(data.api_key);
        }

        this.elem.lead_form_status.prop('checked', data.is_active)
        this.elem.lead_form_status.trigger('change')

        // populate fields.
        if (!Array.isArray(data.fields) || data.fields.length === 0) {
            console.error('Fields data is missing or empty');
            return;
        }

        // Transform fields array to object
        this.state.leads_form_fields = data.fields.reduce((acc, field) => {
            acc[field.reference] = field;
            return acc;
        }, {});

        const input_options_section = Object.values(FIELD_NAMES);
        const input_type_section = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE]
        const label_options_section = [
            FIELD_NAMES.MARKETING_SOURCE,
            FIELD_NAMES.PROJECT_TYPE,
            FIELD_NAMES.CUSTOMER_NOTES,
            FIELD_NAMES.EMAIL_CHECKBOX,
            FIELD_NAMES.UPLOAD_FILE,
            FIELD_NAMES.APPOINTMENT_REQUEST]

        Object.keys(this.state.leads_form_fields).forEach((item) => {
            const field = this.state.leads_form_fields[item]
            const reference = field.reference;

            input_options_section.forEach((input) => {
                if (reference === input) {
                    const visibility_elem = findChild(this.elem.lead_form_settings, jsSelector(`${input}_visibility`))
                    const requirement_elem = findChild(this.elem.lead_form_settings, jsSelector(`${input}_requirement`))

                    requirement_elem.prop('checked', field.is_required);
                    requirement_elem.trigger('change')
                    visibility_elem.prop('checked', field.is_enabled);
                    visibility_elem.trigger('change')
                 }
            })

            input_type_section.forEach((input) => {
                if (reference === input) {
                    const dropdown_radio = findChild(this.elem.lead_form_settings, jsSelector(`${input}_dropdown`))
                    const freeform_radio = findChild(this.elem.lead_form_settings, jsSelector(`${input}_freeform`))

                    if (field.field_type === TYPES.DROPDOWN) {
                        dropdown_radio.prop('checked', true)
                    } else if (field.field_type === TYPES.FREEFORM) {
                        freeform_radio.prop('checked', true)
                    }
                }
            });

            label_options_section.forEach((input) => {
                if (reference === input) {
                    const label_elem = findChild(this.elem.lead_form_settings, jsSelector(`${input}_label`))
                    label_elem.val(field.label)

                    if (field.instruction) {
                        const instruction_label_elem = findChild(this.elem.lead_form_settings, jsSelector(`${input}_instruction_label`))
                        instruction_label_elem.val(field.instruction)
                    }
                }
            });
        });

        this.elem.title.val(data.title);
        this.elem.save_button_label.val(data.save_button_label);

        this.helper.setupAssignedToField(this.state.users).then(() => {
            if (data.default_assigned_to_user_id !== null) {
                this.elem.default_assigned_to.val(data.default_assigned_to_user_id).trigger('change');
            } else {
                this.elem.default_assigned_to.val(this.state.users[0].id).trigger('change');
            }
        });

        if (data.google_tag_id) {
            this.elem.google_tag_id.val(data.google_tag_id);
        }

        if (data.additional_email_recipients) {
            try {
                data.additional_email_recipients =
                    data.additional_email_recipients
                        .map(email => email.trim().toLowerCase())
                        .join(', ');
            } catch (e) {
                console.error('Failed to parse additional_email_recipients:', e);
                data.additional_email_recipients = [];
            }
            this.elem.additional_email_recipients.val(data.additional_email_recipients);
            this.elem.additional_email_recipients.trigger('change');
        }

        if (data.is_send_notifications) {
            this.elem.is_send_notifications.attr('checked', data.is_send_notifications).trigger('change');
        }

        // Refresh form for the first time.
        this.refreshForm();
    }

    async save() {
        if (!this.helper.validateFields()) {
            return;
        }

        const payload = this.helper.buildEntity(
            this.state.leads_form_id,
            this.state.validator,
            this.elem.lead_form_status,
            this.elem.lead_form_settings
        );

        payload['is_send_notifications'] = this.elem.is_send_notifications.is(':checked');

        const raw = this.elem.additional_email_recipients.val();
        payload['additional_email_recipients'] = raw
            ? raw
                .split(',')
                .map(email => email.trim().toLowerCase())
                .filter(email => email.length > 0)
            : [];

        try {
            this.state.settings.showLoader();
            const data = await Api.Resources.LeadForms()
                .accept('application/vnd.adg.fx.form-v1+json')
                .partialUpdate(this.state.leads_form_id, payload)

            if (data) {
                setTimeout(() => {
                    this.state.settings.hideLoader();
                    let message = createSuccessMessage(`Lead form settings saved successfully`);
                    this.router.main_route.layout.toasts.addMessage(message);
                    this.router.navigate('settings.leads.details');
                }, 2000);
            }
        } catch (error) {
            console.log(error);
            this.state.settings.hideLoader();
            let message = createErrorMessage('Unable to save leads form settings, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
        }
    }

    async load(request, next) {
        await super.load(request, next);
        await super.load(request, next);
        this.state.settings.setEditMode(true);
        this.state.settings.showLoader();

        try {
            const users = await lead_api.getUsers();
            const data = await lead_api.fetchLeadFormSettings();

            if (Object.keys(data).length > 0) {
                data.users = users ?? []
                await this.populate(data)
                return;
            }

            await this.activateLeadForm();
            const activated_data = await lead_api.fetchLeadFormSettings();
            activated_data.users = users ?? []

            await this.populate(activated_data)
        } catch(e) {
            console.log(e)
            let message = createErrorMessage('Unable to fetch leads form settings, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
        }
        finally {
            this.state.settings.hideLoader();
        }

    }

    async unload(request, next) {
        if (this.state.validator) {
            this.state.validator.reset();
        }
        if (this.elem.form && this.elem.form.length) {
            this.elem.form[0].reset();
        }

        this.elem.default_assigned_to.val('').trigger('change');
        this.elem.google_tag_id.val('');
        this.elem.additional_email_recipients.val('');

        this.elem.root.scrollTop(0);
        this.state.settings.setEditMode(false);
        await super.unload(request, next);
    }

    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.save = findChild(root, jsSelector('save'));

        // Leads website form status and form settings
        this.elem.lead_form_status = findChild(root, jsSelector('lead-form-status'));
        this.elem.lead_form_settings = findChild(root, jsSelector('lead-form-settings'));
        this.elem.api_key_status = findChild(root, jsSelector('api-key-status'));
        this.elem.api_key_feature_status = findChild(root, jsSelector('api-key-feature-status'));
        this.elem.api_key_code = findChild(root, jsSelector('api-key-code'));

        this.elem.is_send_notifications = findChild(root, jsSelector('is_send_notifications'));
        this.state.is_send_notifications = FormInput.init(this.elem.is_send_notifications);

        this.elem.title = findChild(root, jsSelector('title_label'));
        this.elem.save_button_label = findChild(root, jsSelector('save_button_label'));
        this.elem.default_assigned_to = findChild(root, jsSelector('default_assigned_to'));

        this.elem.google_tag_id = findChild(root, jsSelector('google_tag_id'))
            .filter('input')
            .first();

        this.elem.additional_email_recipients = findChild(root, jsSelector('additional_email_recipients'))
            .filter('textarea')
            .first();

        this.elem.form_preview_container = findChild(root, jsSelector('form-preview-container'));

        this.elem.form = findChild(root, jsSelector('form'));
        this.initOrphanMirrors(root);

        this.helper = new LeadFormHelper(root, this.refreshForm.bind(this));
        this.helper.setupAllListeners();

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });

        onEvent(this.elem.lead_form_status, 'change', (e) => {
            const checked = this.elem.lead_form_status.is(':checked');
            this.elem.lead_form_settings.toggleClass('t-hidden', !checked);
            this.refreshForm();
        });

        onEvent(this.elem.api_key_status, 'change', this.handleApiKeySwitchChange.bind(this));
    }

    /**
     * Create hidden form‐fields inside the real <form> for those
     * out-of-form inputs, and keep them in sync on change.
     */
    initOrphanMirrors(root) {
        const $form = this.elem.form;
        const orphanMap = [
            { sel: 'default_assigned_to',  tag: 'input',    type: 'hidden' },
            { sel: 'google_tag_id',        tag: 'input',    type: 'text', display: 'none'},
            { sel: 'additional_email_recipients', tag: 'textarea' }
        ];

        orphanMap.forEach(({ sel, tag, type , display}) => {
            const attrs = {
                name: sel,
                'data-js': sel
            };
            if (type) {
                attrs.type = type;
            }
            if (display) {
                attrs.style = `display: ${display}`;
            }

            const $mirror = $(`<${tag}>`)
                .attr(attrs)
                .attr(tag === 'input' && type === 'hidden' ? { 'data-parsley-excluded': 'false' } : {})
                .attr('data-parsley-errors-container', `#${sel}_err`)
                .css(tag === 'textarea' ? { display: 'none' } : {})
                .appendTo($form);

            const real = findChild(root, jsSelector(sel));

            const syncAndValidate = () => {
                $mirror.val(real.val());
                if ($mirror.parsley) {
                    $mirror.parsley().validate();
                }
            };
            onEvent(real, 'change keyup', syncAndValidate);
            syncAndValidate();
        });
    }


    render() {
        return lead_tpl({
            title: 'Edit Lead Form Settings',
            cancel_route: 'settings.leads.details',
            brand_name: profile_data.brand_name,
        });
    };
}

module.exports = Edit;
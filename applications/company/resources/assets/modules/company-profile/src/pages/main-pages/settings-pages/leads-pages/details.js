'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {onClick, findChild, jsSelector} = require("@ca-package/dom");
const Tooltip = require('@ca-submodule/tooltip');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

const leads_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/leads-pages/details.hbs');
const LeadFormBuilder = require('./utilities/lead-form-builder');
const {createErrorMessage} = require("../../../../../../../submodules/notification-toast/src/message/error");
const {STATUS_MESSAGES} = require("./utilities/constants");
const LeadFormHelper = require("./utilities/lead-form-helper");

class Details extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings'),
        });
        this.helper = null;
    };


    /**
     * Fetch settings data from server
     */
    async fetchLeadFormSettings() {
        this.state.settings.showLoader();
        try {
            const data = await Api.Resources.LeadFormsAction()
                .method(Api.Request.Method.GET)
                .accept('application/vnd.adg.fx.form-v1+json')
                .custom('current');


            await this.populate(data);
        } catch (error) {
            console.log(error);
            let message = createErrorMessage('Unable to save leads form settings, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.settings.hideLoader();
        }
    }


    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    async populate(data) {
        this.elem.enable_button.addClass('t-hidden');
        this.elem.edit_button.removeClass('t-hidden');

        this.elem.feature_status.html(STATUS_MESSAGES.NOT_SETUP);
        if (Object.keys(data).length === 0) return;

        if (data && !data.is_active) {
            this.elem.feature_status.html(STATUS_MESSAGES.DISABLED);
            return;
        }

        this.elem.feature_status.html(STATUS_MESSAGES.ENABLED);

        // Load API Key Section
        if (data.api_key) {
            this.elem.api_key_status.html(`<span class='h-text t-green'>Active</span>`);
            this.elem.api_key_code
                .removeClass('t-hidden')
                .html(data.api_key);
        }

        await this.loadPreviewCode(data);
    };

    /**
     * Load preview code
     *
     * @param data
     */
    async loadPreviewCode(data) {
        const { marketing_types, project_types } = await this.fetchMarketingInfo();

        const form_props = Object.assign({}, data, {
            form_url: profile_data.leads_website_form.form_url,
        });

        const lead_form_builder = new LeadFormBuilder({
            form_props: form_props,
            marketing_types,
            project_types,
            isMock: true
        });

        const lead = await lead_form_builder.buildForm()
        this.elem.form_preview_container.html(lead);
        this.elem.form_preview_container.removeClass('t-hidden');

        const html = lead_form_builder.generateHTML();
        this.elem.html_code_input.val(html);
        this.elem.code_container.removeClass('t-hidden');
        this.elem.leads_api_section.removeClass('t-hidden');

        this.helper.initializeFormComponents();
    }



    /**
     *
     * @returns {Promise<*>}
     */
    async fetchMarketingInfo() {
        try {
            return await Api.Resources.LeadFormsAction()
                .accept('application/vnd.adg.fx.form-v1+json')
                .method(Api.Request.Method.GET)
                .custom('info')

        } catch (error) {
            console.log(error);
            let message = createErrorMessage('Unable to fetch marketing info, please contact support');
            this.router.main_route.layout.toasts.addMessage(message);
        }
    }



    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.settings.setEditMode(false);
        await this.fetchLeadFormSettings()
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.feature_status = findChild(root, jsSelector('feature-status'));
        this.elem.code_container = findChild(root, jsSelector('code-container'));
        this.elem.html_code_input = findChild(root, jsSelector('html-code-input'));
        this.elem.html_code_copy_button = findChild(root, jsSelector('html-code-copy-button'));
        this.elem.edit_button = findChild(root, jsSelector('edit-button'));
        this.elem.enable_button = findChild(root, jsSelector('enable-button'));
        this.elem.leads_api_section = findChild(root, jsSelector('leads-api-section'));

        this.elem.email_code_link = findChild(root, jsSelector('email-code-link'));
        this.elem.api_key_status = findChild(root, jsSelector('api-key-status'));
        this.elem.api_key_code = findChild(root, jsSelector('api-key-code'));

        this.elem.form_preview_container = findChild(root, jsSelector('form-preview-container'));
        onClick(this.elem.html_code_copy_button, async () => {
            navigator.clipboard.writeText(this.elem.html_code_input.val()).then(() => {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Leads Website Form code was copied.'));
            })
        });

        onClick(this.elem.enable_button, async () => {
            await this.fetchLeadFormSettings();
        });

        onClick(this.elem.email_code_link, (e) => {
            e.preventDefault();
            const code = this.elem.html_code_input.val() || '';
            const subject = encodeURIComponent('Website Lead Form Embed Code');
            const body = encodeURIComponent(
                `My CRM, ${profile_data.brand_name}, has an embedded lead form option for my website that will push website leads into their system. Please find the embed code for our form below and take a look at the help center article that outlines built in tracking options as well.\n\n` +
                `Embed Code: ` + code + `\n\n` +
                `Help Center Article: https://cxlratr.to/hc-leads-form`
            );
            const mailto = `mailto:?subject=${subject}&body=${body}`;
            window.open(mailto, '_blank');
        })

        this.helper = new LeadFormHelper(root, () => {});
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return leads_tpl({
            title: 'Leads',
            edit_route: 'settings.leads.edit',
            brand_name: profile_data.brand_name,
        });
    };
}

module.exports = Details;
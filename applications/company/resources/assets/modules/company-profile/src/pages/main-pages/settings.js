'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onClickWatcher} = require("@ca-package/dom");

const settings_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings.hbs');

class Settings extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            active_menu_tab: 'email',
            tab_items: {
                email: {
                    title: 'Email',
                    icon: 'business--mail-line',
                    is_enabled: true,
                    route: 'email.details'
                },
                text_messaging: {
                    title: 'Text Messaging',
                    icon: 'communication--chat-3-line',
                    is_enabled: profile_data.features.text_messaging,
                    route: 'text_messaging.details'
                },
                reminders: {
                    title: 'Reminders',
                    icon: 'media--notification-2-line',
                    is_enabled: profile_data.features.bid_follow_ups,
                    route: 'reminders.details'
                },
                projects: {
                    title: 'Projects',
                    icon: 'document--folder-open-line',
                    is_enabled: true,
                    route: 'projects.details'
                },
                calendar: {
                    title: 'Calendar',
                    icon: 'business--calendar-line',
                    is_enabled: true,
                    route: 'calendar.details'
                },
                leads : {
                  title: 'Leads',
                    icon: 'finance--p2p-line',
                    is_enabled: profile_data.features.leads_website_form,
                    route: 'leads.details'
                },
                bid_defaults: {
                    title: 'Bid Defaults',
                    icon: 'document--draft-line',
                    is_enabled: true,
                    route: 'bid_defaults.details'
                }
            }
        });
    };

    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            email: {
                default: true,
                page: require('./settings-pages/email')
            },
            text_messaging: {
                path: '/text-messaging',
                page: require('./settings-pages/text_messaging')
            },
            reminders: {
                path: '/reminders',
                page: require('./settings-pages/reminders')
            },
            projects: {
                path: '/projects',
                page: require('./settings-pages/projects')
            },
            calendar: {
                path: '/calendar',
                page: require('./settings-pages/calendar')
            },
            bid_defaults: {
                path: '/bid-defaults',
                page: require('./settings-pages/bid_defaults')
            },
            leads: {
                path: '/leads',
                page: require('./settings-pages/leads')
            }
        };
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Set active tab menu
     *
     * @param {string} id
     * @param {string} full_route
     */
    setActiveTabMenu(id, full_route = null) {
        if (this.state.active_menu_tab === id) {
            return;
        }
        this.state.tab_items[this.state.active_menu_tab].elem.removeClass('t-active');
        this.state.tab_items[id].elem.addClass('t-active');
        this.state.active_menu_tab = id;

        if (full_route !== null) {
            this.router.navigate(full_route);
            return;
        }
        this.router.navigate(`settings.${this.state.tab_items[id].route}`);
    };

    /**
     * Turn edit mode on or off
     *
     * @param {boolean} status
     */
    setEditMode(status) {
        for (let id in this.state.tab_items) {
            if (this.state.active_menu_tab === id) {
                continue;
            }
            let $item =  this.state.tab_items[id];
            if (!$item.is_enabled) {
                continue;
            }
            if (status) {
                $item.elem.addClass('t-disabled');
            } else {
                $item.elem.removeClass('t-disabled');
            }
        }
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Set active item from route id
     *
     * @param {string} route_id
     * @param {string} full_route
     */
    setActiveItemFromRoute(route_id, full_route) {
        for (let id of Object.keys(Settings.routes)) {
            if (route_id.indexOf(id) !== 0) {
                continue;
            }
            this.setActiveTabMenu(id, full_route);
            break;
        }
    };

    /**
     * Handle a route change
     *
     * Automatically updates the selected tab based on the new routes id
     *
     * @param {object} current
     * @param {object} previous
     */
    onRouteChange({current, previous}) {
        this.setActiveTabMenu(current.name.split('.')[1]);
    };

    /**
     * Unload component
     *
     * @param {object} request
     */
    async unload(request, next) {
        this.router.unsubscribe('route-changed', this);
        await super.unload(request, next);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        let route_id = this.router.current_route.name;
        this.setActiveItemFromRoute(route_id.replace('settings.', ''), route_id);
        this.router.subscribe('route-changed', this, 'onRouteChange');

        await super.load(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.tab_menu = findChild(root, jsSelector('tab-menu'));
        this.elem.tab_item = findChild(root, jsSelector('tab-item'));

        //this.elem.loader = findChild(root, jsSelector('loader'));
        this.elem.page_container = findChild(root, jsSelector('page-container'));

        for (let item of this.elem.tab_item) {
            let $item = $(item);
            this.state.tab_items[$item.data('id')]['elem'] = $item;
        }

        let that = this;

        onClickWatcher(this.elem.tab_menu, jsSelector('tab-item'), function() {
            if ($(this).hasClass('t-disabled')) {
                return false;
            }
            let id = $(this).data('id');
            that.setActiveTabMenu(id);
            return false;
        }, true);
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return settings_tpl({
            tab_items: this.state.tab_items
        });
    };
}

module.exports = Settings;

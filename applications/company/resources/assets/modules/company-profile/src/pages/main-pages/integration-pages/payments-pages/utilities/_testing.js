'use strict';

const UI = require('./ui-enhancer');

/**
 * Get today's date formatted as MM/DD/YYYY
 * @returns {string} Today's date in MM/DD/YYYY format
 */
function getTodayFormatted() {
    const today = new Date();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const year = today.getFullYear();
    return `${month}/${day}/${year}`;
}

/**
 * Get a date of birth 30 years ago formatted as MM/DD/YYYY
 * @returns {string} DOB in MM/DD/YYYY format
 */
function getDOBFormatted() {
    const today = new Date();
    const dob = new Date(today.getFullYear() - 30, today.getMonth(), today.getDate());
    const month = (dob.getMonth() + 1).toString().padStart(2, '0');
    const day = dob.getDate().toString().padStart(2, '0');
    const year = dob.getFullYear();
    return `${month}/${day}/${year}`;
}

/**
 * Generate a valid phone number that meets CoPilot requirements:
 * - Area code cannot start with 0 or 1 (must be 2-9)
 * - Cannot be all the same digits
 * - Cannot be common invalid patterns
 */
function generateValidPhoneNumber() {
    let attempts = 0;
    const max_attempts = 100;
    
    while (attempts < max_attempts) {
        const area_code = (Math.floor(Math.random() * 8) + 2) * 100 + Math.floor(Math.random() * 100);
        const exchange = (Math.floor(Math.random() * 8) + 2) * 100 + Math.floor(Math.random() * 100);
        const last_four = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        
        const phone_number = `${area_code.toString().padStart(3, '0')}${exchange.toString().padStart(3, '0')}${last_four}`;
        
        // Check if all digits are the same
        if (new Set(phone_number.split('')).size === 1) {
            attempts++;
            continue;
        }
        
        // Check against invalid patterns
        const invalid_patterns = [
            '1234567890',
            '0123456789',
            '9876543210',
            '5555555555',
            '1111111111',
            '0000000000'
        ];
        
        if (invalid_patterns.includes(phone_number)) {
            attempts++;
            continue;
        }
        
        // Format as XXX-XXX-XXXX
        return `${phone_number.slice(0, 3)}-${phone_number.slice(3, 6)}-${phone_number.slice(6)}`;
    }
    
    // Fallback if we can't generate a valid number (should be very rare)
    return '************';
}

function generateRandomTestData() {
    const random_num = Math.floor(Math.random() * 9000) + 1000;
    const first_names = ["John", "Jane", "Mike", "Sarah", "David", "Lisa"];
    const last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Davis"];
    
    const first_name = first_names[Math.floor(Math.random() * first_names.length)];
    const last_name = last_names[Math.floor(Math.random() * last_names.length)];
    const full_name = `${first_name} ${last_name}`;
    
    const formatted_phone = generateValidPhoneNumber();
    const formatted_mobile = generateValidPhoneNumber();
    
    const deposit_account_num = Math.floor(Math.random() * *********) + *********;
    const withdrawal_account_num = Math.floor(Math.random() * *********) + *********;
    
    return {
        random_num,
        first_name,
        last_name,
        full_name,
        formatted_phone,
        formatted_mobile,
        deposit_account_num,
        withdrawal_account_num
    };
}

function setupTestButton(elements) {
    const environment = profile_data?.environment || 'PROD';
    const url_params = new URLSearchParams(window.location.search);
    const testing_enabled = url_params.get('testing') === 'true';

    console.log('Environment:', environment, 'Testing Enabled:', testing_enabled);

    if (environment !== 'PROD' && testing_enabled) {
        elements.fill_test_data_button?.removeClass('t-hidden');
    } else {
        elements.fill_test_data_button?.addClass('t-hidden');
    }
}

function fillTestData(elements, ui_manager) {
    const test_data = generateRandomTestData();

    const fillField = (field_name, value) => {
        const $field = elements.setup_form.find(`[name="${field_name}"]`);
        if ($field.length > 0) {
            $field.val(value).trigger('input').trigger('change');
        }
    };
    
    // Business Info
    fillField('dba_name', `${test_data.last_name} Foundation Repair`);
    fillField('legal_business_name', `${test_data.last_name} Foundation Repair LLC`);
    fillField('tax_filing_name', `${test_data.last_name} Foundation Repair LLC`);
    fillField('tax_filing_method', 'EIN');

    setTimeout(() => {
        const tax_id = `${Math.floor(Math.random() * 90) + 10}-${Math.floor(Math.random() * 9000000) + 1000000}`;
        fillField('tax_id', tax_id);
    }, 100);

    fillField('business_start_date', getTodayFormatted());
    fillField('owner_dob', getDOBFormatted());
    
    // Contact Info
    fillField('owner_first_name', test_data.first_name);
    fillField('owner_last_name', test_data.last_name);
    fillField('owner_email', `${test_data.first_name.toLowerCase()}.${test_data.last_name.toLowerCase()}@example.com`);
    fillField('contact_name', test_data.full_name);
    fillField('contact_email', `contact@${test_data.last_name.toLowerCase()}foundation.com`);
    fillField('contact_phone', test_data.formatted_phone);
    
    // Business Address
    fillField('business_address_line1', '123 Business Street');
    fillField('business_city', 'Austin');
    fillField('business_state', 'TX');
    fillField('business_zip', '78701');
    fillField('business_country', 'US');

    // Owner Info
    fillField('ownership_type_code', 'LLC');

    const $owner_title = elements.setup_form.find('select[data-js="owner-title"]');
    UI.handleOwnershipTypeChange('LLC', $owner_title);

    setTimeout(() => {
        const first_option = $owner_title.find('option:not(:disabled)').first().val();
        if (first_option) {
            fillField('owner_title', first_option);
        } else {
            fillField('owner_title', 'OWNER');
        }
    }, 100);

    fillField('owner_phone', test_data.formatted_phone);
    fillField('owner_ssn', '***********');
    fillField('owner_ownership_pct', '100.00');
    fillField('drivers_license_number', `DL${test_data.random_num}${Math.floor(Math.random() * 1000)}`);
    fillField('drivers_license_state', 'TX');
    
    // Owner Address
    fillField('owner_address_line1', '456 Owner Avenue');
    fillField('owner_address_line2', 'Apt 2B');
    fillField('owner_city', 'Austin');
    fillField('owner_state', 'TX');
    fillField('owner_zip', '78702');
    fillField('owner_country', 'US');
    
    // Banking Info - Deposit
    fillField('deposit_account_number', test_data.deposit_account_num.toString());
    fillField('deposit_routing_number', '*********');
    fillField('deposit_account_type', 'BIZ');
    fillField('deposit_bank_name', 'Wells Fargo Bank');
    
    // Banking Info - Withdrawal
    fillField('withdrawal_account_number', test_data.withdrawal_account_num.toString());
    fillField('withdrawal_routing_number', '*********');
    fillField('withdrawal_account_type', 'BIZ');
    fillField('withdrawal_bank_name', 'Bank of America');
    
    // Processing Info
    fillField('average_monthly_volume', '50000');
    fillField('high_ticket_amount', '15000');
    fillField('average_ticket_amount', '2500');
}

module.exports = {
    setupTestButton,
    fillTestData
};
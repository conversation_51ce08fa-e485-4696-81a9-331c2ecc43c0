'use strict';
const Api = require('@ca-package/api');

async function fetchLeadFormSettings () {
    return Api.Resources.LeadFormsAction()
        .method(Api.Request.Method.GET)
        .accept('application/vnd.adg.fx.form-v1+json')
        .custom('current');
}

async function getUsers () {
    const { entities } = await Api.Resources.Users()
        .fields(['id','first_name','last_name'])
        .filter('is_active', true)
        .filter('is_user_invited', false)
        .sort('created_at', 'asc')
        .all();

    return entities.map(u => u.data);
}

module.exports = { getUsers, fetchLeadFormSettings };
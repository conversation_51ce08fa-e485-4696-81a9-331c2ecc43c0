'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const FormValidator = require('@ca-submodule/validator');
const Tooltip = require('@ca-submodule/tooltip');

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const settings_tpl = require('@cam-company-profile-tpl/pages/main-pages/integrations-pages/payments-pages/settings.hbs');
const $ = require("jquery");

class Settings extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            integrations: parent.getParentByName('integrations'),
            merchant_data: null
        });
    };

    /**
     * Populate fields using data object
     *
     * @param {object} data
     */
    populate(data) {
        this.elem.is_payments_active.attr('checked', !!data.is_payments_active).trigger('change');
        this.elem.payment_ach_acceptance_enabled.attr('checked', !!data.ach_acceptance_enabled).trigger('change');
        this.elem.payment_credit_card_acceptance_enabled.attr('checked', !!data.credit_card_acceptance_enabled).trigger('change');
        this.elem.credit_card_processing_fee.val(data.credit_card_processing_fee || 0.0);

        if (!this.state.merchant_data.is_ach_available) {
            this.elem.payment_ach_acceptance_enabled.prop('disabled', true);
            this.state.ach_status_switch.toggleDisabled(true);
        }
    };


    /**
     * Save payment settings
     */
    save() {
        this.clearError();
        this.state.integrations.showLoader();

        let data = {
            settings: {
                is_payments_active: this.elem.is_payments_active.is(':checked') ?? false,
                payment_ach_acceptance_enabled: this.elem.payment_ach_acceptance_enabled.is(':checked') ?? false,
                payment_credit_card_acceptance_enabled: this.elem.payment_credit_card_acceptance_enabled.is(':checked') ?? false,
                credit_card_processing_fee: parseFloat(this.elem.credit_card_processing_fee.val() || 0.0)
            }
        };


        Api.Resources.Companies().partialUpdate('current', data)
            .then((response) => {
                this.state.integrations.hideLoader();
                const message = createSuccessMessage('Payment settings saved successfully');
                this.router.main_route.layout.toasts.addMessage(message);
                this.router.navigate('integrations.payments.details');
            })
            .catch((error) => {
                console.error(error);
                this.state.integrations.hideLoader();
                const response = error.response || {};
                if (response.statusCode === 422) {
                    const item_errors = response.data?.errors || [];
                } else {
                    const message = createErrorMessage('Unable to save payment settings, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                }
            });
    };

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            const response = await $.ajax({
                url: '/api/integration/payments/settings',
                type: Api.Request.Method.GET,
                contentType: 'application/json',
            });

            if (response) {
                this.state.merchant_data = response.merchant_data;
                this.populate(response.company_settings);
            }
        } catch (error) {
            console.error('Error fetching payment settings:', error);
            createErrorMessage('Failed to load payment settings');
        }
    }

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {

        this.elem.cancel.prop('disabled', false);
        this.state.validator.reset();
        this.elem.root.scrollTop(0);

        this.clearError();

        this.state.integrations.setEditMode(false);
        await super.unload(request, next);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.integrations.showLoader();
        this.state.integrations.setEditMode(true);
        await this.fetchData();
        this.state.integrations.hideLoader();
        await super.load(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        FormInput.init(this.elem.is_payments_active);
        this.state.ach_status_switch = FormInput.init(this.elem.payment_ach_acceptance_enabled);
        FormInput.init(this.elem.payment_credit_card_acceptance_enabled);

        this.state.validator = FormValidator.create(this.elem.form, {
            is_payments_active: {},
            payment_ach_acceptance_enabled: {},
            payment_credit_card_acceptance_enabled: {},
            credit_card_processing_fee: {
                type: 'number',
                min: 0,
                max: 3,
                step: 0.1
            },
        }, {
            validate_event: true,
            error_event: true
        }).on('submit', () => this.save())
            .on('validate', () => this.clearError())
            .on('error', () => this.setError('Please review form errors below'));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.cancel = findChild(root, jsSelector('cancel'));
        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.error = findChild(root, jsSelector('error'));

        this.elem.is_payments_active = findChild(root, jsSelector('is-payments-active'));
        this.elem.status_tag = findChild(root, jsSelector('status-tag'));

        this.elem.payment_ach_acceptance_enabled = findChild(root, jsSelector('payment-ach-acceptance-enabled'));
        this.elem.payment_credit_card_acceptance_enabled = findChild(root, jsSelector('payment-credit-card-acceptance-enabled'));
        this.elem.credit_card_processing_fee = findChild(root, jsSelector('credit-card-processing-fee'));
        this.initForm();

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });

        onEvent(this.elem.is_payments_active, 'change', (e) => {
            e.preventDefault();
            let tag = '<span class="h-text t-grey">Disabled</span>';
            if (this.elem.is_payments_active.is(':checked')) {
                tag = '<span class="h-text t-green">Enabled</span>';
            }
            this.elem.status_tag.html(tag);
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return settings_tpl({
            cancel_route: 'integrations.payments.details',
            brand_name: profile_data.brand_name
        });
    };
}

module.exports = Settings;
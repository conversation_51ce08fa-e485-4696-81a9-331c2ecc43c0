'use strict';

import $ from 'jquery';
import accounting from "accounting";
import moment from 'moment-timezone';

import Api from '@ca-package/api';
import {findChild, insertTemplate, jsSelector} from '@ca-package/dom';
import {Base as Table} from '@ca-submodule/table';

const Cookie = require('@cac-js/utils/cookie');

import layout_tpl from '@cam-appointment-tpl/layout.hbs';

/**
 * @memberof module:Appointment
 */
export class Controller {
    /**
     * Appointment constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            page_name: 'appointment',
            version: 'v1',
            saved_scope: null,
            saved_table_order: null,
            saved_table_visibility: null,
            table: null,
            layout,
            modals: {},
            table_scope: {
                sorts: {
                    start: Table.Sort.ASC
                },
                filters: {
                    start: [Table.Operators.GREATER_THAN, moment().format('YYYY-MM-DD')+'T00:00:00Z']
                }
            },
            total_bids_accepted_value: 0
        };
        this.boot();
    };

    /**
     * Get layout instance
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Change table headers after scope has changed
     */
    changeTableHeaders() {
        this.state.table.changeColumnName('Total Sold', `Total Sold <span class="table-header-tag">${accounting.formatMoney(this.state.total_bids_accepted_value)}</span>`);
        this.state.total_bids_accepted_value = 0;
    };

    /**
     * Set table header data after row has been drawn
     *
     * @param {object} data
     */
    setTableHeaderData(data) {
        this.state.total_bids_accepted_value = data.total_bids_accepted_value !== null ? parseFloat(data.total_bids_accepted_value) + parseFloat(this.state.total_bids_accepted_value) : this.state.total_bids_accepted_value;
    };

    /**
     * Get table
     *
     * @readonly
     *
     * @returns {module:Table.Base}
     */
    get table() {
        return this.state.table;
    };

    /**
     * Generate project management page url
     *
     * @param {number} id - customer id
     * @returns {string}
     */
    projectManagementUrl(id) {
        return `${window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}', id)}/schedule`;
    };

    /**
     * Define the default settings for customer DataTable
     *
     * @returns {Object}
     */
    tableSettings() {
        return {
            server_paginate: false,
            load_tooltip: false,
            use_table_settings: true,
            column_order: this.state.saved_table_order,
            column_visibility: this.state.saved_table_visibility
        };
    };

    /**
     * Create the Appointment DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table, this.tableSettings())
            .on('row_click', (data) => {
                window.location.href = this.projectManagementUrl(data.project_id);
            }).on('scope_change', (scope) => {
                if (appointment_data.billing_allowed) {
                    this.changeTableHeaders();
                }

                this.state.table_scope = scope;
                let url_scope = Table.buildUrlFromScope(this.state.table_scope);

                // set cookie with scope to expire after an hour
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_scope`, url_scope.replace('?', ''), Cookie.expirationTypes().Hours, 1);
                window.history.replaceState(null, '', window.location.href.split('?')[0]+url_scope);
            }).on('table_settings_changed', (config) => {
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_order`, config.order.toString());
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_visibility`, config.visibility.toString());
            }).on('row_drawn', (row, data) => {
                if (appointment_data.billing_allowed) {
                    this.setTableHeaderData(data);
                }
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Appointments'
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        this.state.table.setFilterOptions({
            status: {
                label: 'Status',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'In Progress/Past Appointment',
                        value: Api.Constants.Events.Status.ACTIVE
                    },
                    2: {
                        label: 'Cancelled',
                        value: Api.Constants.Events.Status.CANCELLED
                    },
                    3: {
                        label: 'Replaced',
                        value: Api.Constants.Events.Status.REPLACED
                    },
                    4: {
                        label: 'Completed',
                        value: Api.Constants.Events.Status.COMPLETED
                    }
                }
            },
            type: {
                label: 'Appointment Type',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'Evaluation',
                        value: Api.Constants.Events.Type.SALES
                    },
                    2: {
                        label: 'Installation',
                        value: Api.Constants.Events.Type.INSTALLATION
                    }
                }
            },
            start: {
                label: 'Start Date',
                type: Table.FilterValueTypes.DATE,
            },
            end: {
                label: 'End Date',
                type: Table.FilterValueTypes.DATE,
            },
            source: {
                label: 'Scheduling Source',
                type: Table.FilterValueTypes.SELECT,
                options: {
                    1: {
                        label: 'New Customer',
                        value: Api.Constants.Events.Source.CUSTOMER_ADD
                    },
                    2: {
                        label: 'New Property',
                        value: Api.Constants.Events.Source.PROPERTY_ADD
                    },
                    3: {
                        label: 'New Project',
                            value: Api.Constants.Events.Source.PROJECT_ADD
                    },
                    4: {
                        label: 'Project Reschedule',
                        value: Api.Constants.Events.Source.PROJECT_RESCHEDULE
                    },
                    5: {
                        label: 'Calendar Reschedule',
                        value: Api.Constants.Events.Source.CALENDAR_RESCHEDULE
                    },
                    6: {
                        label: 'Project Schedule',
                        value: Api.Constants.Events.Source.PROJECT_SCHEDULE
                    }
                }
            },
            send_notifications: {
                label: 'Notified',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            has_bids_accepted: {
                label: 'Has Bids Accepted',
                visible: appointment_data.billing_allowed,
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            user_id: {
                label: 'Scheduled User',
                type: Table.FilterValueTypes.SELECT,
                options: appointment_data.filter_options.all_users
            },
            is_all_day: {
                label: 'All Day',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            created_by_user_id: {
                label: 'Created By',
                type: Table.FilterValueTypes.SELECT,
                options: appointment_data.filter_options.all_users
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_by_user_id: {
                label: 'Updated By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: appointment_data.filter_options.all_users
            },
            cancelled_at: {
                label: 'Cancelled Date',
                type: Table.FilterValueTypes.DATE,
            },
            replaced_at: {
                label: 'Replaced Date',
                type: Table.FilterValueTypes.DATE,
            },
            completed_at: {
                label: 'Completed Date',
                type: Table.FilterValueTypes.DATE,
            }
        });

        let status_map = new Map([
            [Api.Constants.Events.Status.ACTIVE, '<span class="h-text t-yellow">In Progress</span>'],
            [Api.Constants.Events.Status.CANCELLED, '<span class="h-text t-grey">Cancelled</span>'],
            [Api.Constants.Events.Status.REPLACED, '<span class="h-text t-grey">Replaced</span>'],
            [Api.Constants.Events.Status.COMPLETED, '<span class="h-text t-green">Completed</span>'],
            [5, '<span class="h-text t-yellow">Past Appointment</span>']
        ]);

        let type_map = new Map([
            [Api.Constants.Events.Type.SALES, 'Evaluation'],
            [Api.Constants.Events.Type.INSTALLATION, 'Installation']
        ]);

        let source_map = new Map([
            [Api.Constants.Events.Source.CUSTOMER_ADD, 'New Customer'],
            [Api.Constants.Events.Source.PROPERTY_ADD, 'New Property'],
            [Api.Constants.Events.Source.PROJECT_ADD, 'New Project'],
            [Api.Constants.Events.Source.PROJECT_RESCHEDULE, 'Project Reschedule'],
            [Api.Constants.Events.Source.CALENDAR_RESCHEDULE, 'Calendar Reschedule'],
            [Api.Constants.Events.Source.PROJECT_SCHEDULE, 'Project Details']
        ]);

        let columns = {
            customer_name: {
                label: 'Customer',
                value: (data) => {
                    return `<a href="${window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.project.property.customer.id)}">${data.customer_name}</a>`
                },
            },
            property_address: {
                label: 'Property',
                value: (data) => {
                    return `<a href="${window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.project.property.customer.id)}">${data.property_address}</a>`
                },
            },
            project_name: {
                label: 'Project',
                value: (data) => {
                    return `<a href="${window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}', data.project_id)}">${data.project_name}</a>`
                },
            },
            project_type: {
                label: 'Project Type',
                orderable: false,
                value: (data) => {
                    if (data.project === null) {
                        return '';
                    }
                    return data.project.type_name;
                },
            },
            project_salesperson_user_name: {
                label: 'Project Salesperson',
                value: (data) => {
                    if (data.project_salesperson_user_name === null) {
                        return null;
                    }
                    return data.project_salesperson_user_name;
                },
            },
            type: {
                label: 'Appointment Type',
                value: data => type_map.get(data.type)
            },
            scheduled_user_name: {
                label: 'Scheduled User',
                value: data => data.scheduled_user_name
            },
            start: {
                label: 'Start',
                value: (data) => {
                    let date = data.start;
                    if (date === null) {
                        return null;
                    }
                    let format = data.is_all_day ? 'MM/DD/YYYY' : 'MM/DD/YYYY h:mm a';
                    return moment(date).tz(this.state.layout.user.timezone).format(format);
                },
            },
            end: {
                label: 'End',
                value: (data) => {
                    let date = data.end;
                    if (date === null) {
                        return null;
                    }
                    let format = data.is_all_day ? 'MM/DD/YYYY' : 'MM/DD/YYYY h:mm a';
                    return moment(date).tz(this.state.layout.user.timezone).format(format);
                },
            },
            is_all_day: {
                label: 'Is All Day?',
                value: (data) => {
                    return data.is_all_day === true ? 'Yes' : '';
                }
            },
            status: {
                label: 'Status',
                value: (data) => {
                    let date = data.start;
                    let needs_change = moment(date).tz(this.state.layout.user.timezone).isBefore(moment());
                    if (needs_change && data.status === Api.Constants.Events.Status.ACTIVE && data.type === Api.Constants.Events.Type.SALES) {
                        return status_map.get(5);
                    }
                    return status_map.get(data.status);
                }
            },
            source: {
                label: 'Scheduling Source',
                value: data => source_map.get(data.source)
            },
            send_notifications: {
                label: 'Is Notified?',
                value: (data) => {
                    return data.send_notifications === true ? 'Yes' : '';
                }
            }
        };

        if (appointment_data.billing_allowed) {
            Object.assign(columns, {
                total_bids_accepted_value: {
                    label: 'Total Sold',
                    value: (data) => {
                        if (data.total_bids_accepted_value === null) {
                            return null;
                        }
                        return accounting.formatMoney(data.total_bids_accepted_value);
                    }
                }
            });
        }
        Object.assign(columns, {
            cancelled_at: {
                label: 'Cancelled At',
                value: (data) => {
                    let date = data.cancelled_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            replaced_at: {
                label: 'Replaced At',
                value: (data) => {
                    let date = data.replaced_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            completed_at: {
                label: 'Completed At',
                value: (data) => {
                    let date = data.completed_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            created_by_user_name: {
                label: 'Created By',
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_by_user_name: {
                label: 'Updated By'
            }
        });

        // set columns config
        this.state.table.setColumns(columns);

        // set row action config
        this.state.table.setRowActions({
            view_details: {
                label: 'View Details',
                link: {
                    href: data => this.projectManagementUrl(data.project_id)
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            export_csv: {
                label: 'Export',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table.buildRequest(new Api.Request, this.state.table_scope);
                    let url = window.fx_url.API + 'export/project-events' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                    window.location.href = url;
                },
                visible: appointment_data.export,
                type_class: 't-tertiary-icon',
                icon: 'system--download-line'
            }
        });

        this.state.table.setAjax(Api.Resources.ProjectEvents, (request) => {
            let accept_type = appointment_data.billing_allowed ? 'application/vnd.adg.fx.event-collection-v1+json' : 'application/vnd.adg.fx.event-collection-limited-v1+json';
            request.accept(accept_type);
        });
    };

    /**
     * Get Url parameters to build table scope
     *
     * @returns {string|null}
     */
    getUrlParameters() {
        let parameters = window.location.search;
        if (parameters === '') {
            return null;
        }
        return parameters.substring(1);
    };

    /**
     * Draw table
     */
    load() {
        let url_parameters = this.getUrlParameters();
        // if request query contains scope we use it
        if (url_parameters !== null) {
            this.state.table_scope = Table.buildScopeFromQuery(url_parameters);
            // otherwise we get it from the cookie
        } else if (this.state.saved_scope !== null) {
            this.state.table_scope = Table.buildScopeFromQuery(this.state.saved_scope);
        }

        // otherwise we pull from the default scope stored in the state
        this.state.table.setState(this.state.table_scope);
        this.state.table.build();
    };

    /**
     * Boot Appointment
     */
    boot() {
        this.layout.setModeWindow();
        this.layout.setTitle('Appointments');
        this.elem.root = insertTemplate(this.layout.elem.content, layout_tpl());
        this.elem.table = findChild(this.elem.root, jsSelector('data-table'));

        this.state.saved_scope = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_scope`);

        let column_order = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_order`);
        if (column_order !== null) {
            this.state.saved_table_order = column_order.split(',');
        }

        let column_visibility = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_visibility`);
        if (column_visibility !== null) {
            this.state.saved_table_visibility = column_visibility.split(',');
        }

        this.createTable();
        this.load();
    };
}

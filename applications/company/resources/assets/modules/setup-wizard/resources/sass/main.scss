@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/form';
@use '~@cac-sass/app/form/inline';
@use '~@cas-form-input-sass/button-group';
@use '~@cas-form-input-sass/checkbox';
@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/number';
@use '~@cas-form-input-sass/switch';
@use '~@cas-form-input-sass/wysiwyg';
@use '~@cas-form-input-sass/uppy';
@use '~@cac-sass/app/grid';
@use '~@cas-layout-sass/layout';
@use '~@cas-modal-sass/modal';
@use '~@cas-table-sass/table';
@use '~@cas-tooltip-sass/tooltip';

@use '~@simonwep/pickr/src/scss/themes/monolith';

@font-face {
    font-family: 'icomoon';
    src:  url('~@cac-public/fonts/icomoon.eot?e7ly90');
    src:  url('~@cac-public/fonts/icomoon.eot?e7ly90#iefix') format('embedded-opentype'),
    url('~@cac-public/fonts/icomoon.ttf?e7ly90') format('truetype'),
    url('~@cac-public/fonts/icomoon.woff?e7ly90') format('woff');
    font-weight: normal;
    font-style: normal;
}

.m-setup-builder {
    @include base.full-width-height;
    padding: base.unit-rem-calc(24px);
    position: relative;
    @include base.respond-to('<small') {
        padding: 0;
    }
    .c-sb-outer {
        max-width: base.unit-rem-calc(1200px);
        position: relative;
        @include base.full-width-height;
        margin: auto;
        border-radius: base.unit-rem-calc(12px);
        box-shadow: base.$elevation-level-2;
        background: base.$color-white-default;
        @include base.respond-to('<small') {
            width: 100%;
            margin: 0;
            border-radius: 0;
            box-shadow: none;
            padding: 0;
        }
    }
    .c-sb-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
        .c-sbi-loader {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            @include base.full-width-height;
            background: rgba(255, 255, 255, 0.5) url('~@cac-public/images/loading_blue.svg') no-repeat center;
            background-size: base.unit-rem-calc(80px) base.unit-rem-calc(80px);
            z-index: 120;
            border-radius: base.unit-rem-calc(12px);
        }
        .c-sbi-header {
            position: relative;
            @include base.header-icon-text-icon;
            border-bottom: none;
            padding: base.unit-rem-calc(12px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                order: 2;
                border-top: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            }
        }
            .c-sbih-actions {}
                .c-sbiha-primary {
                    @include base.button-text-primary;
                    &.t-hidden {
                        display: none;
                    }
                }
                .c-sbiha-secondary {
                    @include base.button-text-secondary;
                    &.t-hidden {
                        display: none;
                    }
                }
                .c-sbiha-tertiary {
                    &.t-text-icon {
                        @include base.button-text-icon-tertiary;
                    }
                    &.t-icon-text {
                        @include base.button-icon-text-tertiary;
                    }
                    &.t-hidden {
                        display: none;
                    }
                }
        .c-sbi-pages {
            overflow: hidden;
            height: 100%;
            margin-left: base.unit-rem-calc(8px);
            @include base.respond-to('<medium') {
                border: none;
                margin-left: 0;
            }
            &.t-no-border {
                border-bottom: none;
            }
            &.t-borders {
                margin-left: 0;
                @include base.respond-to('>medium') {
                    order: 1;
                    border-top: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                }
                @include base.respond-to('<small') {
                    border-bottom: none;
                }
            }
        }
            .c-sbip-page {
                height: 100%;
                &.t-hidden {
                    display: none;
                }
            }
    .c-sb-content {
        position: relative;
        overflow: auto;
        height: 100%;
        &.t-flex {
            display: grid;
            grid-template-columns: auto 1fr;
            grid-template-rows: 1fr base.unit-rem-calc(56px);
            @include base.respond-to('<medium') {
                display: flex;
                flex-direction: column;
            }
        }
        &.t-instruction {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: base.unit-rem-calc(24px) base.unit-rem-calc(96px);
            @include base.respond-to('<medium') {
                padding: base.unit-rem-calc(24px);
            }
        }
        &.t-complete {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
        .c-sbc-instructions {
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex: 1;
            margin-bottom: base.unit-rem-calc(64px);
        }
            .c-sbci-title {}
            .c-sbci-subtitle {
                margin-bottom: base.unit-rem-calc(8px);
            }
            .c-sbci-description {
                &.t-no-margin {
                    margin-bottom: 0;
                }
            }
            .c-sbci-list {}
        .c-sbc-image {
            height: base.unit-rem-calc(172px);
            width: 100%;
        }
            .c-sbci-wrapper {
                height: base.unit-rem-calc(150px);
                border-bottom: base.unit-rem-calc(2px) solid base.$color-green-light-1;
                position: relative;
            }
                .c-sbciw-image {
                    @include base.svg-icon-base;
                    fill: black;
                    bottom: base.unit-rem-calc(-2px);
                    right: 0;
                }

        .c-sbc-review {
            display: flex;
            flex-direction: column;
            padding: base.unit-rem-calc(48px) base.unit-rem-calc(96px);
            @include base.respond-to('<medium') {
                padding: base.unit-rem-calc(24px);
            }
        }
            .c-sbcr-title {
                margin-bottom: base.unit-rem-calc(16px);
                @include base.typo-header($size: 32px, $line-height: base.unit-rem-calc(40px));
            }
            .c-sbcr-wrapper {
                &.t-skipped {
                    margin-top: base.unit-rem-calc(16px);
                    margin-bottom: 0;
                }
            }
                .c-sbcrw-subtitle {
                    margin-bottom: base.unit-rem-calc(8px);
                    @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
                }
                .c-sbcrw-description {
                    @include base.typo-paragraph;
                }
                .c-sbcrw-items {
                    display: flex;
                    flex-direction: column;
                    gap: base.unit-rem-calc(16px);
                    margin: base.unit-rem-calc(16px) 0;
                }

        .c-sbc-success {}
            .c-sbcs-check {
                width: base.unit-rem-calc(500px);
                height: base.unit-rem-calc(200px);
                @include base.respond-to('<550') {
                    width: base.unit-rem-calc(400px);
                    height: base.unit-rem-calc(175px);
                }
                @include base.respond-to('<450') {
                    width: base.unit-rem-calc(300px);
                    height: base.unit-rem-calc(120px);
                }
                @include base.respond-to('<350') {
                    width: base.unit-rem-calc(200px);
                    height: base.unit-rem-calc(85px);
                }
            }
            .c-sbcs-title {
                flex: 1;
                @include base.typo-header($size: 32px, $line-height: base.unit-rem-calc(40px));
                color: base.$color-green-default;
                text-align: center;
                margin-bottom: base.unit-rem-calc(40px);
                @include base.respond-to('<550') {
                    @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(40px));
                }
                @include base.respond-to('<450') {
                    @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(40px));
                }
            }

        .c-sbc-progress-bar {
            display: flex;
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(16px) 0;
            overflow: auto;
            background-color: base.$color-background-form;
            border-width: base.unit-rem-calc(1px) 0 base.unit-rem-calc(1px) base.unit-rem-calc(1px);
            border-style: solid;
            border-color: base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(12px) 0 0 base.unit-rem-calc(12px);
            grid-area: 1 / 1 / 2 / 2;
            @include base.respond-to('<medium') {
                display: none;
            }
        }
            .c-sbcpb-section {
                display: flex;
                flex: 1;
            }
                .c-sbcpbs-title {
                    margin-bottom: base.unit-rem-calc(16px);
                    .t-subtitle {
                        font-style: italic;
                        font-size: base.unit-rem-calc(12px);
                        font-weight: 400;
                    }
                }
                .c-sbcpbs-container {
                    display: flex;
                }
                    .c-sbcpbsc-items {
                        display: flex;
                        flex-direction: column;
                        padding: 0 base.unit-rem-calc(8px );
                    }
                        .c-sbcpbsci-item {
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            gap: base.unit-rem-calc(16px);
                            &:last-of-type {
                                padding-bottom: base.unit-rem-calc(24px);
                            }
                            &:nth-last-child(2) {
                                padding-bottom: base.unit-rem-calc(24px);
                            }
                            &.t-complete {
                                .c-sbcpbscii-circle {
                                    background-color: base.$color-green-light-1;
                                    border-color: base.$color-green-light-1;
                                    cursor: pointer;
                                }
                                    .c-sbcpbsciic-icon {
                                        display: block;
                                    }
                                    .c-sbcpbsciic-number {
                                        display: none;
                                    }
                                .c-sbcpbscii-label {
                                    color: base.$color-grey-dark-4;
                                    cursor: pointer;
                                }
                            }
                            &.t-skipped {
                                .c-sbcpbscii-circle {
                                    background-color: base.$color-grey-light-2;
                                    border-color: base.$color-grey-light-2;
                                    cursor: pointer;
                                }
                                .c-sbcpbsciic-number {
                                    color: base.$color-white-default;
                                }
                                .c-sbcpbscii-label {
                                    color: base.$color-grey-dark-4;
                                    cursor: pointer;
                                }
                            }
                            &.t-first {
                                min-height: base.unit-rem-calc(32px);
                                margin-bottom: base.unit-rem-calc(16px) !important;
                                padding: 0 base.unit-rem-calc(8px);
                                gap: base.unit-rem-calc(23px) !important;
                                .c-sbcpbscii-label {
                                    margin-left: 0 !important;
                                    cursor: pointer;
                                }
                                .c-sbcpbscii-back {
                                    @include base.svg-icon('default-18');
                                    color: base.$color-primary-default;
                                }
                            }
                            &.t-last {
                                padding-bottom: base.unit-rem-calc(16px);
                                &.t-hidden {
                                    display: none !important;
                                }
                            }
                            &.t-active {
                                .c-sbcpbscii-circle {
                                    background-color: base.$color-primary-default;
                                    border-color: base.$color-primary-default;
                                    cursor: pointer;
                                }
                                .c-sbcpbsciic-number {
                                    color: base.$color-white-default;
                                }
                                .c-sbcpbscii-label {
                                    color: base.$color-grey-dark-4;
                                    cursor: pointer;
                                }
                            }
                            &.t-link {
                                display: flex;
                                align-items: center;
                                gap: 0;
                                padding-bottom: 0;
                                &.t-last {
                                    padding-bottom: base.unit-rem-calc(16px);
                                }
                                .c-sbcpbscii-circle {
                                    background-color: base.$color-white-default;
                                    border-color: base.$color-primary-default;
                                    cursor: pointer;
                                }
                                .c-sbcpbsciic-number {
                                    color: base.$color-primary-default;
                                }
                                .c-sbcpbscii-label {
                                    color: base.$color-primary-default;
                                    flex: 0 0 auto;
                                    margin-left: base.unit-rem-calc(16px);
                                    cursor: pointer;
                                }
                            }
                        }
                            .c-sbcpbscii-button {
                                display: flex;
                                flex: 1;
                                height: base.unit-rem-calc(40px);
                                border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                                background: base.$color-white-default;
                                border-radius: base.unit-rem-calc(40px);
                                box-shadow: base.$elevation-secondary-base;
                                margin: 0 base.unit-rem-calc(-8px);
                                padding: base.unit-rem-calc(10px) base.unit-rem-calc(10px) base.unit-rem-calc(10px) base.unit-rem-calc(12px);
                                justify-content: space-between;
                                transition: all 0.3s ease-in-out;
                                @include base.respond-to('hover') {
                                    &:hover {
                                        box-shadow: base.$elevation-primary-hover;
                                        background: base.$color-primary-light-1;
                                        border-color: transparent;
                                        .c-sbcpbsciib-arrow,
                                        .c-sbcpbsciibr-view,
                                        .c-sbcpbsciibr-label {
                                            color: base.$color-white-default;
                                        }
                                        .c-sbcpbsciib-arrow {
                                            animation: arrow-pulse-right 1000ms ease-in-out infinite;
                                        }
                                    }
                                }
                                .c-sbcpbsciib-review {
                                    display: flex;
                                    gap: base.unit-rem-calc(12px);
                                }
                                    .c-sbcpbsciib-arrow,
                                    .c-sbcpbsciibr-view,
                                    .c-sbcpbsciibr-label {
                                        color: base.$color-primary-default;
                                        transition: all 0.3s ease-in-out;
                                    }
                                    .c-sbcpbsciib-arrow,
                                    .c-sbcpbsciibr-view {
                                        @include base.svg-icon('default-18');
                                    }
                                    .c-sbcpbsciibr-label {
                                        margin-left: 0;
                                        line-height: base.unit-rem-calc(18px);
                                    }
                            }
                            .c-sbcpbscii-circle {
                                flex: 0 0 auto;
                                width: base.unit-rem-calc(32px);
                                height: base.unit-rem-calc(32px);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                border-radius: base.unit-rem-calc(32px);
                                border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                                background-color: base.$color-white-default;
                                cursor: not-allowed;
                            }
                                .c-sbcpbsciic-icon {
                                    @include base.svg-icon('default-18');
                                    color: base.$color-white-default;
                                    height: base.unit-rem-calc(13px);
                                    width: base.unit-rem-calc(13px);
                                    display: none;
                                }
                                .c-sbcpbsciic-number {
                                    color: base.$color-grey-light-3;
                                    font-weight: 500;
                                    line-height: base.unit-rem-calc(20px);

                                }
                            .c-sbcpbscii-label {
                                flex: 1;
                                color: base.$color-grey-light-3;
                                cursor: not-allowed;
                            }

                        .c-sbcpbsci-line {
                            flex: 1;
                            border-left: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                            min-height: base.unit-rem-calc(24px);
                            margin-left: base.unit-rem-calc(15px);
                            &.t-complete {
                                border-color: base.$color-green-light-1;
                            }
                        }
        .c-sbc-save {
            padding: base.unit-rem-calc(12px) base.unit-rem-calc(8px) base.unit-rem-calc(12px) 0;
            grid-area: 2 / 1 / 3 / 2;
            @include base.respond-to('<medium') {
                display: none;
            }
        }
        .c-sbc-steps {
            height: 100%;
            overflow: auto;
            padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) 0;
            border-width: base.unit-rem-calc(1px) 0 0 base.unit-rem-calc(1px);
            border-style: solid;
            border-color: base.$color-grey-light-4;
            grid-area: 1 / 2 / 3 / 3;
            @include base.respond-to('<medium') {
                border: none;
            }
            @include base.respond-to('<small') {
                padding: base.unit-rem-calc(16px);
            }
        }
            .c-sbcs-step {
                display: flex;
                flex-direction: column;
                flex: 1;
                gap: base.unit-rem-calc(32px);
                width: 100%;
                margin-bottom: base.unit-rem-calc(48px);
                &:has(.m-sb-table, .t-bid, .t-terms, .t-warranty) {
                    height: 100%;
                    margin-bottom: 0;
                }
                &.t-hidden {
                    display: none;
                }
                &.t-flex {
                    display: flex;
                }
            }
                .c-sbcss-header {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    &.t-general {
                        margin-bottom: base.unit-rem-calc(16px)
                    }
                    &.t-integrations {
                        justify-content: flex-start;
                        align-items: center;
                    }
                }
                    .c-sbcssh-error {
                        width: 100%;
                        @include base.callout-error;
                        margin-bottom: base.unit-rem-calc(24px);
                        display: none;
                        &.t-show {
                            display: flex;
                        }
                    }
                    .c-sbcssh-title {
                        flex: 0 0 auto;
                        margin-bottom: base.unit-rem-calc(8px);
                    }
                    .c-sbcssh-status {
                        margin-left: base.unit-rem-calc(4px);
                        margin-bottom: 6px;
                        height: base.unit-rem-calc(24px);
                        display: none;
                        > [data-icon] {
                            @include base.svg-icon('default-18');
                            color: base.$color-green-light-1;
                        }
                        &.t-negate {
                            display: none;
                            background-color: base.$color-red-default;
                            padding: base.unit-rem-calc(6px) base.unit-rem-calc(16px) !important;
                            border-radius: base.unit-rem-calc(200px);
                            color: base.$color-white-default;
                        }
                        &.t-show {
                            display: inline-flex;
                            align-items: center;
                        }
                    }
                    .c-sbcssh-instruction {
                        flex: 0 0 100%;
                        @include base.typo-paragraph;
                        margin-bottom: 0;
                        .t-learn {
                            font-size: base.unit-rem-calc(12px);
                            color: base.$color-grey-light-1;
                        }
                    }
                    .c-sbcssh-button {
                        flex: 0 0 auto;
                        @include base.button-text-primary;
                        &.t-save {
                            &.t-hide {
                                display: none;
                            }
                        }
                        &.t-saving {
                            display: none;
                            &.t-show {
                                display: block;
                            }
                        }
                        &.t-saved {
                            background-color: base.$color-green-light-1 !important;
                            border-color: base.$color-green-light-1 !important;
                            display: none;
                            &.t-show {
                                display: block;
                            }
                        }
                        @include base.respond-to('<small') {
                            margin-bottom: base.unit-rem-calc(16px);
                        }
                    }
                .c-sbcss-content {
                    margin: auto;
                    &.t-disconnect,
                    &.t-integrations,
                    &.t-google-connected {
                        .c-sbcssc-button {
                            width: fit-content;
                        }
                    }
                    &.t-disconnect {
                        display: flex;
                        justify-content: flex-end;
                        padding: base.unit-rem-calc(16px) 0 0;
                    }
                    &.t-google-connected {
                        display: flex;
                        justify-content: flex-end;
                    }
                }
                    .c-sbcssc-button {}
                        .c-sbcsscb-link {
                            @include base.button-text-primary;
                            &.t-negate {
                                @include base.button-text-primary($style: 'negate');
                            }
                        }

        .c-sbc-mobile-progress {
            display: none;
            padding: base.unit-rem-calc(12px) base.unit-rem-calc(16px);
            border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
            @include base.respond-to('<medium') {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
        }
            .c-sbcmp-save {
                @include base.button-text-tertiary;
            }
            .c-sbcmp-step-title {
                @include base.typo-paragraph;
                margin-bottom: 0;
                color: base.$color-grey-light-1;
                text-align: right;
            }
}
$general-divider: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
.m-form {
    display: flex;
    flex-direction: column;
    height: 100%;
    &.t-general {
        .c-f-row {
            height: auto;
            &:last-child {
                margin-top: base.unit-rem-calc(16px);
            }
        }
    }
    &.t-bid {
        .c-f-row {
            flex: 1;
            .c-fr-field {
                margin-bottom: base.unit-rem-calc(48px);
            }
        }
    }
    &.t-terms {
        .c-f-row {
            flex: 1;
            .c-fr-field {
                margin-bottom: base.unit-rem-calc(48px);
            }
        }
    }
    &.t-warranty {
        .c-f-row {
            flex: 1;
            .c-fr-field {
                margin-bottom: base.unit-rem-calc(48px);
            }
        }
    }
    &.t-quickbooks {
        display: flex;
        flex-wrap: wrap;
        gap: base.unit-rem-calc(16px);
        .c-f-row {
            width: 100%;
        }
    }
    &.t-products {
        column-gap: base.unit-rem-calc(16px);
        row-gap: base.unit-rem-calc(8px);
        .c-f-row {
            row-gap: base.unit-rem-calc(8px);
        }
    }
    .c-f-instructions {
        @include base.callout-warning;
        margin: base.unit-rem-calc(16px) 0;
    }
    .c-f-row {
        display: flex;
        gap: base.unit-rem-calc(16px);
        &.t-section {
            margin-bottom: base.unit-rem-calc(32px);
        }
        &.t-ai-flex-end {
            align-items: flex-end;
            &.t-center {
                align-items: center;
            }
            @include base.respond-to('<medium') {
                flex-direction: row;
                gap: base.unit-rem-calc(24px);
            }
            @include base.respond-to('<400') {
                flex-direction: column;
            }
        }
        &.t-logo-colors {
            gap: 0;
            border-top: $general-divider;
            border-bottom: $general-divider;
            padding: base.unit-rem-calc(8px) 0;
            margin-bottom: base.unit-rem-calc(32px);
            flex-direction: row;
            @include base.respond-to('<1155') {
                flex-direction: column;
            }
        }
        &:last-of-type {
            margin-bottom: 0;
        }
        @include base.respond-to('<medium') {
            flex-direction: column;
            margin-bottom: 0;
        }
    }
        .c-fr-field {
            &.t-full {
                flex: 1;
            }
            &.t-half {
                flex: 0 0 50%;
                padding-right: base.unit-rem-calc(12px);
                @include base.respond-to('<medium') {
                    padding-right: 0;
                }
            }
            &.t-auto {
                flex: 0 0 auto;
            }
            &.t-url {
                padding-bottom: 0;
                @include base.respond-to('<medium') {
                    margin-bottom: 0;
                }
                @include base.respond-to('<400') {
                    width: 100%;
                }
            }
            // wysiwyg overrides
            .tox-tinymce {
                height: 100% !important;
                .tox-sidebar-wrap {
                    min-height: base.unit-rem-calc(256px);
                }
            }
            &.t-button-icon-text-primary {
                @include base.button-icon-primary;
                @include base.respond-to('<medium') {
                    margin-bottom: 0;
                }
            }
            &.t-button-text-primary {
                @include base.button-text-primary;
                &.t-hidden {
                    display: none;
                }
                &.t-green {
                    background-color: base.$color-green-default;
                    color: base.$color-white-default;
                    display: none;
                    @include base.respond-to('hover') {
                        &:hover {
                            background-color: base.$color-green-default;
                            color: base.$color-white-default;
                        }
                    }
                    &.t-show {
                        display: block;
                    }
                }
            }
            &.t-logo {
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(20px) base.unit-rem-calc(24px) 0;
                @include base.respond-to('<small') {
                    border-right: none;
                    padding: 0 0 base.unit-rem-calc(16px) 0;
                }
            }
            &.t-colors {
                padding: base.unit-rem-calc(16px) 0 base.unit-rem-calc(24px) base.unit-rem-calc(20px);
                @include base.respond-to('<1155px') {
                    padding: base.unit-rem-calc(16px) 0 base.unit-rem-calc(24px);
                }
                @include base.respond-to('<small') {
                    padding: 0 0 base.unit-rem-calc(16px) 0;
                }
            }
        }
            .c-frf-learn {
                float: right;
            }
            .c-frf-label {
                @include base.form-input-label;
            }
            .c-frf-tag {
                background-color: base.$color-primary-light-1;
                padding: base.unit-rem-calc(2px) base.unit-rem-calc(16px);
                border-radius: base.unit-rem-calc(100px);
                color: base.$color-white-default;
                font-size: base.unit-rem-calc(14px);
            }
            .c-frf-none {
                @include base.typo-paragraph-small;
                margin-bottom: 0;
                font-style: italic;
            }
            .c-frf-instructions {
                @include base.typo-paragraph;
                margin-bottom: 0;
            }
            .c-frf-logo-container {
                display: flex;
                width: base.unit-rem-calc(386px);
                align-items: flex-end;
                margin-top: base.unit-rem-calc(24px);
                flex-wrap: wrap;
                @include base.respond-to('<432px') {
                    width: base.unit-rem-calc(322px);
                }
                @include base.respond-to('<370px') {
                    width: auto;
                }
            }
                .c-frflc-placeholder {
                    width: base.unit-rem-calc(126px);
                    height: base.unit-rem-calc(126px);
                    border: base.unit-rem-calc(1px) dashed base.$color-grey-light-3;
                    margin-right: base.unit-rem-calc(24px);
                    border-radius: base.unit-rem-calc(8px);
                    padding: base.unit-rem-calc(8px);
                    &.t-filled {
                        display: flex;
                        align-items: center;
                        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                    }
                    &.t-hidden {
                        display: none;
                    }
                }
                .c-frflc-options-placeholder {
                    display: none;
                    gap: base.unit-rem-calc(16px);
                    justify-content: space-between;
                    padding: base.unit-rem-calc(8px);
                    border-radius: base.unit-rem-calc(14px);
                    background: base.$color-background-form;
                    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                    &.t-show {
                        display: flex;
                    }
                    @include base.respond-to('<370px') {
                        flex-wrap: wrap;
                    }
                }
                    .c-frflcop-option {
                        display: flex;
                        padding: base.unit-rem-calc(4px);
                        align-items: center;
                        justify-content: center;
                        width: base.unit-rem-calc(80px);
                        height: base.unit-rem-calc(80px);
                        background-color: base.$color-white-default;
                        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                        border-radius: base.unit-rem-calc(8px);
                        box-shadow: base.$elevation-level-1;
                        @include base.respond-to('hover') {
                            &:hover {
                                border: base.unit-rem-calc(2px) solid base.$color-primary-light-1;
                                box-shadow: base.$elevation-level-3;
                            }
                        }
                        &.t-active {
                            border: base.unit-rem-calc(1px) solid base.$color-primary-light-1;
                            box-shadow: 0 0 0 base.unit-rem-calc(4px) base.$color-primary-light-3;
                        }
                        & > svg {
                            color: base.$color-grey-dark-4;
                        }
                        & > img {
                            max-height: 100%;
                            max-width: 100%;
                        }
                        @include base.respond-to('<432px') {
                            width: base.unit-rem-calc(64px);
                            height: base.unit-rem-calc(64px);
                        }
                    }

                .c-frflc-error {
                    display: none;
                    flex: 0 0 100%;
                    margin-bottom: base.unit-rem-calc(8px);
                    @include base.typo-paragraph;
                    color: base.$form-error-text-color;
                    &.t-show {
                        display: block;
                    }
                }
                .c-frflc-details {
                    display: none;
                    flex: 0 0 100%;
                    @include base.typo-paragraph-small;
                    color: base.$color-grey-light-2;
                    font-style: italic;
                    padding: base.unit-rem-calc(14px) 0 0 base.unit-rem-calc(8px);
                    &.t-show {
                        display: block;
                    }
                }
                .c-frflc-button {
                    margin-top: base.unit-rem-calc(24px);
                    @include base.button-icon-text-primary;
                }
                .c-frflc-result-buttons {
                    display: none;
                    &.t-show {
                        display: flex;
                        justify-content: flex-start;
                        position: relative;
                        width: 100%;
                        gap: base.unit-rem-calc(16px);
                        padding: 0 0 base.unit-rem-calc(8px) base.unit-rem-calc(8px);
                    }
                }
                    .c-frflcrb-clear {
                        @include base.button-text-tertiary($style: 'negate');
                    }
                    .c-frflcrb-more {
                        @include base.button-text-primary($rounded: 'true');
                        position: absolute;
                        top: base.unit-rem-calc(148px);
                        right: 0;
                        @include base.respond-to('<432px') {
                            top: base.unit-rem-calc(132px);
                        }
                        @include base.respond-to('<370px') {
                            top: base.unit-rem-calc(210px);
                        }
                    }
            .c-frf-no-results {
                margin-top: base.unit-rem-calc(16px);
                display: none;
                font-style: italic;
                color: base.$color-red-default;
            }
    .c-f-settings {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: auto;
        gap: base.unit-rem-calc(8px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        background: base.$color-background-form;
        border-radius: base.unit-rem-calc(8px);
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
        width: 100%;
        @include base.respond-to('<900px') {
            grid-template-columns: 1fr;
        }
    }
            .c-fs-field {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(16px);
            }
                &.t-switch {
                    display: flex;
                    align-items: center;
                    .t-switch {
                        flex: 1;
                    }
                }
}
.m-users-table {
    border-radius: base.unit-rem-calc(8px);
    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
    background: base.$color-background-edit;
    .c-ut-submit {
        display: flex;
        justify-content: flex-end;
        padding: base.unit-rem-calc(16px);
    }
        .c-uts-button {
            @include base.button-text-icon-primary;
            &.t-add {
                display: none;
                margin-right: base.unit-rem-calc(16px);
            }
        }
}
.m-line-items {
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
    .c-li-head {
        background: transparent;
        margin-top: base.unit-rem-calc(8px);
        @include base.respond-to('<928px') {
            display: none;
        }
    }
        .c-lih-header-row {
            display: grid;
            grid-template-columns: 1fr base.unit-rem-calc(88px) base.unit-rem-calc(88px) base.unit-rem-calc(88px) base.unit-rem-calc(88px);
            gap: base.unit-rem-calc(16px);
            padding: 0 base.unit-rem-calc(16px);
            border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        }
            .c-lihhr-header {
                padding: 0;
                &:first-child {
                        flex: 1;
                }
            }
                .c-lihhr-data {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: base.unit-rem-calc(4px);
                    &.t-left {
                        justify-content: flex-start;
                    }
                }
                    .c-lihhrd-label {
                        font-family: base.$typo-font-family-header;
                        font-size: base.unit-rem-calc(14px);
                        line-height: base.unit-rem-calc(32px);
                        font-weight: 600;
                    }
    .c-li-body {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
        padding: base.unit-rem-calc(16px);
        border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        background-color: transparent;
        min-height: base.unit-rem-calc(32px);
        @include base.respond-to('<928px') {
            padding: 0;
            gap: 0;
        }
    }
        .c-lib-row {
            display: grid;
            grid-template-columns: 1fr base.unit-rem-calc(88px) base.unit-rem-calc(88px) base.unit-rem-calc(88px) base.unit-rem-calc(88px);
            gap: base.unit-rem-calc(16px);
            background-color: transparent;
            @include base.respond-to('<928px') {
                grid-template-columns: repeat(2, 1fr);
                gap: base.unit-rem-calc(8px);
                padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                &:last-child {
                    border-bottom: none;
                }
            }
            @include base.respond-to('<320px') {
                display: flex;
                flex-direction: column;
            }
        }
            .c-libr-data {
                padding: 0;
                &.t-email {
                    flex: 1;
                }
            }
        @include base.respond-to('<928px') {
            .c-libr-data {
                display: flex;
                align-items: flex-start;
                gap: base.unit-rem-calc(8px);
                @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
                &.t-email {
                    grid-column: 1/3;
                    flex-direction: column;
                    gap: 0;
                    .f-field {
                        width: 100%;
                    }
                }
                &.t-admin {
                    grid-column: 1/2;
                }
                &.t-schedule {
                    grid-column: 2/3;
                }
                &.t-sales {
                    grid-column: 1/2;
                }
                &.t-install {
                    grid-column: 2/3;
                }
            }
        }
            .f-f-label {
                display: none;
                @include base.respond-to('<928px') {
                    display: flex;
                    align-items: center;
                    gap: base.unit-rem-calc(4px);
                }
            }

            .c-libr-no-data {
                margin: auto;
                padding: base.unit-rem-calc(16px);
                text-align: center;
            }
}

.m-user-invite-success {
    display: none;
    margin: auto;
    width: base.unit-rem-calc(520px);
    flex-direction: column;
    align-items: center;
    &.t-show {
        display: flex;
    }
    .c-uis-check {
        width: base.unit-rem-calc(500px);
        height: base.unit-rem-calc(200px);
        @include base.respond-to('<550') {
            width: base.unit-rem-calc(400px);
            height: base.unit-rem-calc(175px);
        }
        @include base.respond-to('<450') {
            width: base.unit-rem-calc(300px);
            height: base.unit-rem-calc(120px);
        }
        @include base.respond-to('<350') {
            width: base.unit-rem-calc(200px);
            height: base.unit-rem-calc(85px);
        }
    }
    .c-uis-title {
        flex: 1;
        @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
        color: base.$color-green-default;
        text-align: center;
        @include base.respond-to('<550') {
            @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(40px));
        }
        @include base.respond-to('<450') {
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(40px));
        }
    }
}

.m-email-container {
    .c-ec-accordion {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
        overflow: hidden;
        width: 100%;
    }

        .c-eca-item {
            border: base.unit-rem-calc(1px) solid #E1E8F060;
            border-radius: base.unit-rem-calc(8px);
            overflow: hidden;
            background: base.$color-background-form;
            transition: all 0.3s ease-in-out;
            &.t-active {
                border-color: base.$color-grey-light-4;
                background-color: base.$color-background-edit;
                .c-ecai-title-wrapper {
                    background-color: base.$color-primary-light-4;
                    color: base.$color-primary-default;
                }
                .c-ecaitw-icon {
                    > svg {
                        color: base.$color-primary-default;
                        transform: rotate(180deg);
                    }
                }
            }
        }
            .c-ecai-title-wrapper {
                display: flex;
                align-items: center;
                height: base.unit-rem-calc(38px);
                padding-left: base.unit-rem-calc(12px);
                padding-bottom: base.unit-rem-calc(1px);
            }
                .c-ecaitw-title {
                    flex: 1;
                    cursor: pointer;
                }
                .c-ecaitw-icon {
                    width: base.unit-rem-calc(40px);
                    height: base.unit-rem-calc(24px);
                    cursor: pointer;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    > svg {
                        @include base.svg-icon('default-24');
                        color: base.$color-grey-light-2;
                        transition: transform 0.3s ease-in-out;
                    }
                }
            .c-ecai-content {
                display: none;
                padding: base.unit-rem-calc(16px);
                background: base.$color-background-edit;
            }
                .c-ecaic-instructions {
                    @include base.typo-paragraph;
                }
                .c-ecaic-switch {
                    display: flex;
                    align-items: center;
                    gap: base.unit-rem-calc(16px);
                    margin-top: base.unit-rem-calc(16px);
                }
                .c-ecaic-warning,
                .c-ecaic-error {
                    width: 100%;
                    margin: base.unit-rem-calc(16px) 0;
                    display: none;
                    &.t-show {
                        display: block;
                    }
                }
                .c-ecaic-warning {
                    @include base.callout-warning;
                }
                .c-ecaic-error {
                    @include base.callout-error;
                }
                .c-ecaic-button {
                    display: flex;
                    justify-content: flex-end;
                    margin-top: base.unit-rem-calc(16px);
                }
}

.m-color-container {
    display: flex;
    justify-content: center;
    gap: base.unit-rem-calc(16px);
    margin-top: base.unit-rem-calc(40px);
    @include base.respond-to('<1155px') {
        justify-content: flex-start;
        margin-top: base.unit-rem-calc(32px);
    }
    @include base.respond-to('<xsmall') {
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
    }
    .pickr {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
    }
    .c-cc-item,
    .pcr-button, {
        flex: 1;
        height: base.unit-rem-calc(64px);
        border-radius: base.unit-rem-calc(100px);
        padding: 0;
        z-index: 0;
        &::before,
        &::after {
            border-radius: base.unit-rem-calc(100px);
            border: base.unit-rem-calc(2px) solid rgba(0, 0, 0, 0.05);
            background-size: 0;
        }
    }
    .c-cci-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .c-cciw-loader {
        display: none;
        position: absolute;
        width: base.unit-rem-calc(124px);
        height: base.unit-rem-calc(124px);
        background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
        background-size: base.unit-rem-calc(64px) base.unit-rem-calc(64px);
        z-index: 120;
    }

    .c-cciw-title {
        @include base.typo-paragraph-medium;
        color: base.$color-white-default;
        z-index: 100;
        display: none;
        &.t-dark {
            color: base.$color-grey-dark-4;
        }
        &.t-loader {
            color: base.$color-grey-light-4;
        }
    }

    .c-cciw-button {
        @include base.button-text-icon-tertiary;
        color: base.$color-white-default !important;
        z-index: 100;
        outline: 0;

        &.t-dark {
            color: base.$color-grey-dark-4 !important;
        }

        &.t-loader {
            color: base.$color-grey-light-4 !important;
        }
    }
}
.m-sb-table {
    height: 100%;
    padding-bottom: base.unit-rem-calc(24px);
    &.t-media {
        [class^="icon-"], [class*=" icon-"] {
            /* use !important to prevent issues with browser extensions that change fonts */
            font-family: 'icomoon' !important;
            speak: none;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;

            /* Better Font Rendering =========== */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .media-icon {
            display: inline;
            font-size: base.unit-rem-calc(16px);
            &.icon-file-empty:before {
                content: "\e924";
            }
            &.icon-file-picture:before {
                content: "\e927";
            }
            &.icon-file-music:before {
                content: "\e928";
            }
            &.icon-file-video:before {
                content: "\e92a";
            }
            &.icon-file-zip:before {
                content: "\e92b";
            }
            &.icon-circle-down:before {
                content: "\ea43";
            }
            &.icon-file-pdf:before {
                content: "\eadf";
            }
            &.icon-file-word:before {
                content: "\eae1";
            }
            &.icon-file-excel:before {
                content: "\eae2";
            }
        }
    }
    &.t-google {
        margin: base.unit-rem-calc(16px) 0;
        display: none;
        height: unset;
        .dataTables_scrollBody {
            min-height: base.unit-rem-calc(48px);
        }
    }
    &.t-products {
        thead {
            // units
            th:nth-last-child(2) {
                @include base.respond-to('<small') {
                    display: none !important;
                }
            }
        }
        tbody {
            // units
            td:nth-last-child(2) {
                @include base.respond-to('<small') {
                    display: none !important;
                }
            }
        }
        .m-table-edit {
            .m-form {
                display: flex;
                flex-direction: column;
                row-gap: base.unit-rem-calc(8px);
                .c-f-row {
                    column-gap: base.unit-rem-calc(16px);
                    row-gap: base.unit-rem-calc(8px);
                    .c-fr-field {
                        &:last-child {
                            display: flex;
                            flex-wrap: wrap;
                            .f-f-label {
                                width: 100%;
                            }
                            .c-frf-tag {
                                max-width: max-content;
                                margin-right: base.unit-rem-calc(4px);
                                margin-bottom: base.unit-rem-calc(4px);
                            }
                        }
                    }
                }
            }
        }
    }
    .c-t-footer{
        @include base.respond-to('<small') {
            padding: base.unit-rem-calc(12px) base.unit-rem-calc(8px);
        }
    }
    .dataTables_scrollBody {
        min-height: base.unit-rem-calc(256px);
    }
    .c-t-table-wrapper {
        @include base.respond-to('<small') {
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(8px);
        }
    }
}
.m-additional-services {
    .c-as-card {
        border-radius: base.unit-rem-calc(12px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        outline: 0 solid transparent;
        box-shadow: base.$elevation-secondary-base;
        display: flex;
        padding: base.unit-rem-calc(16px);
        gap: base.unit-rem-calc(16px);
        margin-bottom: base.unit-rem-calc(24px);
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease-in-out;
        &:last-of-type {
            margin-bottom: 0;
        }
        @include base.respond-to('hover') {
            &:hover {
                background-color: base.$color-primary-light-4;
                border-color: base.$color-primary-light-1;
                box-shadow: base.$elevation-secondary-hover;
                .c-asc-icon {
                    color: base.$color-primary-default;
                }
                .c-asc-content {
                    color: base.$color-primary-default;
                }
                .c-asc-checkbox {
                    border-color: base.$color-primary-default;
                    color: base.$color-primary-default;
                    .c-ascc-icon.t-plus {
                        color: base.$color-primary-default;
                    }
                }
            }
        }
        &.t-active {
            outline: base.unit-rem-calc(2px) solid base.$color-green-light-1;
            border-color: transparent;
            box-shadow: none;
            @include base.respond-to('hover') {
                background: none;
            }
            .c-asc-icon {
                color: base.$color-green-default;
            }
            .c-ascc-title {
                color: base.$color-green-default;
            }
            .c-ascc-description {
                color: base.$color-green-default;
            }
            .c-asc-checkbox {
                border-color: transparent;
                background: base.$color-green-light-1;
                .c-ascc-icon {
                    color: base.$color-white-default;
                }
            }
            .c-ascc-icon {
                color: base.$color-green-default;
                &.t-plus {
                    display: none;
                }
                &.t-check {
                    display: block;
                }
            }
        }
        @include base.respond-to('<small') {
            flex-direction: column;
            align-items: center;
            gap: base.unit-rem-calc(8px);
        }
    }
        .c-asc-icon {
            flex: 0 0 auto;
            margin: auto 0;
            @include base.svg-icon('default-40');
            color: base.$color-grey-light-1;
            transition: all 0.3s ease-in-out;
            @include base.respond-to('<small') {
                margin-top: base.unit-rem-calc(24px);
            }
        }
        .c-asc-content {
            padding-right: base.unit-rem-calc(48px);
            transition: all 0.3s ease-in-out;
            @include base.respond-to('<small') {
                padding-right: 0;
            }
        }
            .c-ascc-title {
                @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
                @include base.respond-to('<small') {
                    margin-bottom: base.unit-rem-calc(8px);
                    text-align: center;
                }
            }
            .c-ascc-description {
                @include base.typo-paragraph-small;
                margin-bottom: 0;
            }
        .c-asc-checkbox {
            display: flex;
            justify-content: center;
            align-items: center;
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-3;
            border-radius: base.unit-rem-calc(100px);
            background: base.$color-white-default;
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            position: absolute;
            right: base.unit-rem-calc(16px);
            top: base.unit-rem-calc(16px);
            transition: all 0.3s ease-in-out;
        }
            .c-ascc-icon {
                @include base.svg-icon('default-18');
                color: base.$color-grey-light-2;
                transition: all 0.3s ease-in-out;
                &.t-plus {
                    display: block;
                }
                &.t-check {
                    display: none;
                }
            }
}

// Pickr style overrides
.pcr-app {
    width: auto !important;
    background: base.$color-white-default;
    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
    box-shadow: base.$elevation-level-3;
    border-radius: base.unit-rem-calc(12px);
    &.t-swatches {
        .pcr-swatches {
            margin-top: 0 !important;
        }
    }
    .pcr-selection {
        display: none !important;
        &.t-show {
            display: flex !important;
        }
    }
    .pcr-color-palette {
        .pcr-palette {
            border-radius: base.unit-rem-calc(3px) !important;
            &:before {
                border-radius: base.unit-rem-calc(3px) !important;
                background-size: 0 !important;
            }
        }
    }
    .pcr-swatches {
        margin: base.unit-rem-calc(16px) 0 0 0 !important;
        grid-template-columns: repeat(8, 1fr) !important;
        gap: base.unit-rem-calc(6px);
        & > button {
            margin: 0 !important;
            width: base.unit-rem-calc(24px);
            height: base.unit-rem-calc(24px);
            border-radius: base.unit-rem-calc(24px) !important;
            transition: all 0.3s ease-in-out;
            &::before {
                background-size: base.unit-rem-calc(24px) !important;
                border-radius: base.unit-rem-calc(24px) !important;
            }
            &::after {
                background-size: base.unit-rem-calc(24px) !important;
                border-radius: base.unit-rem-calc(24px) !important;
            }
            &:hover {
                scale: 1.2;
            }
            &.pcr-active {
                &:hover {
                    scale: unset;
                }
            }
        }
    }
    .pcr-cancel {
        flex: 0 0 97%;
        background-color: base.$color-primary-default !important;
        border-radius: base.$prop-border-radius !important;
        box-shadow: base.$elevation-level-2 !important;
        color: base.$color-white-default !important;
        border: base.unit-rem-calc(1px) solid base.$color-primary-default !important;
        text-transform: uppercase;
        font-family: "Barlow", "Roboto", Arial, sans-serif;
        height: base.unit-rem-calc(32px);
        font-size: base.unit-rem-calc(14px) !important;
        font-weight: 600;
        display: none !important;
        margin-top: base.unit-rem-calc(24px) !important;
        @include base.respond-to('hover') {
            &:hover {
                background-color: base.$color-primary-light-1 !important;
                filter: unset !important;
                border-color: base.$color-primary-light-1 !important;
                color: base.$color-white-default !important;
                box-shadow: base.$elevation-level-3 !important;
            }
        }
        &.t-show {
            display: inline-block !important;
        }
    }
    .pcr-result {
        flex: 1 1 auto !important;
        border: 1px solid base.$color-grey-light-3;
        background-color: base.$color-white-default !important;
        color: base.$color-grey-dark-4 !important;
        height: base.unit-rem-calc(32px);
        font-family: 'Roboto', sans-serif;
        font-size: base.unit-rem-calc(14px) !important;
        border-radius: base.$prop-border-radius !important;
        letter-spacing: unset !important;
        text-align: center !important;
        display: none !important;
        box-shadow: none;
        @include base.respond-to('hover') {
            &:hover {
                filter: unset !important;
            }
        }
        &:active,
        &:focus {
            border-color: base.$form-input-active-border-color;
            box-shadow: 0 0 0 base.unit-rem-calc(4px) base.$form-input-active-drop-shadow-color !important;
        }
        &.t-show {
            display: inline-block !important;
        }
    }
}

// keyframes
@keyframes arrow-pulse-right {
    50% {
        transform: translateX(-3px);
    }
}
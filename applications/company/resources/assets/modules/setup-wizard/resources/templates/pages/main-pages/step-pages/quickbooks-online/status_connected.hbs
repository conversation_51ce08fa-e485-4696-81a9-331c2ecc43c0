<div class="m-connected">
    <form class="m-form t-quickbooks" data-js="form">
        <div class="c-f-row">
            <div class="c-fr-field t-half f-field">
                <label class="f-f-label">Default Service For QuickBooks Invoice</label>
                <select class="f-f-input" required name="default_service_id" data-js="default_service">
                    <option value="">-- Select One --</option>
                </select>
            </div>
        </div>
        <div class="c-f-settings">
            <div class="c-fs-field f-field t-switch">
                <input class="f-f-input" type="checkbox" id="use-quickbooks-invoice" data-fx-form-input="switch" data-js="use_quickbooks_invoice">
                <label for="use-quickbooks-invoice" class="f-f-label t-switch">Use QuickBooks Invoice</label>
            </div>
            <div class="c-fs-field f-field t-switch">
                <input class="f-f-input" type="checkbox" id="allow-ach" data-fx-form-input="switch" data-js="allow_ach">
                <label for="allow-ach" class="f-f-label t-switch">Show QuickBooks ACH Link</label>
            </div>
            <div class="c-fs-field f-field t-switch">
                <input class="f-f-input" type="checkbox" id="allow-credit-card" data-fx-form-input="switch" data-js="allow_credit_card">
                <label for="allow-credit-card" class="f-f-label t-switch">Show QuickBooks Credit Card Link</label>
            </div>
        </div>
    </form>
    <div class="c-sbcss-content t-disconnect">
        <div class="c-sbcssc-button">
            <a class="c-sbcsscb-link t-negate" href="{{link}}">Disconnect</a>
        </div>
    </div>
</div>
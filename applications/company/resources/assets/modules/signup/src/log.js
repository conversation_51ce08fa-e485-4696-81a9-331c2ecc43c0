'use strict';

import Logger from '@ca-package/log';
import <PERSON>sole<PERSON>and<PERSON> from '@ca-package/log/src/handlers/console';
import RemoteHandler from '@ca-package/log/src/handlers/remote';

const logger = new Logger('signup');

logger.pushHandler(new ConsoleHandler({
    min_level: Logger.Level.ERROR
}));

let remote_handler = new RemoteHandler({
    min_level: Logger.Level.WARNING
});
remote_handler.pushProcessor((record) => {
    record.extra.url = window.location.href;
    record.extra.user_agent = window.navigator.userAgent;
    return record;
});
logger.pushHandler(remote_handler);

export default logger;

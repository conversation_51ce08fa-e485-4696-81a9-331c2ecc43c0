'use strict';

const Component = require('@ca-package/router/src/component');

const LegacyCosts = require('../legacy/costs');

const costs_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/costs.hbs');

class Costs extends Component {
    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        await super.load(request);

        LegacyCosts.get({
            id: request.params.project_id
        });
    };

    /**
     * Boot component
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        LegacyCosts.setup();
        e$(this.elem.root[0]).find('[data-reveal]').foundation();
    };

    /**
     * Render component
     */
    render() {
        return costs_tpl();
    };
}

module.exports = Costs;

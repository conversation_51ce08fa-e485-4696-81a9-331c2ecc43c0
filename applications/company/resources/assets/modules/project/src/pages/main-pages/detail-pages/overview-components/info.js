'use strict';

const moment = require('moment-timezone');

const Api = require('../../../../api');

const Component = require('@ca-package/router/src/component');

const info_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/info.hbs');
const details_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/info/details.hbs');

class Info extends Component {
    /**
     * Get or set project modal
     *
     * @returns {object}
     */
    get project_modal() {
        if (this.state.project_modal === undefined) {
            let modal = require('@cam-project-js/modals/main/project/overview/project');
            this.state.project_modal = new modal();

            this.state.project_modal.on('saved', () => {
                this.parent.fetchData();
            });
            this.state.project_modal.on('closed', () => {
                this.elem.edit_project.removeClass('disabled');
            });
        }
        return this.state.project_modal;
    };

    /**
     * Get or set property modal
     *
     * @returns {object}
     */
    get property_modal() {
        if (this.state.property_modal === undefined) {
            let modal = require('@cam-project-js/modals/main/project/overview/property');
            this.state.property_modal = new modal();

            this.state.property_modal.on('saved', () => {
                this.parent.fetchData();
            });
            this.state.property_modal.on('closed', () => {
                this.elem.edit_property.removeClass('disabled');
            });
        }
        return this.state.property_modal;
    };

    /**
     * Get or set contacts modal
     *
     * @returns {object}
     */
    get contacts_modal() {
        if (this.state.contacts_modal === undefined) {
            let modal = require('@cam-project-js/modals/main/project/overview/contacts');
            this.state.contacts_modal = new modal();

            this.state.contacts_modal.on('saved', () => {
                this.parent.fetchData();
                this.elem.edit_contacts.addClass('disabled');
            });
            this.state.contacts_modal.on('closed', () => {
                this.elem.edit_contacts.removeClass('disabled');
            });
        }
        return this.state.contacts_modal;
    };

    /**
     * Get directions link for Google
     *
     * @param {object} data
     * @returns {string}
     */
    getDirectionsLink(data) {
        return 'https://www.google.com/maps/dir/?api=1&destination='+encodeURIComponent(data.address+' '+data.city+' '+data.state+' '+data.zip);
    };

    /**
     * Populate template with data
     *
     * @param {object} data
     */
    populate(data) {
        let result_content = '',
            timezone = this.router.main_route.layout.user.timezone;
        switch(data.status) {
            case Api.Constants.Projects.Status.CANCELLED:
                let cancelled_utc = moment.utc(data.cancelled_on),
                    cancelled_by_name = '';
                if (data.cancelled_by_user !== null) {
                    cancelled_by_name = ` by ${data.cancelled_by_user.first_name} ${data.cancelled_by_user.last_name}`;
                }
                result_content = `Cancelled${cancelled_by_name} on ${cancelled_utc.clone().tz(timezone).format('M/D/YY h:mm a')}`;
                break;
            case Api.Constants.Projects.Status.CLOSED:
                let completed_utc = moment.utc(data.completed_on),
                    completed_by_name = '';
                if (data.completed_by_user !== null) {
                    completed_by_name = ` by ${data.completed_by_user.first_name} ${data.completed_by_user.last_name}`;
                }
                result_content = `Closed${completed_by_name} on ${completed_utc.clone().tz(timezone).format('M/D/YY h:mm a')}`;
                break;
        }

        this.elem.root.empty();
        this.elem.root.append(details_tpl({
            customer_management_url: window.fx_url.BASE + `customer-management.php?cid=`,
            customer_id: data.property.customer.id,
            reference_id: data.reference_id,
            description: data.description,
            show_result: data.status !== Api.Constants.Projects.Status.ACTIVE,
            status_cancelled: data.status === Api.Constants.Projects.Status.CANCELLED,
            result_display: result_content,
            result: data.result_type !== null ? data.result_type.name : null,
            address: data.property.address,
            address_2: data.property.address_2,
            city: data.property.city,
            state: data.property.state,
            zip: data.property.zip,
            county_or_township: data.property.county !== null || data.property.township !== null,
            county_and_township: data.property.county !== null && data.property.township !== null,
            county: data.property.county,
            township: data.property.township,
            directions: this.getDirectionsLink(data.property),
            first_name: data.property.customer.first_name,
            last_name: data.property.customer.last_name,
            business_name: data.property.customer.business_name,
            email: data.property.customer.email,
            phones: data.property.customer.phones,
            is_unsubscribed: data.property.customer.is_unsubscribed,
            salesperson_user_id: data.salesperson_user_id,
            salesperson_first_name: data.salesperson_user_id != null ? data.salesperson_user?.first_name : null,
            salesperson_last_name: data.salesperson_user_id != null ? data.salesperson_user?.last_name : null,
            salesperson_is_active: data.salesperson_user_id != null ? data.salesperson_user?.is_active : null,
            marketing_type: data.referral_marketing_type_id,
            marketing_source: data.marketing_source,
            secondary_marketing_source: data.secondary_marketing_source,
            priority: this.getPriorityMap().get(data.priority),
            type: data.project_type !== null ? data.project_type.name : null,
            summary: data.summary,
            original_owner: data.property?.customer?.id === data.customer?.id,
            original_id: data.customer?.id,
            original_first_name: data.customer?.first_name,
            original_last_name: data.customer?.last_name,
            contacts: data.contacts,
            edit_project: window.project_data.user.can_edit,
            is_wisetack_enabled: window.project_data.features.wisetack_api,
            is_merchant_approved: window.project_data.wisetack.is_merchant_approved,
            is_financing_required_for_all_projects: window.project_data.company.is_financing_required_for_all_projects,
            is_financing_enabled: data.is_financing_enabled,
        }));


        this.elem.edit_project = this.elem.root.fxFind('edit-project');
        this.elem.edit_property = this.elem.root.fxFind('edit-property');
        this.elem.edit_contacts = this.elem.root.fxFind('edit-contacts');

        this.elem.edit_project.on('click.fx', () => {
            if (this.elem.edit_project.hasClass('disabled')) {
                e.preventDefault();
                return;
            }
            this.elem.edit_project.addClass('disabled');
            this.project_modal.open(this.state.id);
        });
        this.elem.edit_property.on('click.fx', () => {
            if (this.elem.edit_property.hasClass('disabled')) {
                e.preventDefault();
                return;
            }
            this.elem.edit_property.addClass('disabled');
            this.property_modal.open(this.state.property_id);
        });
        this.elem.edit_contacts.on('click.fx', (e) => {
            if (this.elem.edit_contacts.hasClass('disabled')) {
                e.preventDefault();
                return;
            }
            this.elem.edit_contacts.addClass('disabled');
            this.contacts_modal.open(this.state.id);
        })
    };

    /**
     * Handle event pushed to subcomponent from parent
     *
     * @param {string} event
     * @param {object} data
     */
    handleEvent(event, data) {
        if (event === 'update') {
            this.populate(data);
        }
    };

    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        this.state.id = request.params.project_id;
        this.state.property_id = request.data.property_id;
        await super.load(request);
    };

    getPriorityMap() {
        return new Map([
            [null, ''],
            [Api.Constants.Projects.Priority.HIGH, 'High'],
            [Api.Constants.Projects.Priority.MEDIUM, 'Medium'],
            [Api.Constants.Projects.Priority.LOW, 'Low'],
        ])
    };

    /**
     * Render component
     */
    render() {
        return info_tpl();
    };
}

module.exports = Info;

'use strict';

const Api = require('@ca-package/api');
const FormValidator = require('@ca-submodule/validator');

const Modal = require('@ca-submodule/modal').Base;

const modal_tpl = require('@cam-project-tpl/modals/main/project/overview/files/edit.hbs');

class Edit extends Modal {
    constructor() {
        super(modal_tpl(), {
            size: Modal.Size.TINY,
            classes: ['t-project-file']
        });
        Object.assign(this.state, {
            file_id: null,
            file: null
        });
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        this.elem.form = this.elem.content.fxFind('form');
        this.elem.input = {};
        for (let name of ['name']) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.state.form = FormValidator.init(this.elem.form)
            .on('form:submit', () => {
                this.save();
                return false;
            });
        let fields = {
            name: {
                required: true,
                maxlength: 250,
                maxlengthMessage: 'Invalid length - 250 chars. max'
            }
        };
        this.state.field = {};
        for (let name in fields) {
            fields[name].requiredMessage = 'Required';
            this.state.field[name] = this.elem.input[name].parsley(fields[name]);
        }

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
            this.reset();
        });
    };

    /**
     * Load file by id
     *
     * @param {string} file_id - UUID
     * @returns {Promise<void>}
     */
    async loadData(file_id) {
        let {data: file} = await Api.Resources.ProjectFiles().retrieve(file_id);
        return file;
    };

    /**
     * Populate fields using data object
     *
     * @param {object} data
     */
    populate(data) {
        this.elem.input.name.val(data.name);
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {object} $0.file_id
     * @param {Promise} $0.promise
     */
    open({file_id = null, promise}) {
        this.startWorking();
        this.state.promise = promise;
        this.state.file_id = file_id;
        this.setTitle('Edit Project File');
        this.loadData(this.state.file_id).then(data => {
            this.populate(data);
            this.resetWorking();
        }, error => {
            this.showErrorMessage('Unable to load project file, please contact support');
            console.log(error);
        });
        super.open();
    };

    /**
     * Save changes
     */
    save() {
        this.clearMessages();
        this.startWorking();

        Api.Resources.ProjectFiles()
            .accept('application/vnd.adg.fx.collection-v1+json')
            .partialUpdate(this.state.file_id, {
                name: this.elem.input.name.val()
            })
            .then(({data: file}) => {
                this.state.promise.resolve(file);
                this.close();
            }, (error, response) => {
                switch (response.statusCode()) {
                    case 422:
                        let item_errors = response.data().errors;
                        for (let item in item_errors) {
                            this.state.field[item].addError('fx-'+item, {message: item_errors[item]});
                        }
                        break;
                    default:
                        this.showErrorMessage('Unable to save project file, please contact support');
                        break;
                }
            });
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.resetWorking();
        this.state.file_id = null;
        this.state.form.reset();
        this.elem.form[0].reset();
    };
}

module.exports = Edit;

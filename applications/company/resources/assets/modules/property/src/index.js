'use strict';

import $ from 'jquery';
import moment from 'moment-timezone';

import Api from '@ca-package/api';
import {Base as Table} from '@ca-submodule/table';
import {findChild, insertTemplate, jsSelector} from '@ca-package/dom';

import {Delete as DeleteModal} from './modals/delete';

const Cookie = require('@cac-js/utils/cookie');

import layout_tpl from '@cam-property-tpl/layout.hbs';

/**
 * @memberof module:Property
 */
export class Controller {
    /**
     * Property constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            page_name: 'property',
            version: 'v1',
            saved_scope: null,
            saved_table_order: null,
            saved_table_visibility: null,
            table: null,
            layout,
            modals: {},
            table_scope: {
                sorts: {
                    created_at: Table.Sort.ASC
                }
            }
        };
        this.boot();
    };

    /**
     * Get layout instance
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get delete modal
     *
     * @readonly
     *
     * @returns {module:Property/Modals.Delete}
     */
    get delete_modal() {
        if (this.state.modals.delete === undefined) {
            this.state.modals.delete = new DeleteModal(this);
        }
        return this.state.modals.delete;
    };

    /**
     * Get table
     *
     * @readonly
     *
     * @returns {module:Table.Base}
     */
    get table() {
        return this.state.table;
    };

    /**
     * Trigger delete modal for row
     *
     * @param {Object} data - row data from table
     */
    handleDelete(data) {
        this.delete_modal.open(data);
    };

    /**
     * Generate customer management page url
     *
     * @param {number} id - customer id
     * @returns {string}
     */
    customerDetailsUrl(id) {
        return window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', id);
    };

    /**
     * Generate customer management page url
     *
     * @param {number} id - customer id
     * @returns {string}
     */
    propertyDetailsUrl(customer_id, property_id) {
        return `${window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', customer_id)}&tid=${property_id}#${property_id}`;
    };

    /**
     * Generate project page url filtered by property id
     *
     * @param {number} id - property id
     * @returns {string}
     */
    projectPageUrl(id) {
        return window.fx_pages.PROJECTS.replace('{property_id}', id);
    };

    /**
     * Define the default settings for property table
     *
     * @returns {Object}
     */
    tableSettings() {
        return {
            server_paginate: false,
            load_tooltip: false,
            use_table_settings: true,
            column_order: this.state.saved_table_order,
            column_visibility: this.state.saved_table_visibility
        };
    };

    /**
     * Create the Property DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table, this.tableSettings())
            .on('row_click', (data) => {
                window.location.href = this.propertyDetailsUrl(data.customer_id, data.id);
            }).on('scope_change', (scope) => {
                this.state.table_scope = scope;
                let url_scope = Table.buildUrlFromScope(this.state.table_scope);

                // set cookie with scope to expire after an hour
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_scope`, url_scope.replace('?', ''), Cookie.expirationTypes().Hours, 1);
                window.history.replaceState(null, '', window.location.href.split('?')[0]+url_scope);
            }).on('table_settings_changed', (config) => {
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_order`, config.order.toString());
                Cookie.setCookie(`${this.state.page_name}_${this.state.version}_table_visibility`, config.visibility.toString());
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Properties'
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        this.state.table.setFilterOptions({
            city: {
                label: 'City',
                type: Table.FilterValueTypes.SELECT,
                options: property_data.filter_options.cities
            },
            state: {
                label: 'State',
                type: Table.FilterValueTypes.SELECT,
                options: property_data.filter_options.states
            },
            zip: {
                label: 'Postal Code',
                type: Table.FilterValueTypes.SELECT,
                options: property_data.filter_options.postal_codes
            },
            county: {
                label: 'County',
                type: Table.FilterValueTypes.SELECT,
                options: property_data.filter_options.counties
            },
            township: {
                label: 'Township',
                type: Table.FilterValueTypes.SELECT,
                options: property_data.filter_options.townships
            },

            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            created_by_user_id: {
                label: 'Created By',
                type: Table.FilterValueTypes.SELECT,
                options: property_data.filter_options.all_users
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_by_user_id: {
                label: 'Updated By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: property_data.filter_options.all_users
            }
        });

        // set columns config
        this.state.table.setColumns({
            customer_name: {
                label: 'Customer Name',
                value: (data) => {
                    return `<a href="${this.customerDetailsUrl(data.customer_id)}">${data.customer_name}</a>`
                }
            },
            address: {
                label: 'Address',
                value: (data) => {
                    return `<a href="${this.propertyDetailsUrl(data.customer_id, data.id)}">${data.address}</a>`
                }
            },
            address_2: {
                label: 'Address 2'
            },
            city: {
                label: 'City'
            },
            state: {
                label: 'State/Province'
            },
            zip: {
                label: 'Postal Code'
            },
            county: {
                label: 'County'
            },
            township: {
                label: 'Township'
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            created_by_user_name: {
                label: 'Created By'
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.state.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_by_user_name: {
                label: 'Updated By'
            }
        });

        // set row action config
        this.state.table.setRowActions({
            view_property: {
                label: 'View Property',
                link: {
                    href: data => this.propertyDetailsUrl(data.customer_id, data.id)
                }
            },
            view_customer: {
                label: 'View Customer',
                link: {
                    href: data => this.customerDetailsUrl(data.customer_id)
                }
            },
            view_projects: {
                label: 'View Projects',
                link: {
                    href: data => this.projectPageUrl(data.id)
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                visible: property_data.delete,
                action: (data) => {
                    this.handleDelete(data);
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            export_csv: {
                label: 'Export',
                type_class: 't-tertiary-icon',
                icon: 'system--download-line',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/properties' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                },
                visible: property_data.export
            }
        });

        this.state.table.setAjax(Api.Resources.Properties, (request) => {
            request.accept('application/vnd.adg.fx.collection-v1+json');
        });
    };

    /**
     * Get Url parameters to build table scope
     *
     * @returns {string|null}
     */
    getUrlParameters() {
        let parameters = window.location.search;
        if (parameters === '') {
            return null;
        }
        return parameters.substring(1);
    };

    /**
     * Draw table
     */
    load() {
        let url_parameters = this.getUrlParameters();
        // if request query contains scope we use it
        if (url_parameters !== null) {
            this.state.table_scope = Table.buildScopeFromQuery(url_parameters);
            // otherwise we get it from the cookie
        } else if (this.state.saved_scope !== null) {
            this.state.table_scope = Table.buildScopeFromQuery(this.state.saved_scope);
        }

        // otherwise we pull from the default scope stored in the state
        this.state.table.setState(this.state.table_scope);
        this.state.table.build();
    };

    /**
     * Boot Property
     */
    boot() {
        this.layout.setModeWindow();
        this.layout.setTitle('Properties');
        this.elem.root = insertTemplate(this.layout.elem.content, layout_tpl());
        this.elem.table = findChild(this.elem.root, jsSelector('data-table'));

        this.state.saved_scope = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_scope`);

        let column_order = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_order`);
        if (column_order !== null) {
            this.state.saved_table_order = column_order.split(',');
        }

        let column_visibility = Cookie.getCookie(`${this.state.page_name}_${this.state.version}_table_visibility`);
        if (column_visibility !== null) {
            this.state.saved_table_visibility = column_visibility.split(',');
        }

        this.createTable();
        this.load();
    };
}

'use strict';

import 'remixicon/icons/System/close-line.svg';
import 'remixicon/icons/Document/book-marked-line.svg';

import $ from 'jquery';

import {find, findChild, insertTemplate, jsSelector, onClickWatcher} from '@ca-package/dom';

import Modal from '@ca-package/router/src/modal';

import complete_tpl from '@cam-training-tpl/main-pages/complete.hbs';

/**
 * @memberof module:Training/Pages/MainPages
 */
export class CompletePage extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        this.elem = {};
    };

    /**
     * Get layout instance
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.parent.layout;
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.elem.body.addClass('t-modal-open');
        this.elem.overlay.addClass('t-show');
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.elem.overlay.removeClass('t-show');
        this.elem.body.removeClass('t-modal-open');
        await super.unload(request, next);
    };

    /**
     * Handle button action based on value
     *
     * @param {string} value
     */
    handleButton(value) {
        switch (value) {
            case 'yes':
                this.router.externalRedirect(`${window.fx_url.API}training/complete`)
                break;
            case 'no':
                this.router.navigate('main');
                break;
        }
    };

    /**
     * Boot page
     */
    boot() {
        super.boot();

        this.elem.body = find('body');
        this.elem.overlay = insertTemplate(this.elem.body, complete_tpl());
        this.elem.buttons = findChild(this.elem.overlay, jsSelector('buttons'));

        const that = this;
        onClickWatcher(this.elem.buttons, jsSelector('button'), function () {
            that.handleButton($(this).data('value'));
        }, true);
    };
}

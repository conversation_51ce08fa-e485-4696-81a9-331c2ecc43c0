// Modal Confirmation Styles
.m-modal-void-confirmation,
.m-modal-refund-confirmation {
    p {
        margin-bottom: 1rem;
        
        &.t-warning {
            color: #d32f2f;
            font-weight: 500;
        }
        
        &.t-small {
            font-size: 0.875rem;
        }
        
        &.t-grey {
            color: #757575;
        }
    }
}

// Transaction Details Modal Styles
.m-transaction-details-modal {
    .modal-content {
        padding: 2rem;
        max-height: 80vh;
        overflow-y: auto;
    }
    
    .details-section {
        margin-bottom: 2rem;
        
        h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e0e0e0;
            color: #333;
        }
        
        .details-row {
            display: flex;
            margin-bottom: 0.75rem;
            align-items: flex-start;
            
            .details-label {
                flex: 0 0 40%;
                font-weight: 500;
                color: #666;
                padding-right: 1rem;
            }
            
            .details-value {
                flex: 1;
                word-break: break-word;
                
                &.amount {
                    font-weight: 600;
                    color: #2e7d32;
                }
                
                &.total-amount {
                    font-size: 1.2rem;
                    font-weight: 700;
                    color: #1976d2;
                }
                
                a {
                    color: #1976d2;
                    text-decoration: none;
                    
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 3px;
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: uppercase;
        
        &.t-green {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }
        
        &.t-blue {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        &.t-yellow {
            background-color: #fff8e1;
            color: #f57c00;
            border: 1px solid #ffecb3;
        }
        
        &.t-red {
            background-color: #ffebee;
            color: #d32f2f;
            border: 1px solid #ffcdd2;
        }
        
        &.t-orange {
            background-color: #fff3e0;
            color: #f57c00;
            border: 1px solid #ffcc02;
        }
        
        &.t-grey {
            background-color: #f5f5f5;
            color: #757575;
            border: 1px solid #e0e0e0;
        }
    }
    
    // Responsive adjustments
    @media screen and (max-width: 768px) {
        .modal-content {
            padding: 1rem;
        }
        
        .details-row {
            flex-direction: column;
            margin-bottom: 1rem;
            
            .details-label {
                flex: none;
                padding-right: 0;
                margin-bottom: 0.25rem;
                font-size: 0.875rem;
            }
            
            .details-value {
                &.total-amount {
                    font-size: 1.1rem;
                }
            }
        }
    }
}

// Tooltip adjustments for modal context
.m-transaction-details-modal {
    [data-tooltip] {
        border-bottom: 1px dotted #666;
        cursor: help;
        
        &:hover {
            border-bottom-color: #1976d2;
        }
    }
}
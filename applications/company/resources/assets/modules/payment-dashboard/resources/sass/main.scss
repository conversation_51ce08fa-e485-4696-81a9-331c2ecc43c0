@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/form';
@use '~@cas-layout-sass/layout';
@use '~@cas-table-sass/table';
@use '~@cas-form-input-sass/date-time';
@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/number';
@use '~@cac-sass/app/highlight';
@use '~@cac-sass/app/grid';
@use '~@cas-modal-sass/modal';
@use '~@cas-form-input-sass/wysiwyg';
@use '~@cas-tooltip-sass/tooltip';

// Import modal styles
@import "modals";

.m-payment-dashboard {
  position: relative;
  @include base.full-width-height;
  padding: 0;
  .c-pd-components {
    flex: 1;
    width: 80%;
    padding: 1.5rem;
    transition: margin-left 0.5s ease, width 0.5s ease;
  }

  .c-pd-component {
    @include base.full-width-height;
    padding: base.unit-rem-calc(24px);
    &.t-manager {
      @include base.respond-to('<small') {
        padding: 0;
      }
    }
    &.t-hidden {
      display: none;
    }
  }

  .m-list {
    @include base.full-width-height;
    .c-l-table {
      @include base.full-width-height;
      padding: base.unit-rem-calc(12px);
      background-color: base.$color-white-default;
      border-radius: base.unit-rem-calc(12px);
      box-shadow: base.$elevation-level-2;
      @include base.respond-to('<small') {
        padding: base.unit-rem-calc(12px) 0;
        border-radius: 0;
        box-shadow: none;
      }
    }
  }

  .c-cr-loader {
    display: none;
    position: absolute;
    @include base.full-width-height;
    background: rgba(0, 0, 0, 0.5) url('~@cac-public/images/loading.svg') no-repeat center;
    background-size: base.unit-rem-calc(100px) base.unit-rem-calc(100px);
    z-index: 120;
  }

  .c-cr-content {
    display: flex;
    height: 100%;
    width: 100%;

    .c-crc-sidebar {
      background-color: base.$color-white-default;
      box-shadow: base.$elevation-sidebar-left;
      flex: 0 0 auto;
      width: 21%;
      height: 100%;
      transition: transform 0.4s ease, width 0.4s ease, opacity 0.3s ease;

      @include base.respond-to('<=large') {
        width: 35%;
      }
      @include base.respond-to('<=small') {
        width: 100%;
      }
      @include base.respond-to('<=xsmall') {
        width: 100%;
      }

      &.t-visible {
        transform: translateX(0);
        opacity: 1;
        visibility: visible;
      }

      &.collapsed {
        transform: translateX(-100%);
        opacity: 0;
        visibility: hidden;
        width: 0;
      }
    }

    .c-crcs-wrapper {
      border-top: 1px solid base.$color-grey-light-4;
      border-bottom: 1px solid base.$color-grey-light-4;

      .c-crcsw-title {
        @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
        color: base.$color-grey-dark-4;
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(8px);
      }

    }

    .c-crcs-inner {
      margin: 0 auto;
      padding: base.unit-rem-calc(16px);
      height: calc(100% - 50px);
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: base.unit-rem-calc(16px);


      .c-crcsi-item.f-field {
        //padding: base.unit-rem-calc(10px) 0;

        label {
          display: block;
          font-size: base.unit-rem-calc(14px);
          font-weight: 500;
          color: base.$color-grey-dark-1;
          margin-bottom: base.unit-rem-calc(4px);
        }

        .c-crcsi-value {
          font-size: 2rem;
          font-weight: 600;
          line-height: 2.5rem;
          background-clip: text;
          color: transparent;

          &.approved {
            background: linear-gradient(45deg, #007bff, #0056b3); /* Blue gradient */
            -webkit-background-clip: text;
          }

          &.pending {
            background: linear-gradient(45deg, #FFA35D, #FF6D00); /* Orange gradient */
            -webkit-background-clip: text;
          }
        }

        .c-crcsii-wrapper {
          border: 1px solid base.$color-grey-light-4;
          border-radius: base.unit-rem-calc(16px);
          padding: base.unit-rem-calc(12px);

          .c-crcsiiw-title {
            font-size: base.unit-rem-calc(16px);
            font-weight: 600;
            color: base.$color-grey-dark-2;
          }

          .c-crcsiiw-item {
            margin: base.unit-rem-calc(8px) 0;

            .info-container {
              .info-item {
                display: flex;
                align-items: center;

                .label {
                  display: flex;
                  gap: base.unit-rem-calc(4px);
                }
              }

              .dotted-line {
                flex-grow: 1;
                border-bottom: 1px dotted #ccc;
                margin: 0 10px;
              }

              .value {
                flex-shrink: 0;
                font-size: base.unit-rem-calc(14px);
                font-weight: bold;
                background-clip: text;
                color: transparent;
              }

              .value.paid {
                background: linear-gradient(45deg, #36B494, #008671);
                -webkit-background-clip: text;
              }

              .value.loan-size {
                color: base.$color-grey-dark-4;
              }
            }

            .c-crcsiiwi-wrapper {
              margin: base.unit-rem-calc(8px) 0;

              .graph-header {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .label {
                  display: flex;
                  font-size: 1em;
                  color: base.$color-grey-dark-1;
                  grid-gap: 0.25rem;
                  align-items: center;
                }
              }

              .graph-container {
                .f-fps-meter {
                  position: relative;
                  background-color: base.$color-grey-light-4;
                  border-radius: base.$prop-border-radius;
                  height: base.unit-rem-calc(8px);
                  overflow: hidden;
                }

                .f-fpsm-bar {
                  position: absolute;
                  width: 0;
                  height: inherit;
                  border-radius: inherit;
                  transition: width .5s ease-in-out, background .25s ease-in-out;
                }

                .red-gradient {
                  background: linear-gradient(to right, #FF5D5D, #D00000);
                }

                .orange-gradient {
                  background: linear-gradient(to right, #FFD15D, #FF6D00);
                }

                .green-gradient {
                  background: linear-gradient(to right, #36B494, #005752);
                }
              }
            }

            .label {
              display: block;
              font-size: 1em;
              color: #555;
              margin: base.unit-rem-calc(8px) 0;
            }

            .info-icon {
              font-style: normal;
              border: 1px solid #ccc;
              border-radius: 50%;
              padding: 0 5px;
              font-size: 0.8em;
              cursor: pointer;
            }

            .circle {
              position: relative;
              width: 80px;
              height: 80px;
              background: conic-gradient(#36B494 0%, #005752 55%, #E0E0E0 55% 100%);
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .circle-inner {
              width: 60px;
              height: 60px;
              background-color: #fff;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .percentage {
              font-size: 1em;
              color: base.$color-grey-dark-1;
              font-family: 'Barlow', Arial, sans-serif;
            }

          }
        }


      }
    }

    .c-crcsi-input-wrapper {
      display: none;
      flex-direction: column;
      margin-top: base.unit-rem-calc(24px);

      &.t-show {
        display: flex;
      }
    }

    .c-crcsiiw-inputs {
      &.t-has-inputs {
        padding: base.unit-rem-calc(8px) 0 base.unit-rem-calc(18px) 0;
        border-top: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
      }
    }

    .c-crcsiiwi-input {
      margin-bottom: base.unit-rem-calc(16px);

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    .c-crcsiiw-submit {
      width: 50%;
      align-self: end;
      margin-top: base.unit-rem-calc(24px);

      .t-primary {
        width: 100%;
      }
    }

    .c-crc-pages {
      flex: 1;
      width: 80%;
      padding: base.unit-rem-calc(24px);
      @include base.respond-to('<=large') {
        width: 75%;
        padding: base.unit-rem-calc(16px);
      }
      @include base.respond-to('<=small') {
        width: 60%;
        padding: base.unit-rem-calc(8px);
      }
      @include base.respond-to('<=xsmall') {
        width: 50%;
        padding: base.unit-rem-calc(4px);
      }

      transition: margin-left 0.5s ease, width 0.5s ease;

      &.expanded {
        margin-left: 0;
        width: 100%;
      }
    }

    .c-crcp-page {
      width: 100%;
      height: 100%;
    }
  }
}

<div class="m-payment-dashboard">
    <div class="c-cr-loader" data-js="loader"></div>
    <div class="c-cr-content">
        <div class="c-crc-sidebar t-visible" data-js="sidebar">
            <div class="c-crcs-wrapper">
                <div class="c-crcsw-title">Payment Overview</div>
            </div>
            <div class="c-crcs-inner">
                <div class="c-crcsi-item f-field">
                    <label class="c-crcsi-item">Current Captured Amount
                        <span data-tooltip data-type="info">Total transactions in the CAPTURED status</span>
                    </label>
                    <div class="c-crcsi-value approved" data-js="captured-amount">$0</div>
                </div>
                <div class="c-crcsi-item f-field">
                    <label class="c-crcsi-item">Currently Settled
                        <span data-tooltip data-type="info">Total transactions that have been settled</span>
                    </label>
                    <div class="c-crcsi-value pending" data-js="settled-amount">$0</div>
                </div>
                <div class="c-crcsi-item f-field">
                    <div class="c-crcsii-wrapper">
                        <label class="c-crcsiiw-title">Metrics</label>

                        <div class="c-crcsiiw-item">
                            <label for="reportPeriod">Report Period:</label>
                            <select id="reportPeriod" data-js="report-period">
                                {{#each report_period_options}}
                                {{#if is_option}}<option value="{{@key}}"{{#if is_default}} selected{{/if}}>{{label}}</option>{{/if}}
                                {{/each}}
                            </select>
                        </div>

                        <div class="c-crcsiiw-item">
                            <label for="dateRange">Date Range:</label>
                            <input class="f-f-input" id="period" type="date-range" data-js="period" />
                        </div>

                        <div class="c-crcsiiw-item">
                            <div class="info-container">
                                <div class="info-item">
                                    <span class="label">Funded
                                        <span data-tooltip data-type="info">Total amount funded within the date range</span></span>
                                    </span>
                                    <span class="dotted-line"></span>
                                    <span class="value paid" data-js="funded-amount">$0</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">Credit Card
                                        <span data-tooltip data-type="info">Total credit card transactions within the date range</span></span>
                                    <span class="dotted-line"></span>
                                    <span class="value paid" data-js="credit-card-amount">$0</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">ACH
                                        <span data-tooltip data-type="info">Total ACH transactions within the date range</span></span>
                                    <span class="dotted-line"></span>
                                    <span class="value paid" data-js="ach-amount">$0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="c-crc-pages" data-js="pages"></div>
    </div>
</div>
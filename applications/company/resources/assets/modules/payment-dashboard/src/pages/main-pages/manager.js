'use strict';

const EventEmitter = require('events');

import Page from '@ca-package/router/src/page';
import {Base as Table} from '@ca-submodule/table';

import manager_tpl from '@cam-payment-dashboard-tpl/pages/main-pages/manager.hbs';
import TransactionsTable from './manager-pages/transactions-table.js';

export class Manager extends Page {

    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            main: parent,
            layout: parent.state.layout,
            events: new EventEmitter,
            filter_period: null,
            transactions_table: null
        });
    };

    /**
     * On events listener
     *
     * @param event
     * @param closure
     * @returns {*}
     */
    on(event, closure) {
        return this.state.events.on(event, closure);
    };

    /**
     * Initialize the transactions table
     */
    initializeTransactionsTable() {
        try {
            this.state.transactions_table = new TransactionsTable(this.elem.table, this.state);
            this.state.transactions_table.create();
        } catch (error) {
            console.error('Error initializing transactions table:', error);
        }
    }

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        if (this.state.transactions_table) {
            this.state.transactions_table.refresh();
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.transactions_table) {
            this.state.transactions_table.refresh();
        }

        this.state.main.on('period_filter_change', (data) => {
            this.state.filter_period = data;
            
            if (this.state.transactions_table) {
                this.state.transactions_table.updateScope({
                    filters: {
                        created_at: [Table.Operators.BETWEEN, [data.start, data.end]]
                    }
                }, true);
                this.state.transactions_table.refresh();
            }
        });

        await super.load(request, next);
    }

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
    }

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = root.fxFind('table-container');
        this.initializeTransactionsTable();
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return manager_tpl();
    }
}
'use strict';

const EventEmitter = require('events');
import moment from 'moment-timezone';

import Page from '@ca-package/router/src/page';
const Tooltip = require('@ca-submodule/tooltip');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/date_range'));

const Number = require('@cac-js/utils/number');

import main_tpl from '@cam-payment-dashboard-tpl/pages/main.hbs';
import {Manager} from '@cam-payment-dashboard-js/pages/main-pages/manager';
import { report_period_options, report_period_types } from './constants';
import Api from '@ca-package/api';

export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        let [start_date, end_date] = report_period_options[report_period_types.LAST_30_DAYS].range();

        Object.assign(this.state, {
            parent: parent,
            events: new EventEmitter,
            period_filter: {
                start_date,
                end_date
            }
        });
    };

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            manager: {
                default: true,
                page: Manager
            }
        };
    };

    /**
     * On events listener
     *
     * @param event
     * @param closure
     * @returns {*}
     */
    on(event, closure) {
        return this.state.events.on(event, closure);
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };

    /**
     * Get payment metrics report for a specific date range
     *
     * @param {string} start - Start date in ISO format
     * @param {string} end - End date in ISO format
     * @returns {Promise<Object>} Payment metrics data
     */
    async getPaymentMetricsReport(start, end) {
        try {
            const PAYMENT_URL = `${window.fx_url.API_V1}payments/dashboard/metrics?start_date=${start}&end_date=${end}`;
            const response = await $.ajax({
                url: PAYMENT_URL,
                type: Api.Request.Method.GET,
                contentType: 'application/json',
            });

            if (Array.isArray(response.data)) {
                return null;
            }

            return response.data;
        } catch (error) {
            console.error('Error fetching payment metrics:', error);
            return null;
        }
    }

    /**
     * Refresh metrics data
     *
     * @returns {Promise<void>}
     */
    async refreshMetrics(start_date, end_date) {
        let {start, end} = this.getParsedTimestamp(start_date, end_date);
        let metrics = {
            total_captured_amount: 0,
            total_settled_amount: 0,
            total_funded_amount: 0,
            total_credit_card_amount: 0,
            total_ach_amount: 0
        };

        try {
            metrics = await this.getPaymentMetricsReport(start, end) || metrics;
        } catch (error) {
            console.error('Error fetching payment metrics:', error);
        }

        this.elem.captured_amount.text(Number.toCurrency(metrics.total_captured_amount));
        this.elem.settled_amount.text(Number.toCurrency(metrics.total_settled_amount));
        this.elem.funded_amount.text(Number.toCurrency(metrics.total_funded_amount));
        this.elem.credit_card_amount.text(Number.toCurrency(metrics.total_credit_card_amount));
        this.elem.ach_amount.text(Number.toCurrency(metrics.total_ach_amount));
    };

    /**
     * Load sidebar metrics and event listeners
     * @returns {Promise<void>}
     */
    async loadSidebar() {
        let { start_date, end_date } = this.state.period_filter;
        await this.refreshMetrics(start_date, end_date);

        let updatePeriodFilter = (start, end) => {
            this.state.period_filter = { start_date: start, end_date: end };
            this.refreshMetrics(start, end);
            let period_change = this.getParsedTimestamp(start, end);
            this.state.events.emit('period_filter_change', period_change);
        };

        // load flatpickr
        this.state.period = FormInput.init(this.elem.period, {
            pickr_config: {
                mode: "range",
                dateFormat: "m/d/Y",
                defaultDate: [this.state.period_filter.start_date, this.state.period_filter.end_date],
                altFormat: 'm/d/Y',
                onChange: (selected_dates) => {
                    if (selected_dates.length === 2) {
                        let start = moment(selected_dates[0]).format('MM/DD/YYYY'),
                            end = moment(selected_dates[1]).format('MM/DD/YYYY');
                        updatePeriodFilter(start, end);
                        this.elem.report_period.val(report_period_types.CUSTOM).trigger('change');
                    }
                }
            }
        });

        // load sidebar event listeners
        this.elem.report_period.on('change', (event) => {
            let val = $(event.target).find("option:selected").val();
            if (val === report_period_types.CUSTOM) return;
            if (!report_period_options[parseInt(val)].range()) return;

            let selection = parseInt(val),
                [start, end] = report_period_options[selection].range();

            this.state.period.state.pickr.setDate([start, end]);
            updatePeriodFilter(start, end);
        });

        this.elem.sidebar_control.on('click', () => {
            let sidebarVisible = this.elem.sidebar.hasClass('t-visible');
            this.elem.sidebar.toggleClass('collapsed', sidebarVisible).toggleClass('t-visible', !sidebarVisible);
            this.elem.pages.toggleClass('expanded', !sidebarVisible);
        });
    };

    getParsedTimestamp(start, end) {
        let timezone = this.state.layout.user.timezone;
        return {
            start: moment.tz(start, 'MM/DD/YYYY', timezone).startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss[Z]'),
            end: moment.tz(end, 'MM/DD/YYYY', timezone).add(1, 'day').endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
        }
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
    }

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(this.elem.root, {
            max_width: 200
        });
        this.elem.pages = root.fxFind('pages');

        this.elem.table = this.elem.root.fxFind('table-container');
        this.elem.sidebar = this.elem.root.fxFind('sidebar');
        this.elem.sidebar_control = this.elem.root.fxFind('sidebar-control');

        this.elem.captured_amount = this.elem.root.fxFind('captured-amount');
        this.elem.settled_amount = this.elem.root.fxFind('settled-amount');
        this.elem.funded_amount = this.elem.root.fxFind('funded-amount');
        this.elem.credit_card_amount = this.elem.root.fxFind('credit-card-amount');
        this.elem.ach_amount = this.elem.root.fxFind('ach-amount');
        this.elem.report_period = this.elem.root.fxFind('report-period');
        this.elem.period = this.elem.root.fxFind('period');

        this.loadSidebar();
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl({
            report_period_options
        });
    }
}
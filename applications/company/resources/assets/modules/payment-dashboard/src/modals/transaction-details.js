'use strict';

const moment = require('moment-timezone');
const Modal = require('@ca-submodule/modal').Base;
const details_tpl = require('@cam-payment-dashboard-tpl/modals/transaction-details.hbs');
const Number = require('@cac-js/utils/number');
const {Constants} = require('@ca-package/api');

// ACH Return Code mappings
const ACH_RETURN_CODES = {
    'R01': 'Insufficient funds',
    'R02': 'Bank account closed',
    'R03': 'No bank account/unable to locate account',
    'R04': 'Invalid bank account number',
    'R06': 'Returned per ODFI request',
    'R07': 'Authorization revoked by customer',
    'R08': 'Payment stopped',
    'R09': 'Uncollected funds',
    'R10': 'Customer advises not authorized',
    'R11': 'Check truncation entry return',
    'R12': 'Branch sold to another RDFI',
    'R13': 'RDFI not qualified to participate',
    'R14': 'Representative payee deceased or unable to continue in that capacity',
    'R15': 'Beneficiary or bank account holder',
    'R16': 'Bank account frozen',
    'R17': 'File record edit criteria',
    'R18': 'Improper effective entry date',
    'R19': 'Amount field error',
    'R20': 'Non-payment bank account',
    'R21': 'Invalid company ID number',
    'R22': 'Invalid individual ID number',
    'R23': 'Credit entry refused by receiver',
    'R24': 'Duplicate entry',
    'R25': 'Addenda error',
    'R26': 'Mandatory field error',
    'R27': 'Trace number error',
    'R28': 'Transit routing number check digit error',
    'R29': 'Corporate customer advises not authorized',
    'R30': 'RDFI not participant in check truncation program',
    'R31': 'Permissible return entry (CCD and CTX only)',
    'R32': 'RDFI non-settlement',
    'R33': 'Return of XCK entry',
    'R34': 'Limited participation RDFI',
    'R35': 'Return of improper debit entry',
    'R36': 'Return of Improper Credit Entry',
    'R39': 'Improper Source Document',
    'R40': 'Non-Participant in ENR program',
    'R41': 'Invalid transaction code',
    'R42': 'Transit/Routing check digit error',
    'R43': 'Invalid DFI account number',
    'R44': 'Invalid individual ID number',
    'R45': 'Invalid individual name',
    'R46': 'Invalid representative payee indicator',
    'R47': 'Duplicate enrollment',
    'R50': 'State Law affecting RCK Acceptance',
    'R51': 'Item is Ineligible, Notice Not Provided, Signature Not Genuine, or Item Altered (adjustment entries)',
    'R52': 'Stop Payment on Item (adjustment entries)',
    'R61': 'Misrouted return',
    'R62': 'Incorrect trace number',
    'R63': 'Incorrect dollar amount',
    'R64': 'Incorrect individual identification',
    'R65': 'Incorrect transaction code',
    'R66': 'Incorrect company identification',
    'R67': 'Duplicate return',
    'R68': 'Untimely return',
    'R69': 'Multiple errors',
    'R70': 'Permissible return entry not accepted',
    'R71': 'Misrouted dishonored return',
    'R72': 'Untimely dishonored return',
    'R73': 'Timely original return',
    'R74': 'Corrected return',
    'R80': 'Cross Border Payment Coding Error',
    'R81': 'Non-Participant in Cross-Border Program',
    'R82': 'Invalid Foreign Receiving DFI identification',
    'R83': 'Foreign Receiving DFI Unable to Settle'
};

class TransactionDetailsModal extends Modal {
    /**
     * Constructor
     * 
     * @param {Object} state - Application state
     */
    constructor(state) {
        super('', {
            size: Modal.Size.LARGE,
            closable: true,
            classes: ['m-transaction-details-modal']
        });
        
        this.setTitle('Transaction Details');
        this.state.app_state = state;
        
        // Add close action
        this.addAction({
            type: Modal.Action.CLOSE
        });
    }

    /**
     * Open modal with transaction data
     * 
     * @param {Object} transaction - Transaction data
     */
    openWithTransaction(transaction) {
        this.state.transaction = transaction;
        
        // Format transaction data for template
        const formattedData = this.formatTransactionData(transaction);
        
        // Set the modal content
        this.setContent(details_tpl(formattedData));
        
        // Open the modal
        this.open();
    }

    /**
     * Format transaction data for display
     * 
     * @param {Object} transaction - Raw transaction data
     * @returns {Object} - Formatted data for template
     */
    formatTransactionData(transaction) {
        const timezone = this.state.app_state?.layout?.user?.timezone || 'UTC';
        
        // Format currency amounts
        const formatCurrency = (amount) => {
            return amount ? Number.toCurrency(amount) : null;
        };

        // Format dates
        const formatDate = (date) => {
            return date ? moment(date).tz(timezone).format('MM/DD/YYYY h:mm A z') : null;
        };

        const formatDateOnly = (date) => {
            return date ? moment(date).tz(timezone).format('MM/DD/YYYY') : null;
        };

        // Determine status class for badge
        const getStatusClass = (status) => {
            const statusClasses = {
                'CAPTURED': 't-green',
                'AUTHORIZED': 't-blue', 
                'PENDING': 't-yellow',
                'INITIATED': 't-blue',
                'DECLINED': 't-red',
                'EXPIRED': 't-grey',
                'CANCELED': 't-grey',
                'VOIDED': 't-grey',
                'REFUNDED': 't-orange',
                'UNKNOWN': 't-grey'
            };
            return statusClasses[status?.toUpperCase()] || 't-grey';
        };

        // Build customer name
        const customer_name = [transaction.customer_first_name, transaction.customer_last_name]
            .filter(name => name)
            .join(' ') || null;

        // Format ACH account type
        const formatACHAccountType = (type) => {
            switch (parseInt(type)) {
                case 1:
                    return 'Checking';
                case 2:
                    return 'Savings';
                default:
                    return type ? `Type ${type}` : null;
            }
        };

        // Check if there's action history
        const hasActionHistory = transaction.voided_at || transaction.void_reference || 
                                transaction.refunded_at || transaction.refund_reference;

        // Check if there's processing details data
        const hasProcessingDetails = transaction.auth_code || transaction.gateway_response_code || 
                                   transaction.gateway_response_message || transaction.card_response_code ||
                                   transaction.card_response_message || transaction.merchant_mid || 
                                   transaction.company_name;

        // Check if there's settlement and funding data
        const hasSettlementFunding = transaction.batch_id || transaction.funding_id || 
                                    transaction.host_batch || transaction.settlement_date || 
                                    transaction.settled_at || transaction.funded_at;
        const getTransactionTypeName = (type) => {
            switch (parseInt(type)) {
                case Constants.PropelrTransaction.Type.CREDIT_CARD:
                    return 'Credit Card';
                case Constants.PropelrTransaction.Type.ACH:
                    return 'ACH';
                default:
                    return type ? `Type ${type}` : null;
            }
        };
        const isCreditCard = parseInt(transaction.type) === Constants.PropelrTransaction.Type.CREDIT_CARD;
        const isACH = parseInt(transaction.type) === Constants.PropelrTransaction.Type.ACH;

        // ACH Return Information
        const hasACHReturn = isACH && transaction.ach_return_code;
        const achReturnDescription = hasACHReturn ? 
            ACH_RETURN_CODES[transaction.ach_return_code] || 'Unknown return reason' : null;

        return {
            // Transaction Information
            id: transaction.id,
            retref: transaction.retref,
            order_id: transaction.order_id,
            invoice_number: transaction.invoice_number,
            type: getTransactionTypeName(transaction.type),
            status_name: transaction.status_name,
            statusClass: getStatusClass(transaction.status_name),
            currency: transaction.currency,

            // Payment Amount
            base_amount: formatCurrency(transaction.base_amount),
            credit_card_processing_fee: isCreditCard ? formatCurrency(transaction.credit_card_processing_fee) : 'Not Applicable',
            total_amount: formatCurrency(transaction.total_amount),
            settled_amount: formatCurrency(transaction.settled_amount),
            isCreditCard: isCreditCard,
            isACH: isACH,

            // Payment Method
            ach_account_type: formatACHAccountType(transaction.ach_account_type),
            routing_number: transaction.routing_number,
            card_brand: transaction.card_brand,
            last4: transaction.last4,
            exp_month: transaction.exp_month,
            exp_year: transaction.exp_year,

            // Customer Information
            customer_name: customer_name,
            customer_email: transaction.customer_email,
            customer_phone: transaction.customer_phone,

            // Processing Details
            hasProcessingDetails: hasProcessingDetails,
            auth_code: transaction.auth_code,
            gateway_response_code: transaction.gateway_response_code,
            gateway_response_message: transaction.gateway_response_message,
            card_response_code: transaction.card_response_code,
            card_response_message: transaction.card_response_message,
            merchant_mid: transaction.merchant_mid,
            company_name: transaction.company_name,

            // Settlement & Funding
            hasSettlementFunding: hasSettlementFunding,
            batch_id: transaction.batch_id,
            funding_id: transaction.funding_id,
            host_batch: transaction.host_batch,
            settlement_date: formatDateOnly(transaction.settlement_date),

            // Transaction Timeline (all dates)
            authorized_at: formatDate(transaction.authorized_at),
            created_at: formatDate(transaction.created_at),
            updated_at: formatDate(transaction.updated_at),
            captured_at: formatDate(transaction.captured_at),
            settled_at: formatDate(transaction.settled_at),
            funded_at: formatDate(transaction.funded_at),

            // Action History
            hasActionHistory: hasActionHistory,
            voided_at: formatDate(transaction.voided_at) || '-',
            void_reference: transaction.void_reference || '-',
            refunded_at: formatDate(transaction.refunded_at) || '-',
            refund_reference: transaction.refund_reference || '-',

            // ACH Return Information
            hasACHReturn: hasACHReturn,
            ach_return_code: transaction.ach_return_code,
            ach_return_description: achReturnDescription
        };
    }
}

module.exports = TransactionDetailsModal;
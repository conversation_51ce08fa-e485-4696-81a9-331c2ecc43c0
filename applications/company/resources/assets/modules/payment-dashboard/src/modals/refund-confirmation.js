'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;
const content_tpl = require('@cam-payment-dashboard-tpl/modals/refund-confirmation.hbs');
const Number = require('@cac-js/utils/number');

class RefundConfirmationModal extends Confirm {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.setTitle('Refund Transaction');
        this.state.transaction = null;
        this.state.promise = null;
    }

    /**
     * Open modal with transaction data
     * 
     * @param {Object} transaction - Transaction data
     * @param {Object} options - Options including promise resolver
     * @returns {Modal} - Modal instance
     */
    openWithTransaction(transaction, {promise} = {}) {
        this.state.transaction = transaction;
        this.state.promise = promise;
        
        // Format the amount for display
        const amount = Number.toCurrency(transaction.total_amount || transaction.amount);
        
        // Set the modal content with transaction details
        this.setContent(content_tpl({
            amount: amount,
            retref: transaction.retref
        }));

        return this.open();
    }

    /**
     * Handle 'yes' response - execute refund
     */
    handleYes() {
        if (!this.state.transaction) {
            this.state.promise.resolve({ success: false, error: 'No transaction data' });
            this.close();
            return;
        }

        this.startWorking();

        // Make API call to refund the transaction
        fetch(`/api/v1/payments/dashboard/transactions/${this.state.transaction.id}/refund`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(result => {
            this.resetWorking();
            if (result.success) {
                this.state.promise.resolve({ 
                    success: true, 
                    message: `Transaction ${this.state.transaction.retref} has been refunded successfully` 
                });
                this.close();
            } else {
                this.showErrorMessage(result.message || 'Failed to refund transaction');
                this.state.promise.resolve({ 
                    success: false, 
                    error: result.message || 'Failed to refund transaction' 
                });
            }
        })
        .catch(error => {
            console.error('Error refunding transaction:', error);
            this.resetWorking();
            this.showErrorMessage('Network error occurred while refunding transaction');
            this.state.promise.resolve({ 
                success: false, 
                error: 'Network error occurred while refunding transaction' 
            });
        });
    }

    /**
     * Handle 'no' response - cancel action
     */
    handleNo() {
        this.state.promise.resolve({ success: false, cancelled: true });
        this.close();
    }
}

module.exports = RefundConfirmationModal;
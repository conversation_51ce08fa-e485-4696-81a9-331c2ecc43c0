'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;
const content_tpl = require('@cam-payment-dashboard-tpl/modals/void-confirmation.hbs');
const Number = require('@cac-js/utils/number');

class VoidConfirmationModal extends Confirm {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.setTitle('Void Transaction');
        this.state.transaction = null;
        this.state.promise = null;
    }

    /**
     * Open modal with transaction data
     * 
     * @param {Object} transaction - Transaction data
     * @param {Object} options - Options including promise resolver
     * @returns {Modal} - Modal instance
     */
    openWithTransaction(transaction, {promise} = {}) {
        this.state.transaction = transaction;
        this.state.promise = promise;
        
        // Format the amount for display
        const amount = Number.toCurrency(transaction.total_amount || transaction.amount);
        
        // Set the modal content with transaction details
        this.setContent(content_tpl({
            amount: amount,
            retref: transaction.retref
        }));

        return this.open();
    }

    /**
     * Handle 'yes' response - execute void
     */
    handleYes() {
        if (!this.state.transaction) {
            this.state.promise.resolve({ success: false, error: 'No transaction data' });
            this.close();
            return;
        }

        this.startWorking();

        // Make API call to void the transaction
        fetch(`/api/v1/payments/dashboard/transactions/${this.state.transaction.id}/void`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(result => {
            this.resetWorking();
            if (result.success) {
                this.state.promise.resolve({ 
                    success: true, 
                    message: `Transaction ${this.state.transaction.retref} has been voided successfully` 
                });
                this.close();
            } else {
                this.showErrorMessage(result.message || 'Failed to void transaction');
                this.state.promise.resolve({ 
                    success: false, 
                    error: result.message || 'Failed to void transaction' 
                });
            }
        })
        .catch(error => {
            console.error('Error voiding transaction:', error);
            this.resetWorking();
            this.showErrorMessage('Network error occurred while voiding transaction');
            this.state.promise.resolve({ 
                success: false, 
                error: 'Network error occurred while voiding transaction' 
            });
        });
    }

    /**
     * Handle 'no' response - cancel action
     */
    handleNo() {
        this.state.promise.resolve({ success: false, cancelled: true });
        this.close();
    }
}

module.exports = VoidConfirmationModal;
/**
 * @module PaymentsDashboard
 */

'use strict';

import Router from '@ca-package/router';

import {MainPage} from './pages/main';

/**
 * Main controller for payments dashboard module
 *
 * @memberof module:PaymentsDashboard
 */
export class Controller {
    /**
     * PaymentsDashboard constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.state = {
            layout
        };
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.layout.setModeWindow();
        this.state.layout.setTitle('Payments');
        this.state.router = new Router(MainPage, {
            base_path: '/payments',
            main_route_callback: (instance) => {
                instance.layout = this.state.layout;
                return instance;
            }
        });
        this.state.router.boot(this.state.layout.elem.content);
    };
}
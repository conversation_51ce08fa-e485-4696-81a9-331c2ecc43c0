'use strict';

import $ from 'jquery';
const Api = require("@ca-package/api");

class mySalesmanAPI {
    static baseURL = `${window.fx_url.API}integration/mysalesman`;

    static async request(url, method, data = null) {
        try {

            const ajaxOptions = {
                url: url,
                type: method,
                contentType: 'application/json',
            };

            if (data) {
                ajaxOptions.data = JSON.stringify(data);
            }

            const response = await $.ajax(ajaxOptions);

            if (Array.isArray(response.data)) {
                return null;
            }

            return response.data;
        } catch (e) {
            console.error(e);
            throw e;
        }
    }

    static async getMySalesmanData() {
        return this.request(this.baseURL, Api.Request.Method.GET);
    }

    static async updateMySalesmanIntegration(data) {
        return this.request(`${this.baseURL}`, Api.Request.Method.PUT, data);
    }
}

export default mySalesmanAPI;

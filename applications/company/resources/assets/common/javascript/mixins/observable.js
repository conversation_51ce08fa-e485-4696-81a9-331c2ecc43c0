'use strict';

/**
 * Observer Manager class
 */
class ObserverManager {
    constructor() {
        this.state = {
            subscribers: {}
        };
    };

    /**
     * Subscribe object to action
     *
     * @param {string} action
     * @param {object} object
     * @param {string} method - method to call on object during notify
     */
    subscribe(action, object, method) {
        if (this.state.subscribers[action] === undefined) {
            this.state.subscribers[action] = new Map;
        } else if (this.state.subscribers[action].has(object)) {
            return;
        }
        this.state.subscribers[action].set(object, method);
    };

    /**
     * Unsubscribe object from action
     *
     * @param {string} action
     * @param {object} object - object which was previously subscribed
     */
    unsubscribe(action, object) {
        if (this.state.subscribers[action] === undefined) {
            return;
        }
        this.state.subscribers[action].delete(object);
    };

    /**
     * Notify subscribers of action with specified data
     *
     * @protected
     * @param {string} action
     * @param {object} data
     */
    notifySubscribers(action, data) {
        for (let [object, method] of this.state.subscribers[action].entries()) {
            object[method](data);
        }
    };

    /**
     * Notify subscribers of action, sending extra data
     *
     * @param {string} action
     * @param {object} [data={}]
     * @param {boolean} [sync=false] - determines if subscribers are notify synchronously
     */
    notify(action, data = {}, sync = false) {
        if (this.state.subscribers[action] === undefined || this.state.subscribers[action].size === 0) {
            return;
        }
        if (sync) {
            this.notifySubscribers(action, data);
            return;
        }
        setTimeout(() => this.notifySubscribers(action, data), 0);
    };
}

/**
 * @mixin
 */
const Observable = {
    /**
     * Subscribe object to action
     *
     * @param {string} action
     * @param {object} object
     * @param {string} method - method to call on object during notify
     * @returns {this}
     */
    subscribe(action, object, method) {
        this.observer_manager.subscribe(action, object, method);
        return this;
    },

    /**
     * Unsubscribe object from action
     *
     * @param {string} action
     * @param {object} object
     * @returns {this}
     */
    unsubscribe(action, object) {
        this.observer_manager.unsubscribe(action, object);
        return this;
    },

    /**
     * Notify subscribers of event with data
     *
     * @param {string} action
     * @param {object} [data={}]
     * @param {boolean} [sync=false]
     */
    notify(action, data = {}, sync = false) {
        if (this.__observer_manager === undefined) {
            return;
        }
        this.observer_manager.notify(action, data, sync);
    },

    /**
     * Unsubscribe all observers by recreating manager
     */
    unsubscribeAll() {
        this.__observer_manager = undefined;
    }
};

module.exports = (object) => {
    Object.defineProperty(object.prototype, 'observer_manager', {
        get: function () {
            if (this.__observer_manager === undefined) {
                this.__observer_manager = new ObserverManager();
            }
            return this.__observer_manager;
        }
    });
    return Object.assign(object.prototype, Observable);
};

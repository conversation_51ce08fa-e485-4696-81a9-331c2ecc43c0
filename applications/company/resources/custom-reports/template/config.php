<?php

return [
    'columns' => [
        'columnName' => [
            'title' => '', // name of column in CSV and table, required
            'type' => '' // value type, optional, allowed values: date, datetime, currency, number, link
        ],
        [
            'title' => '',
            'type' => 'link', // display as link in table (no affect on CSV)
            'value' => fn(array $data): string => "https://test.com?param={$data['columnName']}", // value override closure, receives the entire row as array
            'link_title' => 'View' // name of link to display in table (no affect on CSV)
        ]
    ],
    'vars' => [], // optional
    'inputs' => [ // optional
        'field_name' => [
            'label' => '', // label for field in form
            'rules' => '', // server side validation rules
            'type' => '' // type of data, allowed values: date
        ]
    ]
];

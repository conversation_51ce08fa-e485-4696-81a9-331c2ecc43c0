{"config": {"configFile": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/playwright.config.js", "rootDir": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/config/global-setup.js", "globalTeardown": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/config/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "results/html-report"}], ["json", {"outputFile": "results/test-results.json"}], ["junit", {"outputFile": "results/junit.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/results/test-artifacts", "repeatEach": 1, "retries": 1, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 180000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 1, "webServer": null}, "suites": [{"title": "payment-tests-generated.spec.js", "file": "payment-tests-generated.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Payment Form E2E Tests - All 178 Cases", "file": "payment-tests-generated.spec.js", "line": 75, "column": 6, "specs": [{"title": "Test Case 1 - AMEX - <PERSON><PERSON>", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 10940, "errors": [], "stdout": [{"text": "🚀 Initializing Payment Form E2E Tests with Rate Limiting...\n"}, {"text": "📊 Loaded 178 test cases\n"}, {"text": "🚀 Starting payment form test run at 2025-09-08 10:37:46\n"}, {"text": "🎯 Test URL: http://app.contractoraccelerator.test/payments/Q1XPWMdQxtWGZY6lw4rpfwTTv9NXQv9Z\n"}, {"text": "⏱️ Rate Limit: 15 requests/minute\n"}, {"text": "📸 Screenshots: Success -> results/screenshots/success\n"}, {"text": "📸 Screenshots: Failed -> results/screenshots/failed\n"}, {"text": "⏰ Estimated completion time: ~11m 52s\n"}, {"text": "\n🧪 Running Test Case 1\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 100 - <PERSON><PERSON>\n"}, {"text": "📝 Filling form for test case 1\n"}, {"text": "✅ Form filled successfully for test case 1\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 1 | Rate: 12.0/min | Elapsed: 5s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_1_AMEX_2025-09-08T13-37-51-805Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_1_AMEX_2025-09-08T13-37-54-940Z_after_submission.png\n"}, {"text": "✅ Test Case 1 (AMEX): PASSED\n"}, {"text": "✅ Test Case 1 completed successfully in 6391ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:37:46.049Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-6c6a71021853afea2624", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 2 - AMEX - Expired card / Invalid Expiration Date", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9589, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 2\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 101 - Expired card / Invalid Expiration Date\n"}, {"text": "📝 Filling form for test case 2\n"}, {"text": "✅ Form filled successfully for test case 2\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 2 | Rate: 8.0/min | Elapsed: 15s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_2_AMEX_2025-09-08T13-38-01-401Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_2_AMEX_2025-09-08T13-38-04-523Z_after_submission.png\n"}, {"text": "✅ Test Case 2 (AMEX): PASSED\n"}, {"text": "✅ Test Case 2 completed successfully in 5257ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:37:59.102Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-eb8c8d41ead5f46b57fb", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 3 - AMEX - CID failed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9639, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 3\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 103 - CID failed\n"}, {"text": "📝 Filling form for test case 3\n"}, {"text": "✅ Form filled successfully for test case 3\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 3 | Rate: 7.2/min | Elapsed: 25s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_3_AMEX_2025-09-08T13-38-11-044Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_3_AMEX_2025-09-08T13-38-14-176Z_after_submission.png\n"}, {"text": "✅ Test Case 3 (AMEX): PASSED\n"}, {"text": "✅ Test Case 3 completed successfully in 5300ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:38:08.700Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-7f253e46086c7cc00ef7", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 4 - AMEX - Card cancelled", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9880, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 4\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 105 - Card cancelled\n"}, {"text": "📝 Filling form for test case 4\n"}, {"text": "✅ Form filled successfully for test case 4\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 4 | Rate: 7.1/min | Elapsed: 34s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_4_AMEX_2025-09-08T13-38-20-963Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_4_AMEX_2025-09-08T13-38-24-077Z_after_submission.png\n"}, {"text": "✅ Test Case 4 (AMEX): PASSED\n"}, {"text": "✅ Test Case 4 completed successfully in 5530ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:38:18.345Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-d44e300372e8f1bb67cb", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 5 - AMEX - Exceeded PIN attempts", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9497, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 5\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 106 - Exceeded PIN attempts\n"}, {"text": "📝 Filling form for test case 5\n"}, {"text": "✅ Form filled successfully for test case 5\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 5 | Rate: 6.8/min | Elapsed: 44s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_5_AMEX_2025-09-08T13-38-30-472Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_5_AMEX_2025-09-08T13-38-33-601Z_after_submission.png\n"}, {"text": "✅ Test Case 5 (AMEX): PASSED\n"}, {"text": "✅ Test Case 5 completed successfully in 5175ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:38:28.236Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-3728b97a2938b87c29b6", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 6 - AMEX - Call issuer", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9450, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 6\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 107 - Call issuer\n"}, {"text": "📝 Filling form for test case 6\n"}, {"text": "✅ Form filled successfully for test case 6\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 6 | Rate: 6.8/min | Elapsed: 53s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_6_AMEX_2025-09-08T13-38-39-901Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_6_AMEX_2025-09-08T13-38-43-023Z_after_submission.png\n"}, {"text": "✅ Test Case 6 (AMEX): PASSED\n"}, {"text": "✅ Test Case 6 completed successfully in 5113ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:38:37.743Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-cde9e5945ec465a4e173", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 7 - AMEX - Invalid merchant", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9692, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 7\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 109 - Invalid merchant\n"}, {"text": "📝 Filling form for test case 7\n"}, {"text": "✅ Form filled successfully for test case 7\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 7 | Rate: 6.7/min | Elapsed: 63s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_7_AMEX_2025-09-08T13-38-49-610Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_7_AMEX_2025-09-08T13-38-52-736Z_after_submission.png\n"}, {"text": "✅ Test Case 7 (AMEX): PASSED\n"}, {"text": "✅ Test Case 7 completed successfully in 5328ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:38:47.200Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-6b9ebecd818fcb933ce6", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 8 - AMEX - Invalid amount", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9582, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 8\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 110 - Invalid amount\n"}, {"text": "📝 Filling form for test case 8\n"}, {"text": "✅ Form filled successfully for test case 8\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 8 | Rate: 6.6/min | Elapsed: 73s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_8_AMEX_2025-09-08T13-38-59-133Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_8_AMEX_2025-09-08T13-39-02-240Z_after_submission.png\n"}, {"text": "✅ Test Case 8 (AMEX): PASSED\n"}, {"text": "✅ Test Case 8 completed successfully in 5161ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:38:56.903Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-f8eb1e911ede2370ad96", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 9 - AMEX - Invalid card / Invalid MICR (Travelers Cheque)", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9524, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 9\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 111 - Invalid card / Invalid MICR (Travelers Cheque)\n"}, {"text": "📝 Filling form for test case 9\n"}, {"text": "✅ Form filled successfully for test case 9\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 9 | Rate: 6.6/min | Elapsed: 82s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_9_AMEX_2025-09-08T13-39-08-761Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_9_AMEX_2025-09-08T13-39-11-872Z_after_submission.png\n"}, {"text": "✅ Test Case 9 (AMEX): PASSED\n"}, {"text": "✅ Test Case 9 completed successfully in 5175ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:39:06.498Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-b20b2f9d1d0266cb14ed", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 10 - AMEX - Function not supported", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9533, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 10\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 115 - Function not supported\n"}, {"text": "📝 Filling form for test case 10\n"}, {"text": "✅ Form filled successfully for test case 10\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 10 | Rate: 6.5/min | Elapsed: 92s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_10_AMEX_2025-09-08T13-39-18-294Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_10_AMEX_2025-09-08T13-39-21-434Z_after_submission.png\n"}, {"text": "✅ Test Case 10 (AMEX): PASSED\n"}, {"text": "✅ Test Case 10 completed successfully in 5199ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:39:16.039Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-a09304357597fdcbe37b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 11 - AMEX - Insufficient funds", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9417, "errors": [], "stdout": [{"text": "\n🧪 Running Test Case 11\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 116 - Insufficient funds\n"}, {"text": "📝 Filling form for test case 11\n"}, {"text": "✅ Form filled successfully for test case 11\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 11 | Rate: 6.5/min | Elapsed: 101s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_11_AMEX_2025-09-08T13-39-27-725Z_before_submission.png\n"}, {"text": "✅ Form submitted successfully\n"}, {"text": "📊 Extracting response data...\n"}, {"text": "📸 Screenshot saved: results/screenshots/failed/test_case_11_AMEX_2025-09-08T13-39-30-848Z_after_submission.png\n"}, {"text": "✅ Test Case 11 (AMEX): PASSED\n"}, {"text": "✅ Test Case 11 completed successfully in 5092ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-08T13:39:25.579Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5619ef2447015a9a5779-9e85d084c5865e9678e1", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 12 - AMEX - Invalid PIN", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "interrupted", "duration": 2742, "error": {"message": "Error: page.waitForTimeout: Test ended.", "stack": "Error: page.waitForTimeout: Test ended.\n    at PaymentPage.submitForm (/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/pages/PaymentPage.js:304:23)\n    at /Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/tests/payment-tests-generated.spec.js:136:9", "location": {"file": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/pages/PaymentPage.js", "column": 23, "line": 304}, "snippet": "\u001b[90m   at \u001b[39m../pages/PaymentPage.js:304\n\n\u001b[0m \u001b[90m 302 |\u001b[39m\n \u001b[90m 303 |\u001b[39m       \u001b[90m// Reduced response processing wait from 3000ms to 1000ms\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 304 |\u001b[39m       \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 305 |\u001b[39m\n \u001b[90m 306 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Form submitted successfully'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 307 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/pages/PaymentPage.js", "column": 23, "line": 304}, "message": "Error: page.waitForTimeout: Test ended.\n\n\u001b[90m   at \u001b[39m../pages/PaymentPage.js:304\n\n\u001b[0m \u001b[90m 302 |\u001b[39m\n \u001b[90m 303 |\u001b[39m       \u001b[90m// Reduced response processing wait from 3000ms to 1000ms\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 304 |\u001b[39m       \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 305 |\u001b[39m\n \u001b[90m 306 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Form submitted successfully'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 307 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\u001b[0m\n\u001b[2m    at PaymentPage.submitForm (/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/pages/PaymentPage.js:304:23)\u001b[22m\n\u001b[2m    at /Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/tests/payment-tests-generated.spec.js:136:9\u001b[22m"}], "stdout": [{"text": "\n🧪 Running Test Case 12\n"}, {"text": "   Card Brand: AMEX\n"}, {"text": "   Expected Response: 117 - Invalid PIN\n"}, {"text": "📝 Filling form for test case 12\n"}, {"text": "✅ Form filled successfully for test case 12\n"}, {"text": "🚀 Submitting payment form...\n"}, {"text": "📊 Request 12 | Rate: 6.5/min | Elapsed: 111s\n"}, {"text": "📸 Screenshot saved: results/screenshots/success/test_case_12_AMEX_2025-09-08T13-39-37-241Z_before_submission.png\n"}, {"text": "🏁 Test run completed at 2025-09-08 10:39:37\n"}, {"text": "⏱️ Total duration: 1m 51s\n"}, {"text": "\n📊 TEST SUMMARY\n"}, {"text": "================\n"}, {"text": "Total Tests: 11\n"}, {"text": "✅ Passed: 11\n"}, {"text": "❌ Failed: 0\n"}, {"text": "⏭️ Skipped: 0\n"}, {"text": "⏱️ Duration: 1m 51s\n"}, {"text": "📈 Success Rate: 100.0%\n"}, {"text": "📊 Generating test reports...\n"}, {"text": "❌ Test Case 12 (AMEX): FAILED\n"}, {"text": "   Error: page.waitForTimeout: Test ended.\n"}, {"text": "📊 CSV report generated: /Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/results/reports/payment-test-results.csv\n"}, {"text": "📊 JSON report generated: /Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/results/reports/payment-test-results.json\n"}, {"text": "📊 HTML report generated: /Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/results/reports/payment-test-report.html\n"}, {"text": "✅ All reports generated successfully\n"}, {"text": "\n📈 Rate Limiter Status:\n"}, {"text": "   Total Requests: 12\n"}, {"text": "   Current Rate: 6.4/min (limit: 15/min)\n"}, {"text": "   Recent Requests: 7/15\n"}, {"text": "   Elapsed Time: 111s\n"}, {"text": "\n📸 Screenshot Statistics:\n"}, {"text": "   Success Screenshots: 449\n"}, {"text": "   Failed Screenshots: 845\n"}, {"text": "   Total Screenshots: 1294\n"}, {"text": "   Success Directory: results/screenshots/success\n"}, {"text": "   Failed Directory: results/screenshots/failed\n"}], "stderr": [{"text": "❌ Error submitting form: page.waitForTimeout: Test ended.\n"}, {"text": "❌ Failed to take screenshot: page.screenshot: Target page, context or browser has been closed\n"}, {"text": "❌ Test Case 12 failed: page.waitForTimeout: Test ended.\n"}, {"text": "❌ Failed to take screenshot: page.screenshot: Target page, context or browser has been closed\n"}], "retry": 0, "startTime": "2025-09-08T13:39:35.006Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/results/test-artifacts/payment-tests-generated-Pa-a480d-ase-12---AMEX---Invalid-PIN-chromium/test-failed-1.png"}], "errorLocation": {"file": "/Users/<USER>/Dev/PhpstormProjects/CA/site-foundation-accelerator/applications/company/tests/e2e/payments/pages/PaymentPage.js", "column": 23, "line": 304}}], "status": "skipped"}], "id": "5619ef2447015a9a5779-60da9fc3339fe91db1fa", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 13 - AMEX - Cardmember not enrolled / not permitted", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-d62046cf31431224bb0e", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 14 - AMEX - Limit exceeded", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-0b5ef6e3aed391d92f3c", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 15 - AMEX - Invalid CID", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-3d222bee0a238987243b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 16 - AMEX - Invalid effective date", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-b84a7582d8bc0b5e5e0d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 17 - AMEX - Additional customer identification required", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-fa628e163ca424aadae2", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 18 - AMEX - Format error", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-0318a426950acd426687", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 19 - AMEX - Invalid currency code", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-108c6a4f81480bb9aeef", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 20 - AMEX - Deny - new card issued", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-dd6aadf2e2c2c40cf2eb", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 21 - AMEX - <PERSON><PERSON> - canceled", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-1823d0864d25bf869f1e", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 22 - AMEX - Deny - Canceled or Closed Merchant/SE", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4f1384b76ee9470c9946", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 23 - AMEX - National ID Mismatch", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-ad643e732332dc09e7a7", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 24 - AMEX - Invalid Country Code", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-76e82151b3dc77f708d0", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 25 - AMEX - Invalid Region Code", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-99929fda13f4705ded7f", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 26 - AMEX - Pick up card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-2acab7238a86409b1797", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 27 - AMEX - Accepted - ATC Synchronization", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-8dc884a58041049534c5", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 28 - AMEX - System Malfunction (Cryptographic error)", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4d3ee9d878429956c9ae", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 29 - AMEX - Issuer not available", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7659cfb59480dbfea00c", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 30 - AMEX - Invalid Payment Plan", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f015600aa840f6b48f21", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 31 - AMEX - Invalid Payment Times", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-46455b36044afe7d6952", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 32 - DISCOVER - Invalid Merchant", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f68bdc09c4fe2987a1d2", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 33 - DISCOVER - Capture Card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-2d5856909cda6ac9fa28", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 34 - DISCOVER - Do not honor", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-184f00287b8d96724033", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 35 - DISCOVER - Pick-up Card special condition", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-a416cc6471c27600ae8d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 36 - DISCOVER - Reserved for future USE", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-1e2e2269ef3be387e590", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 37 - DISCOVER - Invalid transaction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-15f58156c2ff29846a08", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 38 - DISCOVER - Invalid amount", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-444ce9b498df3af4c0ab", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 39 - DISCOVER - Invalid Card Number", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-415f3e541e2f6ca05b8e", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 40 - DISCOVER - Reserved for future USE", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-5ab399d5d971fe64a6bf", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 41 - DISCOVER - Re-enter transaction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-a2087c49aa5679245171", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 42 - DISCOVER - Format error", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-d351ea85ce63ab249df0", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 43 - DISCOVER - Bank not supported by switch", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-61323ebda630a5849e46", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 44 - DISCOVER - Allowable PIN tries exceeded", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-0c1866f4d4b145909741", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 45 - DISCOVER - No credit Account", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6c734365f8ce9b1f227a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 46 - DISCOVER - Requested function not supported", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-c0f07a3be8a544502971", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 47 - DISCOVER - Lost Card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-d3d843b90a16c88a6d74", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 48 - DISCOVER - Stolen Card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-861a0aaeb955f4cf143b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 49 - DISCOVER - Decline", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4b455e72000c2b36c8a7", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 50 - DISCOVER - No savings Account", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-678952681cb4f15d4d0b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 51 - DISCOVER - Expired Card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-81a7d7c9eb8566c64ef8", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 52 - DISCOVER - Invalid PIN", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-a5aaa3e676f66e593c25", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 53 - DISCOVER - No Card record", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-0a131fc62575fa059053", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 54 - DISCOVER - Transaction not permitted to Issuer/Cardholder", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-122e9185e7cb0768065b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 55 - DISCOVER - Transaction not permitted to Acquirer/terminal", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7624cd4533b64ef4f18a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 56 - DISCOVER - Suspected fraud", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6aaa237b01323742065c", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 57 - DISCOVER - Card acceptor contact Acquirer", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7e9593ac21d779fbeede", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 58 - DISCOVER - Exceeds withdrawal amount limit", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-b9b3dbdee4cd81fb67b3", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 59 - DISCOVER - Restricted Card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-5b540b5238e93fbe85bd", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 60 - DISCOVER - Security violation", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-b4fd075344bcb35fd654", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 61 - DISCOVER - Original amount incorrect", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-e7f676263887bced5550", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 62 - DISCOVER - Exceeds withdrawal count limit", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f5c0f9039abb4bc2467f", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 63 - DISCOVER - Card Acceptor call Acquirer's security dept", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-86e36ae9f0f231701309", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 64 - DISCOVER - Hard capture (requires ATM pick-up)", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-481bf57d99e724a44848", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 65 - DISCOVER - Response received too late", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-a12ee9a1e9d85b531fea", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 66 - DISCOVER - Allowable number of PIN tries exceeded", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6d4a04a0327943369eec", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 67 - DISCOVER - \"Invalid/nonexistent \"\"to\"\" Account specified\"", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f35e9d3b6f9887c47d0b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 68 - DISCOVER - \"Invalid/nonexistent \"\"from\"\" Account specified\"", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-2f471795c233acae6751", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 69 - DISCOVER - Invalid/nonexistent Account specified (general)", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-9d7a44ebc3c116b8bf38", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 70 - DISCOVER - Domain Restriction Controls Failure", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-bee35649e3b9283cc5ef", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 71 - DISCOVER - No reason to decline", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-e42ce36f8775c6f17d27", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 72 - DISCOVER - Network unavailable", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-c46ed24f16a11835e548", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 73 - DISCOVER - Authorization system or Issuer system inoperative", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-550d044db6fe32bbd51d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 74 - DISCOVER - Unable to route transaction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-060040058a8c171e4128", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 75 - DISCOVER - Transaction cannot be completed violation of law", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-1d58dd0b45fcbde4cac0", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 76 - DISCOVER - Duplicate transmission detected", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-13826ae07ef502284a5a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 77 - DISCOVER - System malfunction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-93bb4e34f767607fd098", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 78 - DISCOVER - Customer Authentication Required", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f77fb7181861560455b9", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 79 - DISCOVER - System up", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-351cc96c6863062a70fc", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 80 - DISCOVER - Soft down", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-1df2ff8178027590af1f", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 81 - DISCOVER - System down", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-3ea3aad1b9402a8119d4", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 82 - DISCOVER - Decline for AVS or CID mismatch", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-cb0f23a05eb9e4394802", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 83 - DISCOVER - PIN Change/Unblock failed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-a2213aa9d89e5c00d7ad", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 84 - DISCOVER - New PIN not accepted", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-d1a0e484b1652fb9664c", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 85 - MASTERCARD - Refer to card issuer", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-3e5e96ad737e728e07d3", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 86 - MAST<PERSON><PERSON>RD - Invalid merchant", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-9d279be27baaae66c8d0", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 87 - MASTERCARD - Do not honor", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7b7fa57b2eebb454c387", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 88 - MASTERCARD - Invalid transaction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-b5df52825dfa22ad0b23", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 89 - MASTERCARD - Invalid amount", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-bfcaf2da7c055030d72c", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 90 - MASTERCARD - Invalid card number", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4975241fec10bc31c344", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 91 - MASTERCARD - Invalid issuer", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-07a96281360473c69d37", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 92 - MASTERCARD - Format error", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-00bd851432210934520a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 93 - MASTERCARD - Insufficient funds / over credit limit", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7647e3011236f580822d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 94 - MASTERCARD - Wrong expiration", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f0ace942bd56b23674ea", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 95 - MASTERCARD - Incorrect pin", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f123305f0074a8c95fd3", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 96 - MASTERCARD - Invalid txn for card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-d39c3f6cb53e3a118ecd", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 97 - MASTERCARD - Txn not permitted to cardholder", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-19f446f07fbab191ca4c", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 98 - MASTERCARD - Exceeds withdrawal limit", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-9bfcff158b44fb791619", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 99 - MASTERCARD - Restricted card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-abdb1862d7ecfba5db43", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 100 - MASTERCARD - Security violation", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-43addf674c17c01c714b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 101 - MASTERCARD - Exceeds withdrawal frequency", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-a21dc6745759bae35551", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 102 - MASTERCARD - PIN Not Changed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4ac62c37c990677d9e07", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 103 - MASTERCARD - <PERSON><PERSON> try exceeded", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-3621c48a18a6116e42df", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 104 - MASTERCARD - Invalid To Account specified", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-ef362cd18a25133bdffb", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 105 - MASTERCARD - Invalid From Account specified", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-fdd2b28669443b183beb", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 106 - MASTERCARD - Invalid Account specified", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f9b754369bc1a94ac91a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 107 - MASTERCARD - Domestic Debit Transaction not allowed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-ad561e473a22e4dd98ed", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 108 - MASTERCARD - Invalid Authorization Life Cycle", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-f2866965c5627adb9924", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 109 - MASTERCARD - PIN Validation not possible", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-dc62f0a9ce3206fed2db", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 110 - MASTERCARD - Cryptographic Failure", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-26d2770bb7ffa205dd1d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 111 - MASTERCARD - Authentication Failure", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6d9d3d6a016d72ea20a6", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 112 - MASTERCARD - Issuer or Switch Inoperative", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-18b92514cf9a972c33a7", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 113 - MASTERCARD - Unable to route transaction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-a84b83abfcf31c0252cb", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 114 - MASTERCARD - Duplicate Transmission detected", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-8e9ac2ab29104d136fca", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 115 - MASTERCARD - System error", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6396aefb9f3e1698cd54", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 116 - VISA - Refer to card issuer", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-2f8508b23f79b1de43c4", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 117 - VISA - Refer to card issuer - special", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-3bb267d082bf601c8185", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 118 - VISA - Invalid merchant", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-edcab919fdfd64ad1f9c", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 119 - VISA - Pick up card (no fraud)", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6668b4c192d9a69cf947", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 120 - VISA - Do not honor", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-33b5904c0c92f86dc04a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 121 - VISA - <PERSON>rror", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-feafe2cbcc5ce2a95988", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 122 - VISA - Pick up card - special", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-1f8f6b7afaa697317384", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 123 - VISA - Invalid transaction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-028ade5e69e198cc7e7d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 124 - VISA - Invalid amount", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4b1b001b8ece0a1f894b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 125 - VISA - Invalid account number", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-b8be90b08f2066045856", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 126 - VISA - No such issuer", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-dfe5d3aeb8993af5d9a5", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 127 - VISA - Unable to locate record in file", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-bd34928aadea0d56d71a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 128 - VISA - File temporarily unavailable", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-357eb606c155a5f8f613", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 129 - VISA - No credit account", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-02ccaed4be2ba2fb9a95", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 130 - VISA - Card reported lost", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-21f6b5ac51380d6d0a47", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 131 - VISA - Card reported stolen", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-45080cf2a31a33dcf24b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 132 - VISA - Account closed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-48978b9648cb0222de3a", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 133 - VISA - Insufficient funds", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-fc550f2227e27633090f", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 134 - VISA - No checking account", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-2851e34588b9a41bfac5", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 135 - VISA - No savings account", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-ebc742e6286934cfb4ec", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 136 - VISA - Wrong expiration", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-950c295ff187892de3fc", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 137 - VISA - Incorrect PIN", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-da247ca19e0934f338a6", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 138 - VISA - Invalid txn for card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-2ad5dd32493f9335d724", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 139 - VISA - Terminal not permitted", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-70e66a7a105ee0dea43d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 140 - VISA - Suspected fraud", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-5cce15d9f505b09a53f2", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 141 - VISA - Exceeds approval amount limit", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-e9c314d001b09cf68c02", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 142 - VISA - Restricted card", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-8ea6c800d592b907f684", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 143 - VISA - Security violation", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-fe038f7d370aa698a8b3", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 144 - VISA - Transaction does not fulfill AML requirement", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7e3a026a5d4136ea72a3", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 145 - VISA - Exceeds withdrawal frequency limit", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-ae615a810a1fd204034b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 146 - VISA - PIN data required", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-b169966b2f75e19d3ce3", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 147 - VISA - Different value than that used for PIN encryption errors", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-fa4714a1f57544646ecf", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 148 - VISA - <PERSON><PERSON> try exceeded", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6024e8cb2fadc26b2f57", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 149 - VISA - Unsolicited reversal", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7ae7f33ab779ddff206d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 150 - VISA - Blocked - first used", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-011e49633d4d90d7d9ff", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 151 - VISA - Already reversed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-398a307b16d8dc999c63", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 152 - VISA - No financial impact", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-520631120e9f1adce663", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 153 - VISA - Cryptographic error found in PIN", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-36bd7ee4f3e41507cba4", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 154 - VISA - Negative CAM dCVV iCVV or CVV results", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-d2768af56ebe7a38525d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 155 - VISA - Cannot verify PIN", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6dd611f4a9b66cf46c1d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 156 - VISA - Ineligible to receive financial position information (GIV)", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-44de912255facad5e785", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 157 - VISA - Issuer unavailable", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-33fe6788f024fe6d143f", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 158 - VISA - Unable to route", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-dfe955106ec6d711f19b", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 159 - VISA - Transaction cannot be completed - violation of law", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-5fa939a8981188ecb721", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 160 - VISA - System malfunction", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-653de7081d7c3c2e0ced", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 161 - VISA - Additional customer authentication required", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-8d21306249eba3e8d227", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 162 - VISA - Verification data failed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-7de9b4c6e911f523eaf6", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 163 - VISA - Surcharge amount not permitted (U.S. acquirers only)", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-0029417bfae791109dbe", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 164 - VISA - Surcharge amount not supported by debit network issuer", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-75cad029143249c0c7c2", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 165 - VISA - Force STIP", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-edc8201375f8543a38d2", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 166 - VISA - Cash service not available", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-534a8fa1157596cd6047", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 167 - VISA - Cash request exceeds issuer or approved limit", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-e1051a16ce0ad0f543b6", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 168 - VISA - Ineligible for resubmission", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-9a5c5634d848106c6d62", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 169 - VISA - CVV2 failure", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-e426e297338d4a312dc5", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 170 - VISA - Transaction amount exceeded", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-6ed027418b8fabf76d2d", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 171 - VISA - <PERSON><PERSON> unblock", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4d91521793b12c4698d9", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 172 - VISA - Denied <PERSON> change-requested PIN unsafe", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-32c247b67cf8a5de12a3", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 173 - VISA - Card Authentication failed", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-63ab8a9c7813b11e5ffb", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 174 - VISA - Stop Payment Order", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-4e50777f15844d79ea99", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 175 - VISA - Revocation of authorization order", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-e4073b92c8299702dae8", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 176 - VISA - Transaction does not qualify for Visa PIN", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-b10cbbb292a71f7736a7", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 177 - VISA - Revocation of all authorizations order", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-64f7e12f5555b9efc155", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}, {"title": "Test Case 178 - VISA - offline-declined", "ok": true, "tags": [], "tests": [{"timeout": 180000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5619ef2447015a9a5779-aeb01a9844abefc9e376", "file": "payment-tests-generated.spec.js", "line": 115, "column": 5}]}]}], "errors": [], "stats": {"startTime": "2025-09-08T13:37:45.649Z", "duration": 112413.472, "expected": 11, "skipped": 167, "unexpected": 0, "flaky": 0}}
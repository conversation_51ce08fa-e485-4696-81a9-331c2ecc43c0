{"name": "fxlratr/company", "description": "Foundation Accelerator Company Application", "type": "project", "repositories": [{"type": "path", "url": "../../packages/*"}], "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.0 <8.2", "league/csv": "^9.6", "ramsey/uuid": "^4.0", "phpmailer/phpmailer": "^6.1", "intervention/image": "^2.5", "spatie/pdf-to-image": "^2.0", "zordius/lightncandy": "dev-master", "brick/math": "^0.9", "moneyphp/money": "^4.0", "ezyang/htmlpurifier": "^4.13", "html2text/html2text": "^4.3", "pelago/emogrifier": "^5.0", "authorizenet/authorizenet": "^2.0", "drewm/mailchimp-api": "^2.5", "twilio/sdk": "^6.2", "eluceo/ical": "^0.16.0", "quickbooks/v3-php-sdk": "^6.2", "mikehaertl/phpwkhtmltopdf": "^2.4", "mikehaertl/php-pdftk": "^0.10.0", "robmorgan/phinx": "^0.12.1", "dompdf/dompdf": "^1.0", "knplabs/knp-snappy": "^1.2", "lasserafn/php-initial-avatar-generator": "^4.1", "cxlratr/common": "^0.1.0", "google/apiclient": "^2.18", "cxlratr/core": "^0.1.0", "bjeavons/zxcvbn-php": "^1.2", "hubspot/api-client": "^13.0", "fakerphp/faker": "^1.16", "zendesk/zendesk_api_client_php": "^2.2", "guzzlehttp/guzzle": "^7.9", "sentry/sdk": "^4.0", "symfony/cache": "^5.4", "nikolaposa/rate-limit": "^3.0", "nikolaposa/rate-limit-middleware": "^2.0"}, "autoload": {"psr-4": {"App\\": "app"}}, "scripts": {"post-install-cmd": ["Google_Task_Composer::cleanup"], "post-update-cmd": "Google_Task_Composer::cleanup"}, "extra": {"google/apiclient-services": ["Calendar"]}}